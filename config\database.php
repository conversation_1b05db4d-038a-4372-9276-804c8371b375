<?php
/**
 * Database Configuration for Recite! App
 * Contains database connection settings and helper functions
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'recite_app');  // Using consistent database name
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Paystack Configuration
define('PAYSTACK_PUBLIC_KEY', 'pk_test_79ab46bc7ad4029705e3593e00dd2feb7c7cdb77');
define('PAYSTACK_SECRET_KEY', 'sk_test_79ab46bc7ad4029705e3593e00dd2feb7c7cdb77'); // Test secret key

// Application Configuration
define('APP_NAME', 'Recite!');
define('BASE_URL', 'http://localhost/RECITE_appbac/RECITE_app/');
define('SITE_URL', 'http://localhost/RECITE_appbac/RECITE_app/'); // Legacy compatibility
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('REGISTRATION_FEE', 1000);
define('ADMIN_PASSWORD', '1@3Usazladan');
define('DELETE_PASSWORD', '1!3usazladan');
define('DEVELOPMENT_MODE', true); // Set to false in production

// Session Configuration
ini_set('session.cookie_lifetime', 86400); // 24 hours
ini_set('session.gc_maxlifetime', 86400);

/**
 * Get database connection
 * @return mysqli Database connection object
 * @throws Exception If connection fails
 */
function getConnection() {
    static $connection = null;
    
    if ($connection === null) {
        try {
            $connection = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            
            if ($connection->connect_error) {
                throw new Exception("Connection failed: " . $connection->connect_error);
            }
            
            $connection->set_charset(DB_CHARSET);
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    return $connection;
}

/**
 * Execute a prepared statement safely
 * @param string $query SQL query with placeholders
 * @param string $types Parameter types (s, i, d, b)
 * @param array $params Parameters array
 * @return mysqli_result|bool Query result
 */
function executeQuery($query, $types = '', $params = []) {
    $conn = getConnection();
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $result = $stmt->execute();
    
    if (!$result) {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    return $stmt->get_result() ?: $result;
}

/**
 * Sanitize user input
 * @param string $data Input data
 * @return string Sanitized data
 */
function sanitize($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token
 * @return string CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 * @param string $token Token to validate
 * @return bool True if valid
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if user is logged in
 * @return bool True if logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Require user to be logged in
 * @param string $redirect Redirect URL if not logged in
 */
function requireLogin($redirect = '/login.php') {
    if (!isLoggedIn()) {
        header('Location: ' . $redirect);
        exit;
    }
}

/**
 * Get user by ID
 * @param int $userId User ID
 * @return array|null User data or null
 */
function getUserById($userId) {
    try {
        $result = executeQuery(
            "SELECT * FROM users WHERE id = ? AND is_active = 1 AND is_blocked = 0",
            'i',
            [$userId]
        );
        
        return $result->fetch_assoc();
    } catch (Exception $e) {
        error_log("Error getting user: " . $e->getMessage());
        return null;
    }
}

/**
 * Update user wallet balance
 * @param int $userId User ID
 * @param float $amount Amount to add/subtract
 * @param string $description Transaction description
 * @param string $type Transaction type
 * @return bool Success status
 */
function updateWallet($userId, $amount, $description, $type = 'payment') {
    try {
        $conn = getConnection();
        $conn->autocommit(false);
        
        // Update wallet balance
        executeQuery(
            "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
            'di',
            [$amount, $userId]
        );
        
        // Log transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, ?, ?, ?, 'completed')",
            'isds',
            [$userId, $type, $amount, $description]
        );
        
        $conn->commit();
        $conn->autocommit(true);
        
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        error_log("Wallet update error: " . $e->getMessage());
        return false;
    }
}

/**
 * Update user points balance
 * @param int $userId User ID
 * @param int $points Points to add/subtract
 * @param string $description Transaction description
 * @return bool Success status
 */
function updatePoints($userId, $points, $description) {
    try {
        $conn = getConnection();
        $conn->autocommit(false);
        
        // Update points balance
        executeQuery(
            "UPDATE users SET points_balance = points_balance + ? WHERE id = ?",
            'ii',
            [$points, $userId]
        );
        
        // Log transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'bonus', ?, ?, 'completed')",
            'iis',
            [$userId, $points, $description]
        );
        
        $conn->commit();
        $conn->autocommit(true);
        
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        error_log("Points update error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate unique referral code
 * @param int $userId User ID
 * @return string Referral code
 */
function generateReferralCode($userId) {
    return 'REC' . str_pad($userId, 6, '0', STR_PAD_LEFT) . strtoupper(substr(md5($userId . time()), 0, 4));
}

/**
 * Log admin action
 * @param string $username Admin username
 * @param string $action Action performed
 * @param int $targetUserId Target user ID (optional)
 * @param string $details Additional details
 */
function logAdminAction($username, $action, $targetUserId = null, $details = '') {
    try {
        executeQuery(
            "INSERT INTO admin_logs (admin_username, action, target_user_id, details, ip_address) VALUES (?, ?, ?, ?, ?)",
            'ssiss',
            [$username, $action, $targetUserId, $details, $_SERVER['REMOTE_ADDR']]
        );
    } catch (Exception $e) {
        error_log("Admin log error: " . $e->getMessage());
    }
}

/**
 * Format time ago
 * @param string $datetime DateTime string
 * @return string Formatted time ago
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    
    return date('M j, Y', strtotime($datetime));
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?> 