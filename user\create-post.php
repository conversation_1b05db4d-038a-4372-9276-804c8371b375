<?php
require_once '../config.php';
requireLogin();

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    header('Location: ../login.php');
    exit;
}

$conn = getConnection();

// Handle post creation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_post') {
    if (validateCSRFToken($_POST['csrf_token'])) {
        $title = sanitize($_POST['title'] ?? '');
        $description = sanitize($_POST['description'] ?? '');
        $postType = sanitize($_POST['post_type'] ?? 'image'); // 'image' or 'video'
        $postFee = 1000; // ₦1000 for posting
        
        if (empty($title)) {
            $_SESSION['error_message'] = 'Title is required';
        } elseif ($user['wallet_balance'] < $postFee) {
            $_SESSION['error_message'] = 'Insufficient wallet balance. You need ₦1000 to create a post.';
        } elseif (!isset($_FILES['post_file']) || $_FILES['post_file']['error'] !== UPLOAD_ERR_OK) {
            $_SESSION['error_message'] = 'Please select a file to upload';
        } else {
            try {
                // Validate file type
                $allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                $allowedVideoTypes = ['video/mp4', 'video/webm', 'video/avi', 'video/mov'];
                $fileType = $_FILES['post_file']['type'];
                
                if ($postType === 'image' && !in_array($fileType, $allowedImageTypes)) {
                    throw new Exception('Invalid image file type. Only JPEG, PNG, GIF, and WebP are allowed.');
                } elseif ($postType === 'video' && !in_array($fileType, $allowedVideoTypes)) {
                    throw new Exception('Invalid video file type. Only MP4, WebM, AVI, and MOV are allowed.');
                }
                
                // Check file size (max 50MB for videos, 5MB for images)
                $maxSize = $postType === 'video' ? 50 * 1024 * 1024 : 5 * 1024 * 1024;
                if ($_FILES['post_file']['size'] > $maxSize) {
                    throw new Exception('File size too large. Maximum size is ' . ($postType === 'video' ? '50MB' : '5MB'));
                }
                
                // Create upload directory
                $uploadDir = '../uploads/posts/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                // Generate unique filename
                $fileExtension = pathinfo($_FILES['post_file']['name'], PATHINFO_EXTENSION);
                $fileName = 'post_' . $_SESSION['user_id'] . '_' . time() . '_' . uniqid() . '.' . $fileExtension;
                $filePath = $uploadDir . $fileName;
                
                // Move uploaded file
                if (!move_uploaded_file($_FILES['post_file']['tmp_name'], $filePath)) {
                    throw new Exception('Failed to save file');
                }
                
                // Generate thumbnail for videos
                $thumbnailPath = null;
                if ($postType === 'video') {
                    $thumbnailDir = '../uploads/thumbnails/';
                    if (!is_dir($thumbnailDir)) {
                        mkdir($thumbnailDir, 0755, true);
                    }
                    
                    $thumbnailName = 'thumb_' . pathinfo($fileName, PATHINFO_FILENAME) . '.jpg';
                    $thumbnailFullPath = $thumbnailDir . $thumbnailName;
                    
                    // Try to generate thumbnail using FFmpeg if available
                    if (function_exists('exec')) {
                        $ffmpegCmd = "ffmpeg -i \"$filePath\" -ss 00:00:01 -vframes 1 \"$thumbnailFullPath\" 2>/dev/null";
                        @exec($ffmpegCmd);
                        if (file_exists($thumbnailFullPath)) {
                            $thumbnailPath = 'uploads/thumbnails/' . $thumbnailName;
                        }
                    }
                }
                
                // Begin transaction
                $conn->begin_transaction();
                
                // Insert post
                $stmt = $conn->prepare("
                    INSERT INTO posts 
                    (user_id, title, description, post_type, file_path, thumbnail_path, file_size, is_public) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                ");
                $dbFilePath = 'uploads/posts/' . $fileName;
                $fileSize = filesize($filePath);
                $stmt->bind_param("isssssi", $_SESSION['user_id'], $title, $description, $postType, $dbFilePath, $thumbnailPath, $fileSize);
                
                if (!$stmt->execute()) {
                    throw new Exception('Failed to create post');
                }
                
                $postId = $conn->insert_id;
                
                // Deduct fee from user wallet
                $stmt = $conn->prepare("UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?");
                $stmt->bind_param("di", $postFee, $_SESSION['user_id']);
                if (!$stmt->execute()) {
                    throw new Exception('Failed to deduct post fee');
                }
                
                // Log transaction
                $stmt = $conn->prepare("
                    INSERT INTO transactions 
                    (user_id, transaction_type, amount, points, description, status) 
                    VALUES (?, 'deduction', ?, 0, 'Post creation fee', 'completed')
                ");
                $stmt->bind_param("id", $_SESSION['user_id'], $postFee);
                $stmt->execute();
                
                $conn->commit();
                $stmt->close();
                
                $_SESSION['success_message'] = 'Post created successfully! ₦1000 has been deducted from your wallet.';
                header('Location: streaming.php');
                exit;
                
            } catch (Exception $e) {
                $conn->rollback();
                // Delete uploaded file if it exists
                if (isset($filePath) && file_exists($filePath)) {
                    unlink($filePath);
                }
                if (isset($thumbnailFullPath) && file_exists($thumbnailFullPath)) {
                    unlink($thumbnailFullPath);
                }
                $_SESSION['error_message'] = $e->getMessage();
            }
        }
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Post - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/main.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .preview-container {
            max-width: 100%;
            margin-top: 20px;
        }
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
        }
        .preview-video {
            width: 100%;
            max-height: 300px;
            border-radius: 10px;
        }
        .file-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="fas fa-book-quran text-success me-2"></i>
                Qur'an Recite App
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.php">
                            <i class="fas fa-users me-1"></i>Community
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="streaming.php">
                            <i class="fas fa-video me-1"></i>Streaming
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="create-post.php">
                            <i class="fas fa-plus me-1"></i>Create Post
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-1"></i>Profile
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="<?php echo $user['profile_picture'] ? '../uploads/profiles/' . $user['profile_picture'] : '../assets/images/default-avatar.png'; ?>" 
                                 alt="Profile" class="rounded-circle me-2" style="width: 32px; height: 32px;">
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="wallet.php"><i class="fas fa-wallet me-2"></i>Wallet (₦<?php echo formatMoney($user['wallet_balance']); ?>)</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container" style="margin-top: 80px; padding: 20px;">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create New Post
                        </h2>
                        <p class="text-muted">Share your content with the community</p>
                    </div>
                    <div class="text-end">
                        <div class="badge bg-success fs-6">₦<?php echo formatMoney($user['wallet_balance']); ?></div>
                        <small class="d-block text-muted">Wallet Balance</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Post Creation Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Post Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Pricing Info -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Posting Fee:</strong> ₦1000 will be deducted from your wallet to create this post.
                            You can earn money when other users engage with your content.
                        </div>

                        <form method="POST" enctype="multipart/form-data" id="postForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="create_post">
                            
                            <!-- Post Type Selection -->
                            <div class="mb-3">
                                <label class="form-label">Content Type</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="post_type" id="image_type" value="image" checked>
                                    <label class="btn btn-outline-primary" for="image_type">
                                        <i class="fas fa-image me-1"></i>Image
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="post_type" id="video_type" value="video">
                                    <label class="btn btn-outline-primary" for="video_type">
                                        <i class="fas fa-video me-1"></i>Video
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       placeholder="Enter a catchy title for your post" maxlength="200" required>
                            </div>
                            
                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4"
                                          placeholder="Describe your content (optional)" maxlength="1000"></textarea>
                            </div>
                            
                            <!-- File Upload -->
                            <div class="mb-3">
                                <label class="form-label">Upload File <span class="text-danger">*</span></label>
                                <div class="upload-area" id="uploadArea" onclick="document.getElementById('postFile').click()">
                                    <input type="file" id="postFile" name="post_file" accept="" style="display: none;" required>
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Click to select a file</h5>
                                    <p class="text-muted mb-0">
                                        <span id="fileTypeText">Images: JPEG, PNG, GIF, WebP (max 5MB)</span>
                                    </p>
                                    <small class="text-muted">Or drag and drop your file here</small>
                                </div>
                                
                                <div id="previewContainer" class="preview-container" style="display: none;">
                                    <div id="filePreview"></div>
                                    <div id="fileInfo" class="file-info"></div>
                                    <button type="button" class="btn btn-sm btn-outline-secondary mt-2" onclick="clearFile()">
                                        <i class="fas fa-times me-1"></i>Remove File
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Wallet Balance Check -->
                            <?php if ($user['wallet_balance'] < 1000): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Insufficient Balance:</strong> You need ₦1000 to create a post. 
                                    <a href="wallet.php" class="alert-link">Add funds to your wallet</a>.
                                </div>
                            <?php endif; ?>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="streaming.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary" 
                                        <?php echo $user['wallet_balance'] < 1000 ? 'disabled' : ''; ?>>
                                    <i class="fas fa-upload me-1"></i>
                                    Create Post (₦1000)
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const imageType = document.getElementById('image_type');
            const videoType = document.getElementById('video_type');
            const postFile = document.getElementById('postFile');
            const uploadArea = document.getElementById('uploadArea');
            const previewContainer = document.getElementById('previewContainer');
            const filePreview = document.getElementById('filePreview');
            const fileInfo = document.getElementById('fileInfo');
            const fileTypeText = document.getElementById('fileTypeText');
            
            // Update file accept attribute based on selected type
            function updateFileAccept() {
                if (imageType.checked) {
                    postFile.accept = 'image/*';
                    fileTypeText.textContent = 'Images: JPEG, PNG, GIF, WebP (max 5MB)';
                } else {
                    postFile.accept = 'video/*';
                    fileTypeText.textContent = 'Videos: MP4, WebM, AVI, MOV (max 50MB)';
                }
                clearFile();
            }
            
            imageType.addEventListener('change', updateFileAccept);
            videoType.addEventListener('change', updateFileAccept);
            
            // File selection handling
            postFile.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    previewFile(file);
                }
            });
            
            // Drag and drop handling
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    postFile.files = files;
                    previewFile(files[0]);
                }
            });
            
            function previewFile(file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const maxSize = imageType.checked ? 5 : 50;
                
                if (file.size > maxSize * 1024 * 1024) {
                    alert(`File size exceeds ${maxSize}MB limit`);
                    clearFile();
                    return;
                }
                
                // Show file info
                fileInfo.innerHTML = `
                    <strong>File:</strong> ${file.name}<br>
                    <strong>Size:</strong> ${fileSize} MB<br>
                    <strong>Type:</strong> ${file.type}
                `;
                
                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (file.type.startsWith('image/')) {
                        filePreview.innerHTML = `<img src="${e.target.result}" class="preview-image" alt="Preview">`;
                    } else if (file.type.startsWith('video/')) {
                        filePreview.innerHTML = `<video src="${e.target.result}" class="preview-video" controls></video>`;
                    }
                };
                reader.readAsDataURL(file);
                
                uploadArea.style.display = 'none';
                previewContainer.style.display = 'block';
            }
            
            window.clearFile = function() {
                postFile.value = '';
                uploadArea.style.display = 'block';
                previewContainer.style.display = 'none';
                filePreview.innerHTML = '';
                fileInfo.innerHTML = '';
            };
        });
    </script>
</body>
</html> 