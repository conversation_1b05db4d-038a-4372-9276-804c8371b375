/*
 * RECITE APP DESIGN SYSTEM
 * Comprehensive design system for consistent, modern UI/UX
 * Based on the wallet.php design patterns
 */

/* ===========================================
   ROOT VARIABLES & THEME COLORS
   =========================================== */
:root {
    /* Primary Colors - Dark Red Theme */
    --primary-red: #B10020;
    --primary-red-light: #D32F2F;
    --primary-red-dark: #8B0000;
    --primary-gradient: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
    --success-gradient: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
    --warning-gradient: linear-gradient(135deg, var(--primary-red-light), #FF6B6B);
    --danger-gradient: linear-gradient(135deg, var(--primary-red-dark), var(--primary-red));
    --info-gradient: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
    --purple-gradient: linear-gradient(135deg, var(--primary-red-dark), var(--primary-red));
    --dark-gradient: linear-gradient(135deg, #1E1E1E, #333333);
    
    /* Neutral Colors */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --subtle-gray: #f3f3f3;
    --medium-gray: #e8e8e8;
    --dark-gray: #1e1e1e;
    --darker-gray: #222222;
    --border-color: #e8e8e8;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-xl: 0 10px 30px rgba(0,0,0,0.2);
    --shadow-colored: 0 10px 30px rgba(177, 0, 32, 0.3);
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 15px;
    --radius-xl: 20px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===========================================
   GLOBAL STYLES
   =========================================== */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--white);
    color: var(--dark-gray);
    min-height: 100vh;
}

.bg-light {
    background: var(--white) !important;
}

/* ===========================================
   NAVIGATION SYSTEM
   =========================================== */
.navbar {
    background: var(--primary-gradient) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    transition: var(--transition-fast);
    border-radius: var(--radius-sm);
    margin: 0 4px;
    padding: 8px 12px !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: white !important;
    background: rgba(255,255,255,0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-md);
    padding: 0.75rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--primary-gradient);
    color: white;
}

/* ===========================================
   CARD SYSTEM
   =========================================== */
.card {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Premium Card Variants */
.premium-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-colored);
}

.success-card {
    background: var(--success-gradient);
    color: white;
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
}

.warning-card {
    background: var(--warning-gradient);
    color: white;
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.3);
}

.danger-card {
    background: var(--danger-gradient);
    color: white;
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3);
}

.purple-card {
    background: var(--purple-gradient);
    color: white;
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(102, 16, 242, 0.3);
}

/* ===========================================
   ACTION CARDS
   =========================================== */
.action-card {
    transition: var(--transition-normal);
    cursor: pointer;
    border: 2px solid transparent;
    height: 100%;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.action-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: #007bff;
}

.action-card .card-body {
    text-align: center;
    padding: 2rem;
}

.action-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    transition: var(--transition-fast);
}

.action-card:hover i {
    transform: scale(1.1);
}

/* ===========================================
   BUTTONS SYSTEM
   =========================================== */
.btn {
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: var(--transition-fast);
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-red-dark), var(--primary-red));
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-gradient);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #bd2130, #a71e2a);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
}

/* ===========================================
   DISPLAY COMPONENTS
   =========================================== */
.balance-display {
    font-size: 2.5rem;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    margin: 0.5rem 0;
}

.metric-display {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.stats-number {
    font-size: 3rem;
    font-weight: 900;
    line-height: 1;
    color: var(--primary-red);
}

/* ===========================================
   BADGES & LABELS
   =========================================== */
.rate-badge {
    background: var(--success-gradient);
    color: white;
    padding: 6px 16px;
    border-radius: var(--radius-xl);
    font-size: 0.85rem;
    font-weight: 700;
    box-shadow: var(--shadow-sm);
}

.status-badge {
    padding: 8px 16px;
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: var(--success-gradient);
    color: white;
}

.badge-warning {
    background: var(--warning-gradient);
    color: white;
}

.badge-danger {
    background: var(--danger-gradient);
    color: white;
}

.badge-info {
    background: var(--info-gradient);
    color: white;
}

/* ===========================================
   LIST & TRANSACTION ITEMS
   =========================================== */
.transaction-item,
.list-item {
    border-left: 4px solid transparent;
    transition: var(--transition-normal);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: white;
    box-shadow: var(--shadow-sm);
}

.transaction-item:hover,
.list-item:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.transaction-item.positive {
    border-left-color: var(--primary-red);
}

.transaction-item.negative {
    border-left-color: var(--primary-red-dark);
}

.transaction-item.info {
    border-left-color: var(--primary-red-light);
}

.transaction-item.warning {
    border-left-color: var(--primary-red-light);
}

/* ===========================================
   FORM COMPONENTS
   =========================================== */
.form-control {
    border-radius: var(--radius-md);
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
    background: white;
}

.form-control:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(177, 0, 32, 0.25);
    background: white;
}

.form-label {
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--medium-gray);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.exchange-info,
.info-box {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid var(--border-color);
}

/* ===========================================
   MODALS
   =========================================== */
.modal-content {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* ===========================================
   ALERTS & NOTIFICATIONS
   =========================================== */
.alert {
    border: none;
    border-radius: var(--radius-md);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(200, 35, 51, 0.1));
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(19, 132, 150, 0.1));
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* ===========================================
   TABLES
   =========================================== */
.table {
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    background: white;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none;
    font-weight: 700;
    color: var(--dark-gray);
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05), rgba(0, 86, 179, 0.05));
}

.table tbody td {
    padding: 1rem;
    border-color: rgba(0,0,0,0.05);
}

/* ===========================================
   LOADING STATES
   =========================================== */
.spinner-border {
    border-width: 3px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* ===========================================
   UTILITY CLASSES
   =========================================== */
.text-gradient-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.text-gradient-success {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

.rounded-custom {
    border-radius: var(--radius-lg);
}

.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

/* ===========================================
   RESPONSIVE DESIGN
   =========================================== */
@media (max-width: 768px) {
    .balance-display {
        font-size: 2rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
    }
    
    .action-card .card-body {
        padding: 1.5rem;
    }
    
    .action-card i {
        font-size: 2.5rem;
    }
    
    .container-fluid {
        padding: 1rem !important;
    }
}

@media (max-width: 576px) {
    .balance-display {
        font-size: 1.75rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
}

/* ===========================================
   ANIMATIONS
   =========================================== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in {
    animation: slideInLeft 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* ===========================================
   DARK MODE SUPPORT (Future Enhancement)
   =========================================== */
@media (prefers-color-scheme: dark) {
    :root {
        --light-gray: #2d3748;
        --medium-gray: #a0aec0;
        --dark-gray: #1a202c;
        --border-color: #4a5568;
    }
    
    body {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        color: #e2e8f0;
    }
    
    .card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .form-control {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
} 