<?php
/**
 * Buy Points API for Recite! App
 * Allows users to buy points using their wallet balance
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$points = intval($input['points'] ?? 0);
$expectedAmount = floatval($input['amount'] ?? 0);

if ($points < 1) {
    echo json_encode(['success' => false, 'message' => 'Invalid points amount']);
    exit;
}

// Calculate actual cost (₦70 per point)
$actualCost = $points * 70;

if (abs($expectedAmount - $actualCost) > 0.01) {
    echo json_encode(['success' => false, 'message' => 'Amount mismatch']);
    exit;
}

try {
    $conn = getConnection();
    
    // Get user's current wallet balance
    $userResult = executeQuery(
        "SELECT wallet_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    
    $user = $userResult->fetch_assoc();
    $currentBalance = floatval($user['wallet_balance']);
    
    if ($currentBalance < $actualCost) {
        echo json_encode([
            'success' => false, 
            'message' => 'Insufficient wallet balance. You need ₦' . number_format($actualCost, 2)
        ]);
        exit;
    }
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Deduct from wallet
        executeQuery(
            "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
            'di',
            [$actualCost, $userId]
        );
        
        // Add points
        executeQuery(
            "UPDATE users SET points_balance = points_balance + ? WHERE id = ?",
            'ii',
            [$points, $userId]
        );
        
        // Log wallet transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'purchase', ?, ?, 'completed')",
            'ids',
            [$userId, -$actualCost, "Bought {$points} points"]
        );
        
        // Log points transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'purchase', ?, ?, 'completed')",
            'iis',
            [$userId, $points, "Bought {$points} points for ₦{$actualCost}"]
        );
        
        // Commit transaction
        $conn->commit();
        $conn->autocommit(true);
        
        $newBalance = $currentBalance - $actualCost;
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully bought {$points} points for ₦" . number_format($actualCost, 2),
            'new_wallet_balance' => $newBalance,
            'points_added' => $points
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Buy points error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to buy points']);
}
?> 