/* 
 * ReciteApp Fresh Design System
 * Mobile-First Responsive Design with Sidebar Integration
 * Version: 2.0
 */

/* CSS Custom Properties - Design Tokens */
:root {
    /* Primary Colors */
    --primary: #B10020;
    --primary-light: #E53E3E;
    --primary-dark: #8B0000;
    
    /* Gray Scale */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* Semantic Colors */
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --info: #3B82F6;
    
    /* Spacing Scale */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Layout */
    --sidebar-width: 250px;
    --header-height: 80px;
    --container-max-width: 1200px;
    
    /* Transitions */
    --transition: all 0.3s ease;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.5;
    margin: 0;
    padding: 0;
    font-size: var(--font-size-base);
}

/* Layout Components */

/* Main Content Container */
.main-content {
    width: 100%;
    min-height: calc(100vh - var(--header-height));
    background: var(--gray-50);
    padding: var(--space-md);
    margin-left: 0;
    margin-top: var(--header-height);
    transition: var(--transition);
}

/* Sidebar Integration */
@media (min-width: 768px) {
    .main-content {
        margin-left: var(--sidebar-width);
        width: calc(100% - var(--sidebar-width));
        padding: var(--space-lg);
    }
    
    body.sidebar-open .main-content {
        margin-left: var(--sidebar-width);
        width: calc(100% - var(--sidebar-width));
    }
    
    body.sidebar-closed .main-content {
        margin-left: 0;
        width: 100%;
    }
}

/* Mobile - Hide sidebar, full width */
@media (max-width: 767px) {
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding: var(--space-md);
    }
    
    .sidebar {
        display: none !important;
    }
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--space-md);
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--space-md);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Card Components */
.card {
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.card-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.card-content {
    padding: var(--space-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    min-height: 44px; /* Touch-friendly */
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-800);
}

.btn-secondary:hover {
    background: var(--gray-300);
}

.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: var(--warning);
    color: var(--white);
}

.btn-warning:hover {
    background: #D97706;
}

.btn-error {
    background: var(--error);
    color: var(--white);
}

.btn-error:hover {
    background: #DC2626;
}

.btn-sm {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-xs);
    min-height: 36px;
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

/* Form Components */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-sm);
}

.form-input {
    width: 100%;
    padding: var(--space-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
    min-height: 44px;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(177, 0, 32, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }

.text-primary { color: var(--primary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }

.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }

.rounded { border-radius: var(--radius); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.mb-2 { margin-bottom: var(--space-sm); }
.mb-4 { margin-bottom: var(--space-md); }
.mb-6 { margin-bottom: var(--space-lg); }
.mb-8 { margin-bottom: var(--space-xl); }

.mt-2 { margin-top: var(--space-sm); }
.mt-4 { margin-top: var(--space-md); }
.mt-6 { margin-top: var(--space-lg); }
.mt-8 { margin-top: var(--space-xl); }

.p-4 { padding: var(--space-md); }
.p-6 { padding: var(--space-lg); }
.p-8 { padding: var(--space-xl); }

.px-4 { padding-left: var(--space-md); padding-right: var(--space-md); }
.py-2 { padding-top: var(--space-sm); padding-bottom: var(--space-sm); }
.py-4 { padding-top: var(--space-md); padding-bottom: var(--space-md); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-sm); }
.gap-4 { gap: var(--space-md); }
.gap-6 { gap: var(--space-lg); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.hidden { display: none; }
.block { display: block; }

@media (min-width: 768px) {
    .md\:block { display: block; }
    .md\:hidden { display: none; }
    .md\:flex { display: flex; }
}

/* Specialized Components for ReciteApp */

/* Page Header */
.page-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-lg) 0;
    margin-bottom: var(--space-xl);
}

.page-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.page-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin: var(--space-sm) 0 0 0;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    margin-bottom: var(--space-xl);
}

.stat-card {
    background: var(--white);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    text-align: center;
}

.stat-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary);
    display: block;
    margin-bottom: var(--space-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
}

/* List Components */
.list-item {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    padding: var(--space-lg);
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
}

.list-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.list-item-content {
    flex: 1;
}

.list-item-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-xs) 0;
}

.list-item-subtitle {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.list-item-actions {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
}

/* Alert Components */
.alert {
    padding: var(--space-lg);
    border-radius: var(--radius);
    margin-bottom: var(--space-lg);
    border: 1px solid;
}

.alert-success {
    background: #F0FDF4;
    border-color: var(--success);
    color: #166534;
}

.alert-warning {
    background: #FFFBEB;
    border-color: var(--warning);
    color: #92400E;
}

.alert-error {
    background: #FEF2F2;
    border-color: var(--error);
    color: #991B1B;
}

.alert-info {
    background: #EFF6FF;
    border-color: var(--info);
    color: #1E40AF;
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.badge-primary {
    background: var(--primary);
    color: var(--white);
}

.badge-success {
    background: var(--success);
    color: var(--white);
}

.badge-warning {
    background: var(--warning);
    color: var(--white);
}

.badge-error {
    background: var(--error);
    color: var(--white);
}

.badge-gray {
    background: var(--gray-200);
    color: var(--gray-800);
}

/* Modal Components */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--space-md);
}

.modal {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--space-lg);
}

.modal-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Arabic Text Support */
.arabic-text {
    font-family: 'Amiri', 'Times New Roman', serif;
    direction: rtl;
    text-align: right;
    font-size: var(--font-size-lg);
    line-height: 1.8;
}

.arabic-word {
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.arabic-word.correct {
    background: #DBEAFE;
    color: var(--info);
}

.arabic-word.error {
    background: #FEE2E2;
    color: var(--error);
}

/* Responsive Utilities */
@media (max-width: 767px) {
    .mobile-stack > * {
        width: 100% !important;
        margin-bottom: var(--space-md);
    }

    .mobile-hide {
        display: none !important;
    }

    .mobile-center {
        text-align: center !important;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
}
