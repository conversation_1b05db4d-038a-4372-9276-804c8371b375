/*
 * RECITE App - Modern Dark Green Design System
 * Mobile-First Responsive Design with Clean Minimal Aesthetics
 * Version: 3.0 - Complete Redesign
 */

/* CSS Custom Properties - Design Tokens */
:root {
    /* Primary Dark Green Colors */
    --primary: #1a5f3f;
    --primary-light: #2d7a5a;
    --primary-dark: #0f3d28;
    --primary-hover: #164d34;

    /* Neutral Colors - Clean and Minimal */
    --white: #FFFFFF;
    --gray-50: #FAFAFA;
    --gray-100: #F5F5F5;
    --gray-200: #EEEEEE;
    --gray-300: #E0E0E0;
    --gray-400: #BDBDBD;
    --gray-500: #9E9E9E;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;

    /* Semantic Colors */
    --success: #4CAF50;
    --success-light: #81C784;
    --warning: #FF9800;
    --warning-light: #FFB74D;
    --error: #F44336;
    --error-light: #E57373;
    --info: #2196F3;
    --info-light: #64B5F6;

    /* Spacing Scale - Consistent and Clean */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */

    /* Typography - Modern and Readable */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-arabic: 'Amiri', 'Times New Roman', serif;
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */

    /* Border Radius - Consistent Rounded Corners */
    --radius-sm: 0.375rem;   /* 6px */
    --radius: 0.5rem;        /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */

    /* Shadows - Subtle and Modern */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Layout - Responsive and Flexible */
    --sidebar-width: 280px;
    --header-height: 70px;
    --container-max-width: 1200px;
    --container-padding: var(--space-md);

    /* Transitions - Smooth and Consistent */
    --transition-fast: all 0.15s ease;
    --transition: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== BASE STYLES - RESET AND FOUNDATION ===== */

/* Modern CSS Reset */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Remove default button styles */
button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font: inherit;
    cursor: pointer;
}

/* Remove default link styles */
a {
    color: inherit;
    text-decoration: none;
}

/* Remove default list styles */
ul, ol {
    list-style: none;
}

/* Remove default form styles */
input, textarea, select {
    font: inherit;
    color: inherit;
}

/* Focus styles for accessibility */
:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* ===== LAYOUT SYSTEM - RESPONSIVE AND FLEXIBLE ===== */

/* Container System */
.container {
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--container-padding);
}

/* Main Layout Structure */
.app-layout {
    display: flex;
    min-height: 100vh;
    background: var(--gray-50);
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: var(--white);
    transition: var(--transition);
    margin-left: 0;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: var(--space-lg);
    background: var(--gray-50);
    overflow-y: auto;
}

/* Sidebar Integration - Desktop First */
@media (min-width: 768px) {
    .main-content {
        margin-left: var(--sidebar-width);
        width: calc(100% - var(--sidebar-width));
    }

    .content-area {
        padding: var(--space-xl);
    }

    body.sidebar-open .main-content {
        margin-left: var(--sidebar-width);
        width: calc(100% - var(--sidebar-width));
    }

    body.sidebar-closed .main-content {
        margin-left: 0;
        width: 100%;
    }
}

/* Mobile Layout - Full Width */
@media (max-width: 767px) {
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .content-area {
        padding: var(--space-md);
    }

    .sidebar {
        display: none !important;
    }
}

/* ===== GRID SYSTEM - FLEXIBLE AND RESPONSIVE ===== */

.grid {
    display: grid;
    gap: var(--space-md);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Auto-fit grids for responsive cards */
.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* Gap variations */
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

/* Responsive grid classes */
@media (min-width: 640px) {
    .sm\:grid-cols-1 { grid-template-columns: 1fr; }
    .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .sm\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 768px) {
    .md\:grid-cols-1 { grid-template-columns: 1fr; }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
    .md\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
    .md\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-1 { grid-template-columns: 1fr; }
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
    .lg\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
    .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

@media (min-width: 1280px) {
    .xl\:grid-cols-1 { grid-template-columns: 1fr; }
    .xl\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
    .xl\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
    .xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

/* ===== CARD COMPONENTS - CLEAN AND MODERN ===== */

.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    border-color: var(--primary);
}

.card-simple {
    background: var(--white);
    border-radius: var(--radius);
    padding: var(--space-lg);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.card-simple:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

/* Card Header */
.card-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-md);
    background: var(--gray-50);
}

.card-header-clean {
    padding: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: var(--primary);
    color: var(--white);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.card-icon-simple {
    font-size: var(--font-size-2xl);
    color: var(--primary);
    flex-shrink: 0;
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    line-height: 1.4;
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: var(--space-xs) 0 0 0;
}

/* Card Content */
.card-content {
    padding: var(--space-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-content-padded {
    padding: var(--space-xl);
}

/* Card Footer */
.card-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-md);
}

.card-footer-clean {
    padding: var(--space-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-md);
}

/* ===== BUTTON COMPONENTS - MODERN AND ACCESSIBLE ===== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
    min-height: 44px; /* Touch-friendly */
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Primary Button - Dark Green */
.btn-primary {
    background: var(--primary);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-1px);
}

/* Outline Button */
.btn-outline {
    background: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-1px);
}

/* Ghost Button */
.btn-ghost {
    background: transparent;
    color: var(--gray-600);
    border: none;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--gray-100);
    color: var(--gray-800);
}

/* Semantic Buttons */
.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-success:hover:not(:disabled) {
    background: var(--success-light);
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--warning);
    color: var(--white);
}

.btn-warning:hover:not(:disabled) {
    background: var(--warning-light);
    transform: translateY(-1px);
}

.btn-error {
    background: var(--error);
    color: var(--white);
}

.btn-error:hover:not(:disabled) {
    background: var(--error-light);
    transform: translateY(-1px);
}

/* Button Sizes */
.btn-xs {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
    min-height: 32px;
}

.btn-sm {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-lg {
    padding: var(--space-lg) var(--space-xl);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

.btn-xl {
    padding: var(--space-xl) var(--space-2xl);
    font-size: var(--font-size-xl);
    min-height: 60px;
}

/* Full Width Button */
.btn-full {
    width: 100%;
}

/* Icon-only Button */
.btn-icon {
    padding: var(--space-md);
    min-width: 44px;
    aspect-ratio: 1;
}

/* Button Group */
.btn-group {
    display: inline-flex;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid var(--gray-300);
}

.btn-group .btn:first-child {
    border-radius: var(--radius) 0 0 var(--radius);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--radius) var(--radius) 0;
    border-right: none;
}

/* ===== FORM COMPONENTS - CLEAN AND ACCESSIBLE ===== */

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group-inline {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-sm);
    line-height: 1.4;
}

.form-label-required::after {
    content: ' *';
    color: var(--error);
}

.form-input {
    width: 100%;
    padding: var(--space-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: var(--font-size-base);
    background: var(--white);
    transition: var(--transition-fast);
    min-height: 44px;
    line-height: 1.4;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(26, 95, 63, 0.1);
}

.form-input:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

.form-input.error {
    border-color: var(--error);
}

.form-input.error:focus {
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23757575' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    padding-right: 2.5rem;
    cursor: pointer;
}

.form-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%231a5f3f' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Checkbox and Radio */
.form-checkbox,
.form-radio {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    background: var(--white);
    cursor: pointer;
    transition: var(--transition-fast);
}

.form-checkbox {
    border-radius: var(--radius-sm);
}

.form-radio {
    border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
    background: var(--primary);
    border-color: var(--primary);
}

.form-checkbox:focus,
.form-radio:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Form Help Text */
.form-help {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    margin-top: var(--space-xs);
}

.form-error {
    font-size: var(--font-size-xs);
    color: var(--error);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

/* Input Groups */
.input-group {
    display: flex;
    align-items: stretch;
    width: 100%;
}

.input-group .form-input {
    border-radius: 0;
    border-right: none;
}

.input-group .form-input:first-child {
    border-radius: var(--radius) 0 0 var(--radius);
}

.input-group .form-input:last-child {
    border-radius: 0 var(--radius) var(--radius) 0;
    border-right: 1px solid var(--gray-300);
}

.input-group-addon {
    display: flex;
    align-items: center;
    padding: var(--space-md);
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    white-space: nowrap;
}

.input-group-addon:first-child {
    border-radius: var(--radius) 0 0 var(--radius);
    border-right: none;
}

.input-group-addon:last-child {
    border-radius: 0 var(--radius) var(--radius) 0;
    border-left: none;
}

/* ===== UTILITY CLASSES - COMPREHENSIVE AND CONSISTENT ===== */

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Font Sizes */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

/* Font Weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

/* Line Heights */
.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.75; }

/* Text Colors */
.text-white { color: var(--white); }
.text-gray-400 { color: var(--gray-400); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }
.text-primary { color: var(--primary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }

/* Background Colors */
.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-gray-200 { background-color: var(--gray-200); }
.bg-primary { background-color: var(--primary); }
.bg-success { background-color: var(--success); }
.bg-warning { background-color: var(--warning); }
.bg-error { background-color: var(--error); }
.bg-info { background-color: var(--info); }

/* Border Radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Spacing - Margin */
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-xs); }
.mt-2 { margin-top: var(--space-sm); }
.mt-4 { margin-top: var(--space-md); }
.mt-6 { margin-top: var(--space-lg); }
.mt-8 { margin-top: var(--space-xl); }
.mt-12 { margin-top: var(--space-2xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-4 { margin-bottom: var(--space-md); }
.mb-6 { margin-bottom: var(--space-lg); }
.mb-8 { margin-bottom: var(--space-xl); }
.mb-12 { margin-bottom: var(--space-2xl); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-xs); }
.ml-2 { margin-left: var(--space-sm); }
.ml-4 { margin-left: var(--space-md); }
.ml-6 { margin-left: var(--space-lg); }
.ml-8 { margin-left: var(--space-xl); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-xs); }
.mr-2 { margin-right: var(--space-sm); }
.mr-4 { margin-right: var(--space-md); }
.mr-6 { margin-right: var(--space-lg); }
.mr-8 { margin-right: var(--space-xl); }

/* Spacing - Padding */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-xs); }
.p-2 { padding: var(--space-sm); }
.p-4 { padding: var(--space-md); }
.p-6 { padding: var(--space-lg); }
.p-8 { padding: var(--space-xl); }
.p-12 { padding: var(--space-2xl); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-xs); padding-right: var(--space-xs); }
.px-2 { padding-left: var(--space-sm); padding-right: var(--space-sm); }
.px-4 { padding-left: var(--space-md); padding-right: var(--space-md); }
.px-6 { padding-left: var(--space-lg); padding-right: var(--space-lg); }
.px-8 { padding-left: var(--space-xl); padding-right: var(--space-xl); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-xs); padding-bottom: var(--space-xs); }
.py-2 { padding-top: var(--space-sm); padding-bottom: var(--space-sm); }
.py-4 { padding-top: var(--space-md); padding-bottom: var(--space-md); }
.py-6 { padding-top: var(--space-lg); padding-bottom: var(--space-lg); }
.py-8 { padding-top: var(--space-xl); padding-bottom: var(--space-xl); }

/* Flexbox */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* Gap */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--space-xs); }
.gap-2 { gap: var(--space-sm); }
.gap-4 { gap: var(--space-md); }
.gap-6 { gap: var(--space-lg); }
.gap-8 { gap: var(--space-xl); }

/* Width & Height */
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

/* Cursor */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* User Select */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* ===== SPECIALIZED COMPONENTS FOR RECITE APP ===== */

/* Page Header */
.page-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-xl) 0;
    margin-bottom: var(--space-2xl);
}

.page-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    line-height: 1.2;
}

.page-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin: var(--space-sm) 0 0 0;
    line-height: 1.4;
}

/* Navigation Components */
.nav {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.nav-horizontal {
    flex-direction: row;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md) var(--space-lg);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius);
    transition: var(--transition-fast);
    font-weight: 500;
}

.nav-link:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.nav-link.active {
    background: var(--primary);
    color: var(--white);
}

.nav-link-icon {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-lg);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--gray-400);
}

.breadcrumb-link {
    color: var(--primary);
    text-decoration: none;
}

.breadcrumb-link:hover {
    text-decoration: underline;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.stat-card {
    background: var(--white);
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
}

.stat-value {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary);
    display: block;
    margin-bottom: var(--space-sm);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-change {
    font-size: var(--font-size-xs);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

/* List Components */
.list-item {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    padding: var(--space-lg);
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
}

.list-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.list-item-content {
    flex: 1;
}

.list-item-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-xs) 0;
}

.list-item-subtitle {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.list-item-actions {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
}

/* ===== ALERT COMPONENTS - INFORMATIVE AND ACCESSIBLE ===== */

.alert {
    padding: var(--space-lg);
    border-radius: var(--radius);
    margin-bottom: var(--space-lg);
    border: 1px solid;
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.alert-icon {
    font-size: var(--font-size-lg);
    flex-shrink: 0;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.alert-message {
    margin: 0;
}

.alert-success {
    background: #F0FDF4;
    border-color: var(--success);
    color: #166534;
}

.alert-success .alert-icon {
    color: var(--success);
}

.alert-warning {
    background: #FFFBEB;
    border-color: var(--warning);
    color: #92400E;
}

.alert-warning .alert-icon {
    color: var(--warning);
}

.alert-error {
    background: #FEF2F2;
    border-color: var(--error);
    color: #991B1B;
}

.alert-error .alert-icon {
    color: var(--error);
}

.alert-info {
    background: #EFF6FF;
    border-color: var(--info);
    color: #1E40AF;
}

.alert-info .alert-icon {
    color: var(--info);
}

/* Dismissible Alert */
.alert-dismissible {
    padding-right: var(--space-2xl);
    position: relative;
}

.alert-close {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    transition: var(--transition-fast);
}

.alert-close:hover {
    opacity: 1;
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.badge-primary {
    background: var(--primary);
    color: var(--white);
}

.badge-success {
    background: var(--success);
    color: var(--white);
}

.badge-warning {
    background: var(--warning);
    color: var(--white);
}

.badge-error {
    background: var(--error);
    color: var(--white);
}

.badge-gray {
    background: var(--gray-200);
    color: var(--gray-800);
}

/* Modal Components */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--space-md);
}

.modal {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--space-lg);
}

.modal-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* ===== ARABIC TEXT SUPPORT - SPECIALIZED FOR QURAN RECITATION ===== */

.arabic-text {
    font-family: var(--font-family-arabic);
    direction: rtl;
    text-align: right;
    font-size: var(--font-size-xl);
    line-height: 2;
    color: var(--gray-800);
}

.arabic-text-large {
    font-size: var(--font-size-2xl);
    line-height: 2.2;
}

.arabic-text-small {
    font-size: var(--font-size-lg);
    line-height: 1.8;
}

.arabic-word {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    display: inline-block;
    margin: 0 2px;
}

.arabic-word:hover {
    background: var(--gray-100);
}

.arabic-word.correct {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.arabic-word.error {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.arabic-word.current {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning);
    border: 1px solid rgba(255, 152, 0, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Recitation Status Indicators */
.recitation-status {
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius);
    text-align: center;
    font-weight: 600;
    margin-bottom: var(--space-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.recitation-status.listening {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.recitation-status.recording {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error);
    border: 1px solid rgba(244, 67, 54, 0.3);
    animation: pulse 2s infinite;
}

.recitation-status.ready {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

/* ===== RESPONSIVE UTILITIES - MOBILE-FIRST APPROACH ===== */

/* Mobile-specific utilities */
@media (max-width: 639px) {
    .mobile-hidden { display: none !important; }
    .mobile-only { display: block !important; }

    .mobile\:text-center { text-align: center; }
    .mobile\:text-left { text-align: left; }
    .mobile\:p-4 { padding: var(--space-md); }
    .mobile\:px-4 { padding-left: var(--space-md); padding-right: var(--space-md); }
    .mobile\:py-4 { padding-top: var(--space-md); padding-bottom: var(--space-md); }
    .mobile\:mb-4 { margin-bottom: var(--space-md); }
    .mobile\:mt-4 { margin-top: var(--space-md); }

    .mobile-stack > * {
        width: 100% !important;
        margin-bottom: var(--space-md);
    }

    .mobile-hide {
        display: none !important;
    }

    .mobile-center {
        text-align: center !important;
    }

    .card {
        margin-bottom: var(--space-md);
    }

    .btn:not(.btn-sm):not(.btn-xs) {
        width: 100%;
        justify-content: center;
    }

    .btn + .btn {
        margin-top: var(--space-sm);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
}

/* Tablet utilities */
@media (min-width: 640px) and (max-width: 1023px) {
    .tablet-hidden { display: none !important; }
    .tablet-only { display: block !important; }

    .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .sm\:text-center { text-align: center; }
    .sm\:text-left { text-align: left; }
    .sm\:flex { display: flex; }
    .sm\:block { display: block; }
    .sm\:hidden { display: none; }
}

/* Desktop utilities */
@media (min-width: 768px) {
    .desktop-hidden { display: none !important; }
    .mobile-only { display: none !important; }

    .md\:block { display: block; }
    .md\:hidden { display: none; }
    .md\:flex { display: flex; }
    .md\:inline-flex { display: inline-flex; }
    .md\:grid { display: grid; }

    .md\:text-left { text-align: left; }
    .md\:text-center { text-align: center; }
    .md\:text-right { text-align: right; }

    .md\:w-auto { width: auto; }
    .md\:w-1\/2 { width: 50%; }
    .md\:w-1\/3 { width: 33.333333%; }
    .md\:w-2\/3 { width: 66.666667%; }
    .md\:w-1\/4 { width: 25%; }
    .md\:w-3\/4 { width: 75%; }
}

/* Large desktop utilities */
@media (min-width: 1024px) {
    .lg\:block { display: block; }
    .lg\:hidden { display: none; }
    .lg\:flex { display: flex; }
    .lg\:inline-flex { display: inline-flex; }

    .lg\:text-left { text-align: left; }
    .lg\:text-center { text-align: center; }
    .lg\:text-right { text-align: right; }

    .lg\:w-auto { width: auto; }
    .lg\:w-1\/2 { width: 50%; }
    .lg\:w-1\/3 { width: 33.333333%; }
    .lg\:w-2\/3 { width: 66.666667%; }
    .lg\:w-1\/4 { width: 25%; }
    .lg\:w-3\/4 { width: 75%; }
}

/* ===== PRINT STYLES ===== */
@media print {
    .print-hidden { display: none !important; }
    .no-print { display: none !important; }

    .card {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }

    .btn {
        border: 1px solid var(--gray-400);
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border-width: 2px;
    }

    .btn {
        border-width: 2px;
        border-style: solid;
    }

    .btn-primary {
        border-color: var(--primary-dark);
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here in future versions */
}
