<?php
$page_title = 'Manage Payouts';
require_once __DIR__ . '/../components/admin_header.php';

// Fetch pending withdrawals
$conn = getConnection();
$result = $conn->query("
    SELECT w.*, u.full_name as username, u.email
    FROM withdrawal_requests w
    JOIN users u ON w.user_id = u.id
    WHERE w.status = 'pending'
    ORDER BY w.created_at ASC
");
$pending_withdrawals = $result->fetch_all(MYSQLI_ASSOC);
?>

<div class="data-table-container">
    <div class="table-header">
        <h4 class="table-title">Pending Withdrawal Requests</h4>
    </div>
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Amount</th>
                    <th>Bank Details</th>
                    <th>Requested At</th>
                    <th class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($pending_withdrawals)): ?>
                    <tr><td colspan="5" class="text-center py-5">No pending withdrawals.</td></tr>
                <?php else: ?>
                    <?php foreach ($pending_withdrawals as $withdrawal): ?>
                    <tr>
                        <td>
                            <div>
                                <div class="fw-semibold"><?php echo htmlspecialchars($withdrawal['username']); ?></div>
                                <small class="text-muted"><?php echo htmlspecialchars($withdrawal['email']); ?></small>
                            </div>
                        </td>
                        <td>$<?php echo number_format($withdrawal['amount'], 2); ?></td>
                        <td>
                            <span class="text-muted">Contact user for details</span>
                        </td>
                        <td><?php echo date('M d, Y H:i', strtotime($withdrawal['created_at'])); ?></td>
                        <td class="text-end">
                            <button class="btn btn-sm btn-success">Approve</button>
                            <button class="btn btn-sm btn-danger">Reject</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>