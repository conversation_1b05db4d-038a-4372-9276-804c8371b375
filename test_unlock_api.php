<?php
session_start();
require_once 'config/database.php';

// Simulate logged in user
$_SESSION['user_id'] = 1; // Assuming user ID 1 exists

echo "<h2>Testing Unlock Content API</h2>";

// Test data
$testData = [
    'content_id' => 1,
    'csrf_token' => 'test_token'
];

echo "<h3>Test Data:</h3>";
echo "<pre>" . print_r($testData, true) . "</pre>";

// Make a POST request to the API
$url = 'http://localhost/RECITE_appbac/RECITE_app/api/unlock_content.php';
$data = json_encode($testData);

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => $data
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

echo "<h3>API Response:</h3>";
echo "<pre>" . htmlspecialchars($result) . "</pre>";

// Try to decode JSON
$decoded = json_decode($result, true);
if ($decoded) {
    echo "<h3>Decoded Response:</h3>";
    echo "<pre>" . print_r($decoded, true) . "</pre>";
} else {
    echo "<p style='color: red;'>Failed to decode JSON response</p>";
}

// Check user balance
try {
    $conn = getConnection();
    $stmt = $conn->prepare("SELECT wallet_balance FROM users WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    echo "<h3>Current User Balance:</h3>";
    echo "<p>₦" . number_format($user['wallet_balance'], 2) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking balance: " . $e->getMessage() . "</p>";
}

echo "<p><a href='dashboard.php'>Back to Dashboard</a></p>";
?>
