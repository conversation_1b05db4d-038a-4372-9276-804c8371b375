<?php
/**
 * User Dashboard for Recite! App
 * Clean, Mobile-First Dashboard with Simple Functionality
 */

require_once 'config/database.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Dashboard';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Initialize variables
$message = '';
$error = '';
$conn = getConnection();

// SIMPLE UNLOCK PROCESS
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unlock_content'])) {
    $contentId = intval($_POST['content_id'] ?? 0);
    
    if ($contentId > 0) {
        // Get content details
        $contentQuery = "SELECT * FROM content WHERE id = $contentId";
        $contentResult = $conn->query($contentQuery);
        
        if ($contentResult && $contentResult->num_rows > 0) {
            $content = $contentResult->fetch_assoc();
            $unlockPrice = floatval($content['unlock_price']);
            
            // Check if already unlocked
            $checkQuery = "SELECT id FROM unlocked_content WHERE user_id = $userId AND content_id = $contentId";
            $checkResult = $conn->query($checkQuery);
            
            if ($checkResult && $checkResult->num_rows > 0) {
                $message = 'Content already unlocked!';
            } else {
                $currentBalance = floatval($user['wallet_balance']);
                
                if ($currentBalance >= $unlockPrice) {
                    // Deduct from wallet
                    $updateWallet = "UPDATE users SET wallet_balance = wallet_balance - $unlockPrice WHERE id = $userId";
                    $walletResult = $conn->query($updateWallet);
                    
                    if ($walletResult) {
                        // Add unlock record
                        $unlockQuery = "INSERT INTO unlocked_content (user_id, content_id) VALUES ($userId, $contentId)";
                        $unlockResult = $conn->query($unlockQuery);
                        
                        if ($unlockResult) {
                            $message = 'Content unlocked successfully! ₦' . number_format($unlockPrice, 2) . ' deducted.';
                            $user = getUserById($userId); // Refresh user data
                        } else {
                            $error = 'Failed to unlock content.';
                        }
                    } else {
                        $error = 'Failed to update wallet.';
                    }
                } else {
                    $error = 'Insufficient wallet balance.';
                }
            }
        } else {
            $error = 'Content not found.';
        }
    } else {
        $error = 'Invalid content ID.';
    }
}

// Get available content
$contentResult = $conn->query("SELECT * FROM content ORDER BY surah_number");
$availableContent = [];
while ($row = $contentResult->fetch_assoc()) {
    $availableContent[] = $row;
}

// Get unlocked content
$unlockedResult = $conn->query("SELECT content_id FROM unlocked_content WHERE user_id = $userId");
$unlockedContent = [];
while ($row = $unlockedResult->fetch_assoc()) {
    $unlockedContent[] = $row['content_id'];
}

// Get selected content for Recitation Engine
$selectedContent = null;
if (isset($_GET['selected_content']) && !empty($_GET['selected_content'])) {
    $selectedContentId = intval($_GET['selected_content']);
    foreach ($availableContent as $content) {
        if ($content['id'] == $selectedContentId && in_array($content['id'], $unlockedContent)) {
            $selectedContent = $content;
            break;
        }
    }
}

// Get user recordings
$recordingsResult = $conn->query("SELECT * FROM screen_records WHERE user_id = $userId ORDER BY created_at DESC LIMIT 5");
$userRecordings = [];
while ($row = $recordingsResult->fetch_assoc()) {
    $userRecordings[] = $row;
}

$mirrorResult = $conn->query("SELECT * FROM mirror_recordings WHERE user_id = $userId ORDER BY created_at DESC LIMIT 5");
$mirrorRecordings = [];
while ($row = $mirrorResult->fetch_assoc()) {
    $mirrorRecordings[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }

        .header-center {
            flex: 1;
            max-width: 300px;
            margin: 0 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.75rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .welcome-info h2 {
            font-size: 1.1rem;
            margin: 0;
            font-weight: 600;
        }

        .welcome-info p {
            font-size: 0.8rem;
            margin: 0;
            opacity: 0.9;
        }

        .balance-info {
            text-align: right;
        }

        .balance-amount {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        .balance-toggle {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            opacity: 0.8;
            margin-left: 0.5rem;
        }

        .balance-toggle:hover {
            opacity: 1;
        }

        /* Messages */
        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-header {
            background: var(--secondary);
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text);
        }

        .card-body {
            padding: 1rem;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 44px; /* Touch-friendly */
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: #333;
        }

        .btn-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
            min-height: 36px;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        /* Content List */
        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border);
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-info h6 {
            margin: 0;
            font-weight: 600;
        }

        .content-info small {
            color: #666;
        }

        /* Video Container */
        .video-container {
            margin: 1rem 0;
        }

        .video-placeholder {
            background: var(--secondary);
            border: 2px dashed var(--border);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            color: #666;
        }

        iframe {
            width: 100%;
            height: 200px;
            border: none;
            border-radius: 8px;
        }

        /* Arabic Text */
        .arabic-text {
            direction: rtl;
            text-align: right;
            font-size: 1.2rem;
            line-height: 2;
            padding: 1rem;
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin: 1rem 0;
        }

        /* Translation */
        .translation {
            background: #f0f8f0;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Form Controls */
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 0.9rem;
            background: white;
            min-height: 44px; /* Touch-friendly */
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(26, 95, 63, 0.1);
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 768px;
                margin: 0 auto;
            }

            .grid-2 {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 767px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 1.3rem;
            }

            .user-info {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='profile.php'">
                    <?php if (!empty($user['profile_picture'])): ?>
                        <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>"
                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <?php else: ?>
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="header-center">
                <input type="text" class="search-input" placeholder="Search Surahs, recordings...">
            </div>

            <div class="header-right">
                <div class="notification-icon" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <div class="notification-badge">3</div>
                </div>
            </div>
        </div>

        <div class="welcome-card">
            <div class="welcome-info">
                <h2>Welcome back, <?php echo htmlspecialchars(explode(' ', $user['full_name'])[0]); ?>!</h2>
                <p><i class="fas fa-book-quran"></i> Continue your Qur'an journey</p>
            </div>
            <div class="balance-info">
                <div class="balance-amount" id="balance-display" data-balance="₦<?php echo number_format($user['wallet_balance'], 2); ?>">
                    ₦<?php echo number_format($user['wallet_balance'], 2); ?>
                </div>
                <button class="balance-toggle" onclick="toggleBalance()">
                    <i class="fas fa-eye" id="balance-eye"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="message success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Dashboard Grid -->
        <div class="grid grid-2">
            <!-- Selfie Mirror -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-video"></i> Selfie Mirror
                    </div>
                </div>
                <div class="card-body">
                    <div class="video-container">
                        <video id="selfie-video" style="width: 100%; height: 200px; background: #000; border-radius: 8px; display: none;"></video>
                        <div id="camera-placeholder" class="video-placeholder">
                            <i class="fas fa-camera" style="font-size: 2rem;"></i>
                            <p>Start camera to practice</p>
                        </div>
                    </div>

                    <div class="grid grid-2" style="margin-top: 1rem;">
                        <button id="start-camera-btn" class="btn btn-primary" onclick="startCamera()">
                            <i class="fas fa-camera"></i> Start Camera
                        </button>
                        <button id="stop-camera-btn" class="btn btn-warning" onclick="stopCamera()" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Camera
                        </button>
                    </div>

                    <div class="grid grid-2" style="margin-top: 0.5rem;">
                        <button id="start-record-btn" class="btn btn-success" onclick="startRecording()" style="display: none;">
                            <i class="fas fa-record-vinyl"></i> Record
                        </button>
                        <button id="stop-record-btn" class="btn btn-warning" onclick="stopRecording()" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Record
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recitation Engine -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-play-circle"></i> Recitation Engine
                    </div>
                </div>
                <div class="card-body">
                    <!-- Content Selection -->
                    <?php if (!empty($unlockedContent)): ?>
                        <form method="GET" style="margin-bottom: 1rem;">
                            <select name="selected_content" class="form-control" onchange="this.form.submit()">
                                <option value="">-- Choose a Surah --</option>
                                <?php foreach ($availableContent as $content): ?>
                                    <?php if (in_array($content['id'], $unlockedContent)): ?>
                                        <option value="<?php echo $content['id']; ?>"
                                                <?php echo (isset($_GET['selected_content']) && $_GET['selected_content'] == $content['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($content['surah_name']); ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </form>
                    <?php endif; ?>

                    <!-- Video Display -->
                    <div class="video-container">
                        <?php if ($selectedContent): ?>
                            <iframe src="https://www.youtube.com/embed/<?php echo htmlspecialchars($selectedContent['youtube_id']); ?>?rel=0" allowfullscreen></iframe>
                        <?php else: ?>
                            <div class="video-placeholder">
                                <i class="fab fa-youtube" style="font-size: 2rem; color: #FF0000;"></i>
                                <p><?php echo !empty($unlockedContent) ? 'Select a Surah above' : 'Unlock content to watch videos'; ?></p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Arabic Text and Translation -->
                    <?php if ($selectedContent): ?>
                        <div class="arabic-text">
                            <?php echo $selectedContent['arabic_text']; ?>
                        </div>

                        <?php if (!empty($selectedContent['translation'])): ?>
                            <div class="translation">
                                <strong><i class="fas fa-language"></i> Translation:</strong><br>
                                <?php echo $selectedContent['translation']; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Content Library -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-book"></i> Content Library
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <small><?php echo count($unlockedContent); ?>/<?php echo count($availableContent); ?></small>
                        <a href="content_library.php" class="btn btn-sm" style="background: var(--primary); color: white; text-decoration: none;">
                            <i class="fas fa-eye"></i> View More
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                    $displayContent = array_slice($availableContent, 0, 3); // Show only first 3
                    foreach ($displayContent as $content):
                    ?>
                        <div class="content-item">
                            <div class="content-info">
                                <h6><?php echo htmlspecialchars($content['surah_name']); ?></h6>
                                <small>Surah <?php echo $content['surah_number']; ?></small>
                            </div>
                            <div>
                                <?php if (in_array($content['id'], $unlockedContent)): ?>
                                    <span class="btn btn-success btn-sm">
                                        <i class="fas fa-unlock"></i> Unlocked
                                    </span>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="content_id" value="<?php echo $content['id']; ?>">
                                        <input type="hidden" name="unlock_content" value="1">
                                        <button type="submit" class="btn btn-primary btn-sm"
                                                onclick="return confirm('Unlock <?php echo htmlspecialchars($content['surah_name']); ?> for ₦<?php echo number_format($content['unlock_price']); ?>?');">
                                            <i class="fas fa-lock"></i> ₦<?php echo number_format($content['unlock_price']); ?>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <?php if (count($availableContent) > 3): ?>
                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="content_library.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> View All <?php echo count($availableContent); ?> Surahs
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Your Recordings with Performance Ranking -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-trophy"></i> Top Recordings & Performance
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <?php
                        // Include points system
                        require_once 'includes/points_system.php';
                        $userPoints = getUserPointsSummary($userId);
                        $userRank = getUserRanking($userId);
                        ?>
                        <small>Rank: #<?php echo $userRank ?? 'N/A'; ?></small>
                        <small><?php echo $userPoints['total_points']; ?> pts</small>
                        <a href="leaderboard.php" class="btn btn-sm" style="background: #ff6b35; color: white; text-decoration: none;">
                            <i class="fas fa-trophy"></i> Leaderboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Performance Stats -->
                    <div class="performance-stats" style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                        <div class="grid grid-2" style="gap: 1rem;">
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: var(--primary);">
                                    <?php echo $userPoints['total_points']; ?>
                                </div>
                                <small>Total Points</small>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #ff6b35;">
                                    <?php echo $userPoints['current_streak']; ?>
                                </div>
                                <small>Current Streak</small>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 0.5rem;">
                            <small>🏆 Best Streak: <?php echo $userPoints['best_streak']; ?> | 📊 Total Recitations: <?php echo $userPoints['total_recitations']; ?></small>
                        </div>
                    </div>

                    <!-- Top Recordings by Performance -->
                    <?php
                    // Get user's recordings with performance data
                    $performanceQuery = "
                        SELECT sr.*, re.accuracy_score, re.duration_seconds, re.points_earned,
                               DATE_FORMAT(sr.created_at, '%M %d, %Y') as formatted_date
                        FROM screen_records sr
                        LEFT JOIN recitation_evaluations re ON sr.id = re.recording_id AND re.recording_type = 'screen_record'
                        WHERE sr.user_id = ?
                        ORDER BY re.points_earned DESC, re.accuracy_score DESC, re.duration_seconds DESC
                        LIMIT 5
                    ";
                    $stmt = $conn->prepare($performanceQuery);
                    $stmt->bind_param("i", $userId);
                    $stmt->execute();
                    $topRecordings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
                    ?>

                    <?php if (!empty($topRecordings)): ?>
                        <h6 style="margin-bottom: 1rem;"><i class="fas fa-star"></i> Your Best Performances</h6>
                        <?php foreach ($topRecordings as $index => $recording): ?>
                            <div class="content-item" style="border-left: 3px solid <?php echo $index === 0 ? '#ffd700' : ($index === 1 ? '#c0c0c0' : ($index === 2 ? '#cd7f32' : 'var(--primary)')); ?>; padding-left: 1rem;">
                                <div class="content-info">
                                    <div style="display: flex; justify-content: space-between; align-items: start;">
                                        <div>
                                            <h6 style="margin: 0;">
                                                <?php if ($index < 3): ?>
                                                    <span style="color: <?php echo $index === 0 ? '#ffd700' : ($index === 1 ? '#c0c0c0' : '#cd7f32'); ?>;">
                                                        <?php echo $index === 0 ? '🥇' : ($index === 1 ? '🥈' : '🥉'); ?>
                                                    </span>
                                                <?php endif; ?>
                                                <?php echo htmlspecialchars($recording['title'] ?? 'Recording'); ?>
                                            </h6>
                                            <small><?php echo $recording['formatted_date']; ?></small>
                                        </div>
                                        <div style="text-align: right;">
                                            <?php if ($recording['points_earned']): ?>
                                                <div style="background: var(--primary); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.7rem; margin-bottom: 0.25rem;">
                                                    +<?php echo $recording['points_earned']; ?> pts
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($recording['accuracy_score']): ?>
                                                <div style="font-size: 0.7rem; color: #666;">
                                                    <?php echo $recording['accuracy_score']; ?>% accuracy
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($recording['duration_seconds']): ?>
                                                <div style="font-size: 0.7rem; color: #666;">
                                                    <?php echo gmdate("i:s", $recording['duration_seconds']); ?> duration
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <a href="<?php echo htmlspecialchars($recording['file_path']); ?>" class="btn btn-primary btn-sm" target="_blank">
                                    <i class="fas fa-play"></i> Play
                                </a>
                            </div>
                        <?php endforeach; ?>

                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="streams.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-stream"></i> View All Recordings
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="video-placeholder">
                            <i class="fas fa-video" style="font-size: 2rem;"></i>
                            <p>No recordings yet. Start practicing to earn points!</p>
                            <small style="color: #666; margin-top: 0.5rem; display: block;">
                                🏆 Earn points by: Completing recitations • Maintaining streaks • Getting likes/comments
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="community.php" class="nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="streams.php" class="nav-item">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <!-- Simple JavaScript for Camera -->
    <script>
        let currentStream = null;
        let mediaRecorder = null;
        let recordedChunks = [];

        // Start Camera
        function startCamera() {
            navigator.mediaDevices.getUserMedia({ video: true, audio: true })
                .then(function(stream) {
                    currentStream = stream;
                    const video = document.getElementById('selfie-video');
                    const placeholder = document.getElementById('camera-placeholder');

                    video.srcObject = stream;
                    video.play();
                    video.style.display = 'block';
                    placeholder.style.display = 'none';

                    document.getElementById('start-camera-btn').style.display = 'none';
                    document.getElementById('stop-camera-btn').style.display = 'block';
                    document.getElementById('start-record-btn').style.display = 'block';
                })
                .catch(function(error) {
                    alert('Camera access denied or not available');
                    console.error('Camera error:', error);
                });
        }

        // Stop Camera
        function stopCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }

            const video = document.getElementById('selfie-video');
            const placeholder = document.getElementById('camera-placeholder');

            video.style.display = 'none';
            placeholder.style.display = 'block';

            document.getElementById('start-camera-btn').style.display = 'block';
            document.getElementById('stop-camera-btn').style.display = 'none';
            document.getElementById('start-record-btn').style.display = 'none';
            document.getElementById('stop-record-btn').style.display = 'none';
        }

        // Start Recording
        function startRecording() {
            if (currentStream) {
                recordedChunks = [];
                mediaRecorder = new MediaRecorder(currentStream);

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = function() {
                    const blob = new Blob(recordedChunks, { type: 'video/webm' });
                    const filename = 'recitation-' + new Date().getTime() + '.webm';

                    // Save to database
                    saveRecordingToDatabase(blob, filename);

                    // Also create download link
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();

                    URL.revokeObjectURL(url);
                };

                mediaRecorder.start();

                document.getElementById('start-record-btn').style.display = 'none';
                document.getElementById('stop-record-btn').style.display = 'block';
            }
        }

        // Stop Recording
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();

                document.getElementById('start-record-btn').style.display = 'block';
                document.getElementById('stop-record-btn').style.display = 'none';
            }
        }

        // Save recording to database
        function saveRecordingToDatabase(blob, filename) {
            const formData = new FormData();
            formData.append('recording', blob, filename);
            formData.append('action', 'save_recording');

            fetch('save_recording.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Recording saved successfully!');
                    // Refresh the page to show new recording
                    window.location.reload();
                } else {
                    alert('Failed to save recording: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error saving recording:', error);
                alert('Error saving recording');
            });
        }

        // Toggle balance visibility
        let balanceVisible = true;
        function toggleBalance() {
            const balanceDisplay = document.getElementById('balance-display');
            const eyeIcon = document.getElementById('balance-eye');

            if (balanceVisible) {
                balanceDisplay.textContent = '****';
                eyeIcon.className = 'fas fa-eye-slash';
                balanceVisible = false;
            } else {
                balanceDisplay.textContent = '₦<?php echo number_format($user['wallet_balance'], 2); ?>';
                eyeIcon.className = 'fas fa-eye';
                balanceVisible = true;
            }
        }

        // Show notifications
        function showNotifications() {
            alert('Notifications:\n• New Surah unlocked!\n• Recording saved successfully\n• Welcome to RECITE!');
        }
    </script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
