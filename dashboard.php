<?php
/**
 * User Dashboard for Recite! App
 * Clean, Mobile-First Dashboard with Simple Functionality
 */

require_once 'config/database.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Dashboard';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Initialize variables
$message = '';
$error = '';
$conn = getConnection();

// SIMPLE UNLOCK PROCESS
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unlock_content'])) {
    $contentId = intval($_POST['content_id'] ?? 0);
    
    if ($contentId > 0) {
        // Get content details
        $contentQuery = "SELECT * FROM content WHERE id = $contentId";
        $contentResult = $conn->query($contentQuery);
        
        if ($contentResult && $contentResult->num_rows > 0) {
            $content = $contentResult->fetch_assoc();
            $unlockPrice = floatval($content['unlock_price']);
            
            // Check if already unlocked
            $checkQuery = "SELECT id FROM unlocked_content WHERE user_id = $userId AND content_id = $contentId";
            $checkResult = $conn->query($checkQuery);
            
            if ($checkResult && $checkResult->num_rows > 0) {
                $message = 'Content already unlocked!';
            } else {
                $currentBalance = floatval($user['wallet_balance']);
                
                if ($currentBalance >= $unlockPrice) {
                    // Deduct from wallet
                    $updateWallet = "UPDATE users SET wallet_balance = wallet_balance - $unlockPrice WHERE id = $userId";
                    $walletResult = $conn->query($updateWallet);
                    
                    if ($walletResult) {
                        // Add unlock record
                        $unlockQuery = "INSERT INTO unlocked_content (user_id, content_id) VALUES ($userId, $contentId)";
                        $unlockResult = $conn->query($unlockQuery);
                        
                        if ($unlockResult) {
                            $message = 'Content unlocked successfully! ₦' . number_format($unlockPrice, 2) . ' deducted.';
                            $user = getUserById($userId); // Refresh user data
                        } else {
                            $error = 'Failed to unlock content.';
                        }
                    } else {
                        $error = 'Failed to update wallet.';
                    }
                } else {
                    $error = 'Insufficient wallet balance.';
                }
            }
        } else {
            $error = 'Content not found.';
        }
    } else {
        $error = 'Invalid content ID.';
    }
}

// Get available content
$contentResult = $conn->query("SELECT * FROM content ORDER BY surah_number");
$availableContent = [];
while ($row = $contentResult->fetch_assoc()) {
    $availableContent[] = $row;
}

// Get unlocked content
$unlockedResult = $conn->query("SELECT content_id FROM unlocked_content WHERE user_id = $userId");
$unlockedContent = [];
while ($row = $unlockedResult->fetch_assoc()) {
    $unlockedContent[] = $row['content_id'];
}

// Get selected content for Recitation Engine
$selectedContent = null;
if (isset($_GET['selected_content']) && !empty($_GET['selected_content'])) {
    $selectedContentId = intval($_GET['selected_content']);
    foreach ($availableContent as $content) {
        if ($content['id'] == $selectedContentId && in_array($content['id'], $unlockedContent)) {
            $selectedContent = $content;
            break;
        }
    }
}

// Get user recordings
$recordingsResult = $conn->query("SELECT * FROM screen_records WHERE user_id = $userId ORDER BY created_at DESC LIMIT 5");
$userRecordings = [];
while ($row = $recordingsResult->fetch_assoc()) {
    $userRecordings[] = $row;
}

$mirrorResult = $conn->query("SELECT * FROM mirror_recordings WHERE user_id = $userId ORDER BY created_at DESC LIMIT 5");
$mirrorRecordings = [];
while ($row = $mirrorResult->fetch_assoc()) {
    $mirrorRecordings[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .user-info {
            font-size: 0.9rem;
            margin-top: 0.5rem;
            opacity: 0.9;
        }

        /* Messages */
        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-header {
            background: var(--secondary);
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text);
        }

        .card-body {
            padding: 1rem;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 44px; /* Touch-friendly */
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: #333;
        }

        .btn-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
            min-height: 36px;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        /* Content List */
        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border);
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-info h6 {
            margin: 0;
            font-weight: 600;
        }

        .content-info small {
            color: #666;
        }

        /* Video Container */
        .video-container {
            margin: 1rem 0;
        }

        .video-placeholder {
            background: var(--secondary);
            border: 2px dashed var(--border);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            color: #666;
        }

        iframe {
            width: 100%;
            height: 200px;
            border: none;
            border-radius: 8px;
        }

        /* Arabic Text */
        .arabic-text {
            direction: rtl;
            text-align: right;
            font-size: 1.2rem;
            line-height: 2;
            padding: 1rem;
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin: 1rem 0;
        }

        /* Translation */
        .translation {
            background: #f0f8f0;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Form Controls */
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 0.9rem;
            background: white;
            min-height: 44px; /* Touch-friendly */
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(26, 95, 63, 0.1);
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 768px;
                margin: 0 auto;
            }

            .grid-2 {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 767px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 1.3rem;
            }

            .user-info {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-book-quran"></i> Qur'an Recite</h1>
        <div class="user-info">
            Welcome, <?php echo htmlspecialchars($user['full_name']); ?> |
            Wallet: ₦<?php echo number_format($user['wallet_balance'], 2); ?>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="message success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Dashboard Grid -->
        <div class="grid grid-2">
            <!-- Content Library -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-book"></i> Content Library
                    </div>
                    <small><?php echo count($unlockedContent); ?>/<?php echo count($availableContent); ?></small>
                </div>
                <div class="card-body">
                    <?php foreach ($availableContent as $content): ?>
                        <div class="content-item">
                            <div class="content-info">
                                <h6><?php echo htmlspecialchars($content['surah_name']); ?></h6>
                                <small>Surah <?php echo $content['surah_number']; ?></small>
                            </div>
                            <div>
                                <?php if (in_array($content['id'], $unlockedContent)): ?>
                                    <span class="btn btn-success btn-sm">
                                        <i class="fas fa-unlock"></i> Unlocked
                                    </span>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="content_id" value="<?php echo $content['id']; ?>">
                                        <input type="hidden" name="unlock_content" value="1">
                                        <button type="submit" class="btn btn-primary btn-sm"
                                                onclick="return confirm('Unlock <?php echo htmlspecialchars($content['surah_name']); ?> for ₦<?php echo number_format($content['unlock_price']); ?>?');">
                                            <i class="fas fa-lock"></i> ₦<?php echo number_format($content['unlock_price']); ?>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Recitation Engine -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-play-circle"></i> Recitation Engine
                    </div>
                </div>
                <div class="card-body">
                    <!-- Content Selection -->
                    <?php if (!empty($unlockedContent)): ?>
                        <form method="GET" style="margin-bottom: 1rem;">
                            <select name="selected_content" class="form-control" onchange="this.form.submit()">
                                <option value="">-- Choose a Surah --</option>
                                <?php foreach ($availableContent as $content): ?>
                                    <?php if (in_array($content['id'], $unlockedContent)): ?>
                                        <option value="<?php echo $content['id']; ?>"
                                                <?php echo (isset($_GET['selected_content']) && $_GET['selected_content'] == $content['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($content['surah_name']); ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </form>
                    <?php endif; ?>

                    <!-- Video Display -->
                    <div class="video-container">
                        <?php if ($selectedContent): ?>
                            <iframe src="https://www.youtube.com/embed/<?php echo htmlspecialchars($selectedContent['youtube_id']); ?>?rel=0" allowfullscreen></iframe>
                        <?php else: ?>
                            <div class="video-placeholder">
                                <i class="fab fa-youtube" style="font-size: 2rem; color: #FF0000;"></i>
                                <p><?php echo !empty($unlockedContent) ? 'Select a Surah above' : 'Unlock content to watch videos'; ?></p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Arabic Text and Translation -->
                    <?php if ($selectedContent): ?>
                        <div class="arabic-text">
                            <?php echo $selectedContent['arabic_text']; ?>
                        </div>

                        <?php if (!empty($selectedContent['translation'])): ?>
                            <div class="translation">
                                <strong><i class="fas fa-language"></i> Translation:</strong><br>
                                <?php echo $selectedContent['translation']; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Selfie Mirror -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-video"></i> Selfie Mirror
                    </div>
                </div>
                <div class="card-body">
                    <div class="video-container">
                        <video id="selfie-video" style="width: 100%; height: 200px; background: #000; border-radius: 8px; display: none;"></video>
                        <div id="camera-placeholder" class="video-placeholder">
                            <i class="fas fa-camera" style="font-size: 2rem;"></i>
                            <p>Start camera to practice</p>
                        </div>
                    </div>

                    <div class="grid grid-2" style="margin-top: 1rem;">
                        <button id="start-camera-btn" class="btn btn-primary" onclick="startCamera()">
                            <i class="fas fa-camera"></i> Start Camera
                        </button>
                        <button id="stop-camera-btn" class="btn btn-warning" onclick="stopCamera()" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Camera
                        </button>
                    </div>

                    <div class="grid grid-2" style="margin-top: 0.5rem;">
                        <button id="start-record-btn" class="btn btn-success" onclick="startRecording()" style="display: none;">
                            <i class="fas fa-record-vinyl"></i> Record
                        </button>
                        <button id="stop-record-btn" class="btn btn-warning" onclick="stopRecording()" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Record
                        </button>
                    </div>
                </div>
            </div>

            <!-- Streams -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-stream"></i> Your Recordings
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($userRecordings) || !empty($mirrorRecordings)): ?>
                        <?php foreach (array_merge($userRecordings, $mirrorRecordings) as $recording): ?>
                            <div class="content-item">
                                <div class="content-info">
                                    <h6><?php echo htmlspecialchars($recording['title'] ?? 'Recording'); ?></h6>
                                    <small><?php echo date('M j, Y', strtotime($recording['created_at'])); ?></small>
                                </div>
                                <a href="<?php echo htmlspecialchars($recording['file_path']); ?>" class="btn btn-primary btn-sm" target="_blank">
                                    <i class="fas fa-play"></i> Play
                                </a>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="video-placeholder">
                            <i class="fas fa-video" style="font-size: 2rem;"></i>
                            <p>No recordings yet. Start practicing!</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="streams.php" class="nav-item">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <!-- Simple JavaScript for Camera -->
    <script>
        let currentStream = null;
        let mediaRecorder = null;
        let recordedChunks = [];

        // Start Camera
        function startCamera() {
            navigator.mediaDevices.getUserMedia({ video: true, audio: true })
                .then(function(stream) {
                    currentStream = stream;
                    const video = document.getElementById('selfie-video');
                    const placeholder = document.getElementById('camera-placeholder');

                    video.srcObject = stream;
                    video.play();
                    video.style.display = 'block';
                    placeholder.style.display = 'none';

                    document.getElementById('start-camera-btn').style.display = 'none';
                    document.getElementById('stop-camera-btn').style.display = 'block';
                    document.getElementById('start-record-btn').style.display = 'block';
                })
                .catch(function(error) {
                    alert('Camera access denied or not available');
                    console.error('Camera error:', error);
                });
        }

        // Stop Camera
        function stopCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }

            const video = document.getElementById('selfie-video');
            const placeholder = document.getElementById('camera-placeholder');

            video.style.display = 'none';
            placeholder.style.display = 'block';

            document.getElementById('start-camera-btn').style.display = 'block';
            document.getElementById('stop-camera-btn').style.display = 'none';
            document.getElementById('start-record-btn').style.display = 'none';
            document.getElementById('stop-record-btn').style.display = 'none';
        }

        // Start Recording
        function startRecording() {
            if (currentStream) {
                recordedChunks = [];
                mediaRecorder = new MediaRecorder(currentStream);

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = function() {
                    const blob = new Blob(recordedChunks, { type: 'video/webm' });
                    const filename = 'recitation-' + new Date().getTime() + '.webm';

                    // Save to database
                    saveRecordingToDatabase(blob, filename);

                    // Also create download link
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();

                    URL.revokeObjectURL(url);
                };

                mediaRecorder.start();

                document.getElementById('start-record-btn').style.display = 'none';
                document.getElementById('stop-record-btn').style.display = 'block';
            }
        }

        // Stop Recording
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();

                document.getElementById('start-record-btn').style.display = 'block';
                document.getElementById('stop-record-btn').style.display = 'none';
            }
        }

        // Save recording to database
        function saveRecordingToDatabase(blob, filename) {
            const formData = new FormData();
            formData.append('recording', blob, filename);
            formData.append('action', 'save_recording');

            fetch('save_recording.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Recording saved successfully!');
                    // Refresh the page to show new recording
                    window.location.reload();
                } else {
                    alert('Failed to save recording: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error saving recording:', error);
                alert('Error saving recording');
            });
        }
    </script>
</body>
</html>
