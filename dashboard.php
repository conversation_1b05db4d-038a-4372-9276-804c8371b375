<?php
/**
 * User Dashboard for Recite! App
 * 4-Quadrant Interactive Dashboard as per PRD specifications
 */

$page_title = 'Dashboard';
require_once 'config/database.php';
require_once 'components/user_header.php';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Initialize message variables
$message = '';
$error = '';

// SIMPLE UNLOCK PROCESS - Handle content unlocking
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unlock_content'])) {
    $contentId = intval($_POST['content_id'] ?? 0);

    if ($contentId <= 0) {
        $error = 'Invalid content ID.';
    } else {
        // Get the content details
        $contentQuery = "SELECT * FROM content WHERE id = $contentId";
        $contentResult = $conn->query($contentQuery);

        if ($contentResult && $contentResult->num_rows > 0) {
            $content = $contentResult->fetch_assoc();
            $unlockPrice = floatval($content['unlock_price']);

            // Check if already unlocked
            $checkQuery = "SELECT id FROM unlocked_content WHERE user_id = $userId AND content_id = $contentId";
            $checkResult = $conn->query($checkQuery);

            if ($checkResult && $checkResult->num_rows > 0) {
                $message = 'Content already unlocked!';
            } else {
                // Check wallet balance
                $currentBalance = floatval($user['wallet_balance']);

                if ($currentBalance >= $unlockPrice) {
                    try {
                        // 1. Deduct from wallet
                        $updateWalletQuery = "UPDATE users SET wallet_balance = wallet_balance - $unlockPrice WHERE id = $userId";
                        $walletResult = $conn->query($updateWalletQuery);

                        if ($walletResult) {
                            // 2. Add unlock record
                            $unlockQuery = "INSERT INTO unlocked_content (user_id, content_id) VALUES ($userId, $contentId)";
                            $unlockResult = $conn->query($unlockQuery);

                            if ($unlockResult) {
                                // 3. Log transaction
                                $description = mysqli_real_escape_string($conn, "Unlocked: " . $content['surah_name']);
                                $transactionQuery = "INSERT INTO transactions (user_id, transaction_type, amount, description, status)
                                                    VALUES ($userId, 'unlock', -$unlockPrice, '$description', 'completed')";
                                $conn->query($transactionQuery);

                                // Success message
                                $message = 'Content unlocked successfully! ₦' . number_format($unlockPrice, 2) . ' deducted from your wallet.';

                                // Refresh user data
                                $user = getUserById($userId);

                                // Refresh unlocked content list
                                $unlockedQuery = "SELECT content_id FROM unlocked_content WHERE user_id = $userId";
                                $unlockedResult = $conn->query($unlockedQuery);
                                $unlockedContent = [];
                                while ($row = $unlockedResult->fetch_assoc()) {
                                    $unlockedContent[] = $row['content_id'];
                                }
                            } else {
                                $error = 'Failed to unlock content. Please try again.';
                            }
                        } else {
                            $error = 'Failed to update wallet balance. Please try again.';
                        }
                    } catch (Exception $e) {
                        $error = 'An error occurred during the unlock process. Please try again.';
                    }
                } else {
                    $error = 'Insufficient wallet balance. Please fund your wallet.';
                }
            }
        } else {
            $error = 'Content not found.';
        }
    }
}

// Get user's wallet and points
$conn = getConnection();

// Fetch available content (Surahs)
$contentQuery = "SELECT * FROM content ORDER BY surah_number ASC";
$contentResult = $conn->query($contentQuery);
$availableContent = [];
if ($contentResult) {
    while ($row = $contentResult->fetch_assoc()) {
        $availableContent[] = $row;
    }
}

// Fetch user's unlocked content
$unlockedQuery = "SELECT content_id FROM unlocked_content WHERE user_id = ?";
$stmt = $conn->prepare($unlockedQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$unlockedResult = $stmt->get_result();
$unlockedContent = [];
while ($row = $unlockedResult->fetch_assoc()) {
    $unlockedContent[] = $row['content_id'];
}

// Initialize selected content variable
$selectedContent = null;
if (isset($_GET['selected_content']) && !empty($_GET['selected_content'])) {
    $selectedContentId = intval($_GET['selected_content']);
    foreach ($availableContent as $content) {
        if ($content['id'] == $selectedContentId && in_array($content['id'], $unlockedContent)) {
            $selectedContent = $content;
            break;
        }
    }
}

// Get user's recent recordings
$recordingsQuery = "SELECT * FROM screen_records WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
$stmt = $conn->prepare($recordingsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$recordingsResult = $stmt->get_result();
$userRecordings = [];
while ($row = $recordingsResult->fetch_assoc()) {
    $userRecordings[] = $row;
}

// Get user's mirror recordings (selfie recordings)
$mirrorRecordingsQuery = "SELECT * FROM mirror_recordings WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
$stmt = $conn->prepare($mirrorRecordingsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$mirrorResult = $stmt->get_result();
$mirrorRecordings = [];
while ($row = $mirrorResult->fetch_assoc()) {
    $mirrorRecordings[] = $row;
}

// Get user rankings
$rankingsQuery = "
    SELECT
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance AND u2.ward = u1.ward) as ward_rank,
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance AND u2.lga = u1.lga) as lga_rank,
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance AND u2.state = u1.state) as state_rank,
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance) as country_rank
    FROM users u1 WHERE u1.id = ?
";
$stmt = $conn->prepare($rankingsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$rankings = $stmt->get_result()->fetch_assoc();
?>

<style>
/* ===== DASHBOARD - DARK GREEN DESIGN SYSTEM ===== */

/* Reset Everything */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Use Design System Variables */
:root {
    /* Primary Colors - Dark Green Theme */
    --primary: #1a5f3f;
    --primary-light: #2d7a5a;
    --primary-dark: #0f3d28;
    --primary-hover: #164d34;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Status Colors */
    --success: #10b981;
    --danger: #ef4444;
    --warning: #f59e0b;
    --info: #3b82f6;

    /* Design Tokens */
    --radius: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    font-size: 16px;
}

/* Main Dashboard Container - Full Width with Bottom Navigation */
.dashboard-main {
    width: 100%;
    min-height: calc(100vh - 160px); /* Account for header + bottom nav */
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    padding: 1rem;
    margin-left: 0;
    margin-top: 0; /* Remove excessive margin */
    padding-bottom: 100px; /* Space for bottom navigation */
    position: relative;
}

/* Mobile-First Grid Container */
.grid-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 1600px;
    margin: 0 auto;
    width: 100%;
}

/* Responsive Grid Layout */
@media (min-width: 640px) {
    .dashboard-main {
        padding: 1.5rem;
        padding-bottom: 100px;
    }

    .grid-container {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .dashboard-main {
        padding: 2rem;
        padding-bottom: 100px;
    }

    .grid-container {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 2rem;
        height: auto; /* Remove fixed height */
        min-height: calc(100vh - 200px);
    }
}

/* Desktop Layout */
@media (min-width: 768px) {
    .dashboard-main {
        padding: 2rem;
        padding-bottom: 100px; /* Space for bottom navigation */
        margin-top: 0; /* Remove excessive margin */
        min-height: calc(100vh - 180px);
    }
}

/* Card Base Styles */
.dashboard-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    min-height: 450px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: visible; /* Allow content to be visible */
    transition: var(--transition);
    width: 100%;
    box-sizing: border-box;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
    border-color: var(--primary);
}

/* Card Headers */
.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
    flex-shrink: 0;
}

.card-icon {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(26, 95, 63, 0.3);
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    line-height: 1.3;
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-top: 0.25rem;
}

/* Card Content */
.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: hidden; /* Prevent content from breaking out */
}

/* Content Library Styles */
.content-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;
    flex: 1;
}

.content-list::-webkit-scrollbar {
    width: 6px;
}

.content-list::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

.content-list::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 3px;
}

.content-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius);
    transition: var(--transition);
    position: relative;
    min-height: 60px;
}

.content-item:hover {
    background: var(--white);
    border-color: var(--primary);
    box-shadow: var(--shadow);
    transform: translateX(4px);
}

.content-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.content-info small {
    color: var(--primary);
    font-weight: 500;
    font-size: 0.875rem;
}

/* Action Buttons */
.action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    display: flex;
    align-items: center;
    gap: 0.4rem;
}

.btn-unlock {
    background: var(--warning);
    color: var(--white);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.btn-unlock:hover {
    background: #d97706;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.btn-start {
    background: var(--primary);
    color: var(--white);
    box-shadow: 0 2px 8px rgba(26, 95, 63, 0.3);
}

.btn-start:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(26, 95, 63, 0.4);
}

/* Video Player */
.video-container {
    background: var(--gray-900);
    border-radius: var(--radius-lg);
    overflow: hidden;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: var(--shadow-lg);
    flex-shrink: 0;
    margin-top: 1rem;
}

.video-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: var(--radius-lg);
}

.video-placeholder {
    color: var(--gray-400);
    font-style: italic;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.video-placeholder i {
    font-size: 3rem;
    opacity: 0.5;
}

/* Recitation Engine Styles */
.recitation-display {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    border: 2px solid var(--primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Amiri', serif;
    font-size: 1.5rem;
    line-height: 2;
    text-align: center;
    direction: rtl;
    box-shadow: inset 0 2px 4px rgba(26, 95, 63, 0.1);
    flex-shrink: 0;
    overflow-y: auto;
}

.arabic-word {
    padding: 6px 12px;
    border-radius: var(--radius);
    margin: 0 6px;
    transition: var(--transition);
    cursor: pointer;
}

.arabic-word:hover {
    background: var(--gray-100);
}

.arabic-word.correct {
    background: var(--success);
    color: var(--white);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.arabic-word.error {
    background: var(--danger);
    color: var(--white);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.arabic-word.current {
    background: var(--primary);
    color: var(--white);
    animation: pulse 2s infinite;
    box-shadow: 0 2px 8px rgba(26, 95, 63, 0.4);
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
    100% { opacity: 1; transform: scale(1); }
}

.recitation-status {
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    text-align: center;
    font-weight: 600;
    margin-bottom: 1.5rem;
    background: var(--gray-100);
    color: var(--gray-700);
    border: 2px solid var(--gray-200);
    transition: var(--transition);
}

.status-listening {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    color: var(--white);
    animation: pulse 2s infinite;
    border-color: var(--success);
}

.status-recording {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
    animation: pulse 2s infinite;
    border-color: var(--danger);
}

.recitation-controls {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin: 1.5rem 0;
    flex-wrap: wrap;
}

.control-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 140px;
    justify-content: center;
}

.btn-start-recite {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-start-recite:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.btn-start-recite:disabled {
    background: var(--gray-400);
    color: var(--gray-600);
    cursor: not-allowed;
    box-shadow: none;
}

.btn-stop-recite {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-stop-recite:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

/* Selfie Mirror */
.selfie-container {
    background: var(--gray-900);
    border-radius: var(--radius-lg);
    overflow: hidden;
    height: 250px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    flex-shrink: 0;
}

.selfie-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scaleX(-1);
    border-radius: var(--radius-lg);
}

.camera-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-align: center;
    padding: 2rem;
    gap: 1rem;
}

.camera-placeholder i {
    font-size: 3rem;
    opacity: 0.5;
}

.camera-btn {
    padding: 0.6rem 1.2rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 1rem;
    box-shadow: 0 4px 12px rgba(26, 95, 63, 0.3);
}

.camera-btn:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(26, 95, 63, 0.4);
}

/* Rankings & Stats */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 640px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.stat-item {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    text-align: center;
    border: 2px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

.stat-item:hover {
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rankings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ranking-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem;
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius);
    transition: var(--transition);
    position: relative;
}

.ranking-item:hover {
    background: var(--white);
    border-color: var(--primary);
    box-shadow: var(--shadow);
    transform: translateX(4px);
}

.rank-label {
    font-weight: 600;
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rank-label i {
    color: var(--primary);
}

.rank-value {
    font-weight: 700;
    color: var(--primary);
    font-size: 1.25rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Design */

/* Tablet Layout */
@media (min-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        padding: 0;
    }

    .dashboard-card {
        min-height: 480px;
        padding: 1.75rem;
    }
}

/* Desktop - Full 4-quadrant grid */
@media (min-width: 1024px) {
    .dashboard-main {
        padding: 2rem;
        margin-top: 0; /* Remove excessive margin */
        padding-bottom: 100px; /* Space for bottom navigation */
        min-height: calc(100vh - 180px);
    }

    .grid-container {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto;
        gap: 2rem;
        max-width: none;
    }

    .dashboard-card {
        min-height: 500px;
        height: auto; /* Allow natural height */
    }
}

/* Large Desktop - Optimized spacing */
@media (min-width: 1200px) {
    .dashboard-main {
        padding: 2.5rem;
        padding-bottom: 100px; /* Space for bottom navigation */
    }

    .grid-container {
        gap: 2.5rem;
    }

    .dashboard-card {
        min-height: 550px;
        padding: 2rem;
    }
}

/* Mobile Layout */
@media (max-width: 767px) {
    .dashboard-main {
        padding: 1rem;
        padding-bottom: 100px; /* Space for bottom navigation */
    }

    /* Stack cards vertically on mobile */
    .grid-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        height: auto;
    }

    .dashboard-card {
        min-height: 400px;
        padding: 1.25rem;
    }

    .card-header {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .card-title {
        font-size: 1rem;
    }

    .content-list {
        max-height: 250px;
    }

    .content-item {
        padding: 0.75rem;
        min-height: 50px;
    }

    .video-container {
        height: 180px;
    }

    .selfie-container {
        height: 200px;
    }

    .recitation-display {
        height: 120px;
        padding: 1rem;
        font-size: 1.25rem;
    }
}

/* Progress Stats */
.progress-stats {
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-row .stat-label {
    font-weight: 500;
    color: var(--gray-700);
}

.stat-row .stat-value {
    font-weight: 700;
    color: var(--primary);
    font-size: 1.125rem;
}

/* Recitation Video Container */
.recitation-video-container {
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.recitation-video-container .video-placeholder {
    text-align: center;
    padding: 1rem;
}

.recitation-video-container .video-frame {
    transition: all 0.3s ease;
    cursor: pointer;
}

.recitation-video-container .video-frame:hover {
    border-color: var(--primary);
    background: var(--gray-50);
}

.recitation-video-container iframe {
    border-radius: var(--radius-md);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Admin Video Container */
.admin-video-container {
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.admin-video-container .video-placeholder {
    text-align: center;
    padding: 1rem;
}

.admin-video-container .video-frame {
    transition: all 0.3s ease;
    cursor: pointer;
}

.admin-video-container .video-frame:hover {
    border-color: var(--primary);
    background: var(--gray-50);
}

.admin-video-container iframe {
    border-radius: var(--radius-md);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}













</style>

<!-- FRESH MOBILE-FIRST DASHBOARD -->
<div class="dashboard-main">
    <div class="grid-container">

        <!-- Quadrant 1: Selfie Mirror -->
        <div class="dashboard-card" id="selfie-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-camera"></i>
                </div>
                <div>
                    <h2 class="card-title">Selfie Mirror</h2>
                    <!-- <p class="card-subtitle">Monitor your recitation posture</p> -->
                </div>
            </div>
            <div class="card-content">
                <div class="selfie-container">
                    <video id="selfie-video" class="selfie-video" autoplay muted style="display: none;"></video>
                    <div class="camera-placeholder" id="camera-placeholder">
                        <i class="fas fa-camera"></i>
                        <p style="margin-bottom: 0.5rem;">Camera not active</p>
                        <button class="camera-btn" onclick="startCamera()">
                            <i class="fas fa-video"></i>
                            Start Camera
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quadrant 2: Recitation Engine -->
        <div class="dashboard-card" id="recitation-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-microphone"></i>
                </div>
                <div>
                    <h2 class="card-title">Recitation Engine</h2>
                    <!-- <p class="card-subtitle">Practice your Qur'an recitation</p> -->
                </div>
                <div class="refresh-button" style="margin-left: auto;">
                    <a href="dashboard.php" class="btn-refresh" style="display: inline-flex; align-items: center; justify-content: center; width: 32px; height: 32px; border-radius: 50%; background: var(--gray-100); color: var(--primary); text-decoration: none;">
                        <i class="fas fa-sync-alt"></i>
                    </a>
                </div>
            </div>
            <div class="card-content">
                <!-- SIMPLE Content Selection -->
                <div class="content-selection" style="margin-bottom: 1rem;">
                    <?php if (!empty($unlockedContent)): ?>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--text);">
                            <i class="fas fa-book-quran"></i> Select Unlocked Content:
                        </label>
                        <form method="GET" action="dashboard.php" style="margin-bottom: 1rem;">
                            <select name="selected_content" class="form-control" style="width: 100%; padding: 0.5rem; border: 1px solid var(--gray-300); border-radius: 4px; background: white;" onchange="this.form.submit()">
                                <option value="">-- Choose a Surah --</option>
                                <?php
                                // Get unlocked content details
                                foreach ($availableContent as $content):
                                    if (in_array($content['id'], $unlockedContent)):
                                        $selected = (isset($_GET['selected_content']) && $_GET['selected_content'] == $content['id']) ? 'selected' : '';
                                ?>
                                    <option value="<?php echo $content['id']; ?>" <?php echo $selected; ?>>
                                        <?php echo htmlspecialchars($content['surah_name']); ?> (Surah <?php echo $content['surah_number']; ?>)
                                    </option>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </select>
                        </form>
                    <?php else: ?>
                        <div class="no-content-message" style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
                            <i class="fas fa-lock" style="font-size: 2rem; color: var(--gray-400); margin-bottom: 0.5rem;"></i>
                            <p style="margin: 0; color: var(--gray-500);">No unlocked content available. Please unlock Surahs from the Content Library first.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- <div class="recitation-status" id="recitation-status">
                    Ready to start recitation
                </div> -->

                <div class="recitation-display" id="recitation-display">
                    <?php if ($selectedContent): ?>
                        <!-- Show Arabic Text -->
                        <div class="content-display" style="background: #f8f9fa; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem;">
                            <div class="arabic-text" style="direction: rtl; text-align: right; font-size: 1.4rem; line-height: 2.5; padding: 1rem; background: white; border-radius: 8px; border-right: 4px solid var(--primary); font-family: 'Amiri', 'Times New Roman', serif; margin-bottom: 1rem;">
                                <?php echo $selectedContent['arabic_text']; ?>
                            </div>

                            <?php if (!empty($selectedContent['translation'])): ?>
                                <div class="translation-text" style="direction: ltr; text-align: left; font-size: 1rem; line-height: 1.8; padding: 1rem; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745; color: #2d5a2d;">
                                    <h6 style="margin: 0 0 0.5rem 0; color: #28a745; font-weight: 600;">
                                        <i class="fas fa-language"></i> Translation:
                                    </h6>
                                    <?php echo $selectedContent['translation']; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php elseif (!empty($userRecordings) || !empty($mirrorRecordings)): ?>
                        <div class="user-recordings-section">
                            <h5 style="color: var(--text); margin-bottom: 1rem;">
                                <i class="fas fa-video"></i> Your Recent Recordings
                            </h5>
                            <div class="recordings-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <?php foreach (array_merge($userRecordings, $mirrorRecordings) as $recording): ?>
                                    <div class="recording-item" style="border: 1px solid var(--gray-200); border-radius: 8px; padding: 1rem; background: var(--white);">
                                        <div class="recording-preview" style="aspect-ratio: 16/9; background: var(--gray-100); border-radius: 4px; margin-bottom: 0.5rem; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-play-circle" style="font-size: 2rem; color: var(--primary); cursor: pointer;" onclick="playRecording('<?php echo htmlspecialchars($recording['file_path']); ?>')"></i>
                                        </div>
                                        <h6 style="margin: 0; font-size: 0.8rem; color: var(--text);"><?php echo htmlspecialchars($recording['title'] ?? 'Recording'); ?></h6>
                                        <small style="color: var(--gray-500);"><?php echo date('M j, Y', strtotime($recording['created_at'])); ?></small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Show Placeholder -->
                        <div style="text-align: center; padding: 2rem; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
                            <i class="fas fa-book-quran" style="font-size: 2rem; color: var(--primary); margin-bottom: 1rem;"></i>
                            <p style="color: var(--gray-500); font-style: italic; margin: 0;">
                                <?php if (!empty($unlockedContent)): ?>
                                    Select a Surah from the dropdown above to begin recitation practice
                                <?php else: ?>
                                    Unlock Surahs from the Content Library to access recitation content
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- SIMPLE YouTube Video Display -->
                <div class="recitation-video-container" style="margin: 1rem 0;">

                    <?php if ($selectedContent): ?>
                        <!-- Show YouTube Video -->
                        <div style="margin-bottom: 1rem;">
                            <iframe src="https://www.youtube.com/embed/<?php echo htmlspecialchars($selectedContent['youtube_id']); ?>?rel=0&modestbranding=1"
                                    style="width: 100%; height: 250px; border: none; border-radius: 8px;"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    <?php else: ?>
                        <!-- Show Placeholder -->
                        <div style="text-align: center; padding: 1rem; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; margin-bottom: 1rem;">
                            <i class="fab fa-youtube" style="font-size: 3rem; color: #FF0000; margin-bottom: 1rem;"></i>
                            <h4 style="margin-bottom: 0.5rem; color: var(--text);">Tutorial Video</h4>
                            <p style="color: var(--gray-500); font-size: 0.9rem; margin: 0;">
                                <?php if (!empty($unlockedContent)): ?>
                                    Select a Surah from the dropdown above to watch the tutorial video
                                <?php else: ?>
                                    Unlock Surahs from the Content Library to access tutorial videos
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="recitation-controls">
                    <button class="control-btn btn-start-recite" id="start-recitation-btn" onclick="startRecitationEngine()" disabled>
                        <i class="fas fa-microphone"></i>
                        Start Reciting
                    </button>
                    <button class="control-btn btn-stop-recite" id="stop-recitation-btn" onclick="stopRecitationEngine()" style="display: none;">
                        <i class="fas fa-stop"></i>
                        Stop
                    </button>
                </div>

                <div class="progress-stats" id="recitation-progress" style="display: none;">
                    <div class="stat-row">
                        <span class="stat-label">Score:</span>
                        <span class="stat-value" id="current-score">100</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Words Correct:</span>
                        <span class="stat-value" id="words-correct">0</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Current Word:</span>
                        <span class="stat-value" id="current-word-index">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quadrant 3: Content Library & Video Player -->
        <div class="dashboard-card" id="content-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-book-quran"></i>
                </div>
                <div>
                    <h2 class="card-title">Content Library</h2>
                    <p class="card-subtitle">Browse and unlock Qur'an content</p>
                </div>
                <div class="wallet-balance" style="margin-left: auto; text-align: right;">
                    <small style="color: var(--gray-500); display: block;">Wallet Balance</small>
                    <strong style="color: var(--primary); font-size: 1.1rem;">₦<?php echo number_format($user['wallet_balance'], 2); ?></strong>
                    <small style="color: var(--gray-500); display: block; margin-top: 0.25rem;">
                        Unlocked: <?php echo count($unlockedContent); ?>/<?php echo count($availableContent); ?> Surahs
                    </small>
                </div>
            </div>
            <div class="card-content">
                <?php if ($message): ?>
                    <div class="alert alert-success" style="margin-bottom: 1rem; padding: 0.75rem; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 0.375rem; color: #155724;">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger" style="margin-bottom: 1rem; padding: 0.75rem; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 0.375rem; color: #721c24;">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <div class="content-list">
                    <?php if (empty($availableContent)): ?>
                        <p style="text-align: center; color: var(--gray-500); font-style: italic;">No content available. Please contact admin.</p>
                    <?php else: ?>
                        <?php foreach ($availableContent as $content): ?>
                            <div class="content-item" data-content-id="<?php echo $content['id']; ?>">
                                <div class="content-info">
                                    <h4><?php echo htmlspecialchars($content['surah_name']); ?></h4>
                                    <small>Surah <?php echo $content['surah_number']; ?></small>
                                </div>
                                <?php if (in_array($content['id'], $unlockedContent)): ?>
                                    <button class="action-btn btn-start" onclick="startRecitation(<?php echo $content['id']; ?>, '<?php echo $content['youtube_id']; ?>', '<?php echo htmlspecialchars($content['arabic_text']); ?>', '<?php echo htmlspecialchars($content['translation'] ?? ''); ?>')"
                                        <i class="fas fa-play"></i>
                                        Start
                                    </button>
                                <?php else: ?>
                                    <form method="POST" action="dashboard.php" style="display: inline;">
                                        <input type="hidden" name="content_id" value="<?php echo $content['id']; ?>">
                                        <input type="hidden" name="unlock_content" value="1">
                                        <button type="submit" class="action-btn btn-unlock" style="background-color: #1a5f3f; color: white; padding: 0.5rem 1rem; border-radius: 4px; border: none; cursor: pointer; font-weight: bold;" onclick="return confirm('This will deduct ₦<?php echo number_format($content['unlock_price']); ?> from your wallet. Continue?');">
                                            <i class="fas fa-lock"></i>
                                            Unlock for ₦<?php echo number_format($content['unlock_price']); ?>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>


            </div>
        </div>

        <!-- Quadrant 4: Rankings & Stats -->
        <div class="dashboard-card" id="rankings-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div>
                    <h2 class="card-title">Rankings & Stats</h2>
                    <p class="card-subtitle">Your performance and rankings</p>
                </div>
            </div>
            <div class="card-content">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">₦<?php echo number_format($user['wallet_balance'] ?? 0, 2); ?></div>
                        <div class="stat-label">Wallet Balance</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo number_format($user['points_balance'] ?? 0); ?></div>
                        <div class="stat-label">Points</div>
                    </div>
                </div>

                <div class="rankings-list">
                    <div class="ranking-item">
                        <span class="rank-label">
                            <i class="fas fa-map-marker-alt"></i>
                            Ward Rank
                        </span>
                        <span class="rank-value">#<?php echo $rankings['ward_rank'] ?? 'N/A'; ?></span>
                    </div>
                    <div class="ranking-item">
                        <span class="rank-label">
                            <i class="fas fa-city"></i>
                            LGA Rank
                        </span>
                        <span class="rank-value">#<?php echo $rankings['lga_rank'] ?? 'N/A'; ?></span>
                    </div>
                    <div class="ranking-item">
                        <span class="rank-label">
                            <i class="fas fa-flag"></i>
                            State Rank
                        </span>
                        <span class="rank-value">#<?php echo $rankings['state_rank'] ?? 'N/A'; ?></span>
                    </div>
                    <div class="ranking-item">
                        <span class="rank-label">
                            <i class="fas fa-globe-africa"></i>
                            Country Rank
                        </span>
                        <span class="rank-value">#<?php echo $rankings['country_rank'] ?? 'N/A'; ?></span>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- End grid-container -->
</div> <!-- End dashboard-main -->

<script>
// 4-Quadrant Dashboard Functionality

// Global variables
let currentContentId = null;
let currentArabicText = '';
let recognition = null;
let mediaRecorder = null;
let recordedChunks = [];
let currentScore = 100;
let wordsCorrect = 0;
let currentWordIndex = 0;
let arabicWords = [];
let isRecording = false;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('4-Quadrant Dashboard initialized');

    // Check for Web Speech API support
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        console.log('Speech recognition supported');
    } else {
        console.warn('Speech recognition not supported');
        document.getElementById('start-recitation-btn').disabled = true;
        document.getElementById('start-recitation-btn').textContent = 'Speech Recognition Not Supported';
    }
});

// SIMPLE Content Library Functions - No complex JavaScript needed

function startRecitation(contentId, youtubeId, arabicText, translation = '') {
    currentContentId = contentId;
    currentArabicText = arabicText;

    // Show video player in the single container
    const videoPlaceholder = document.getElementById('video-placeholder');
    const youtubePlayer = document.getElementById('youtube-player');

    // Hide placeholder and show video
    videoPlaceholder.style.display = 'none';
    youtubePlayer.style.display = 'block';
    youtubePlayer.src = `https://www.youtube.com/embed/${youtubeId}?autoplay=1&rel=0&modestbranding=1`;

    // Update recitation display with enhanced Arabic text and translation
    const recitationDisplay = document.getElementById('recitation-display');
    recitationDisplay.innerHTML = `
        <div class="content-display" style="background: #f8f9fa; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem;">
            <div class="arabic-text" style="direction: rtl; text-align: right; font-size: 1.4rem; line-height: 2.5; padding: 1rem; background: white; border-radius: 8px; border-right: 4px solid var(--primary); font-family: 'Amiri', 'Times New Roman', serif; margin-bottom: 1rem;">
                ${arabicText}
            </div>
            ${translation ? `
                <div class="translation-text" style="direction: ltr; text-align: left; font-size: 1rem; line-height: 1.8; padding: 1rem; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745; color: #2d5a2d;">
                    <h6 style="margin: 0 0 0.5rem 0; color: #28a745; font-weight: 600;">
                        <i class="fas fa-language"></i> Translation:
                    </h6>
                    ${translation}
                </div>
            ` : ''}
        </div>
    `;

    // Parse Arabic words
    arabicWords = arabicText.split(' ').filter(word => word.trim() !== '');

    // Enable recitation button
    document.getElementById('start-recitation-btn').disabled = false;

    // Update status
    document.getElementById('recitation-status').style.display = 'block';
    document.getElementById('recitation-status').textContent = 'Video loaded. Ready to start recitation.';
    document.getElementById('recitation-status').className = 'recitation-status status-listening';

    console.log('Recitation started for content ID:', contentId);
}

// Quadrant 2: Recitation Engine Functions
function startRecitationEngine() {
    if (!currentContentId) {
        alert('Please select a Surah first');
        return;
    }

    // Reset variables
    currentScore = 100;
    wordsCorrect = 0;
    currentWordIndex = 0;

    // Update UI
    document.getElementById('start-recitation-btn').style.display = 'none';
    document.getElementById('stop-recitation-btn').style.display = 'block';
    document.getElementById('recitation-progress').style.display = 'block';
    document.getElementById('current-score').textContent = currentScore;
    document.getElementById('words-correct').textContent = wordsCorrect;
    document.getElementById('current-word-index').textContent = currentWordIndex + 1;

    // Start speech recognition
    startSpeechRecognition();

    // Start screen recording
    startScreenRecording();

    console.log('Recitation engine started');
}

function stopRecitationEngine() {
    // Stop speech recognition
    if (recognition) {
        recognition.stop();
    }

    // Stop screen recording
    stopScreenRecording();

    // Update UI
    document.getElementById('start-recitation-btn').style.display = 'block';
    document.getElementById('stop-recitation-btn').style.display = 'none';
    document.getElementById('recitation-status').textContent = 'Recitation stopped';
    document.getElementById('recitation-status').className = 'recitation-status';

    console.log('Recitation engine stopped');
}

function startSpeechRecognition() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('Speech recognition not supported in this browser');
        return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognition = new SpeechRecognition();

    recognition.lang = 'ar-SA'; // Arabic
    recognition.continuous = true;
    recognition.interimResults = true;

    recognition.onstart = function() {
        document.getElementById('recitation-status').textContent = 'Listening...';
        document.getElementById('recitation-status').className = 'recitation-status status-listening';
    };

    recognition.onresult = function(event) {
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            if (event.results[i].isFinal) {
                finalTranscript += event.results[i][0].transcript;
            }
        }

        if (finalTranscript) {
            processSpokenText(finalTranscript.trim());
        }
    };

    recognition.onerror = function(event) {
        console.error('Speech recognition error:', event.error);
        document.getElementById('recitation-status').textContent = 'Error: ' + event.error;
        document.getElementById('recitation-status').className = 'recitation-status';
    };

    recognition.start();
}

// Quadrant 3: Camera Functions with Recording
let cameraRecorder = null;
let cameraRecordedChunks = [];
let isCameraRecording = false;

function startCamera() {
    const video = document.getElementById('selfie-video');
    const placeholder = document.getElementById('camera-placeholder');

    navigator.mediaDevices.getUserMedia({ video: true, audio: true })
        .then(function(stream) {
            video.srcObject = stream;
            video.style.display = 'block';
            placeholder.style.display = 'none';

            // Store the stream for later use
            window.currentCameraStream = stream;

            // Update button to show recording options
            updateCameraControls(true);
            console.log('Selfie camera started');
        })
        .catch(function(error) {
            console.error('Error accessing camera:', error);
            alert('Could not access camera. Please check permissions.');
        });
}

function updateCameraControls(cameraActive) {
    const placeholder = document.getElementById('camera-placeholder');

    if (cameraActive) {
        placeholder.innerHTML = `
            <div style="text-align: center; padding: 1rem;">
                <p style="margin-bottom: 1rem; color: var(--text);">Camera Active</p>
                <div style="display: flex; gap: 0.5rem; justify-content: center; flex-wrap: wrap;">
                    <button class="camera-btn" onclick="startCameraRecording()" id="start-record-btn" style="background: #dc3545; font-size: 0.7rem; padding: 0.4rem 0.8rem;">
                        <i class="fas fa-record-vinyl"></i>
                        Record
                    </button>
                    <button class="camera-btn" onclick="stopCameraRecording()" id="stop-record-btn" style="display: none; background: #28a745; font-size: 0.7rem; padding: 0.4rem 0.8rem;">
                        <i class="fas fa-stop"></i>
                        Stop
                    </button>
                    <button class="camera-btn" onclick="stopCamera()" style="background: var(--gray-600); font-size: 0.7rem; padding: 0.4rem 0.8rem;">
                        <i class="fas fa-times"></i>
                        Close
                    </button>
                </div>
            </div>
        `;
    } else {
        placeholder.innerHTML = `
            <i class="fas fa-camera"></i>
            <p style="margin-bottom: 0.5rem;">Camera not active</p>
            <button class="camera-btn" onclick="startCamera()">
                <i class="fas fa-video"></i>
                Start Camera
            </button>
        `;
    }
}

function startCameraRecording() {
    if (!window.currentCameraStream) {
        alert('Please start camera first');
        return;
    }

    try {
        cameraRecorder = new MediaRecorder(window.currentCameraStream);
        cameraRecordedChunks = [];

        cameraRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
                cameraRecordedChunks.push(event.data);
            }
        };

        cameraRecorder.onstop = function() {
            const blob = new Blob(cameraRecordedChunks, { type: 'video/webm' });
            uploadCameraRecording(blob);
        };

        cameraRecorder.start();
        isCameraRecording = true;

        // Update UI
        document.getElementById('start-record-btn').style.display = 'none';
        document.getElementById('stop-record-btn').style.display = 'inline-flex';

        console.log('Camera recording started');
    } catch (error) {
        console.error('Error starting camera recording:', error);
        alert('Could not start recording. Please try again.');
    }
}

function stopCameraRecording() {
    if (cameraRecorder && isCameraRecording) {
        try {
            cameraRecorder.stop();
            isCameraRecording = false;

            // Update UI
            const startBtn = document.getElementById('start-record-btn');
            const stopBtn = document.getElementById('stop-record-btn');

            if (startBtn) startBtn.style.display = 'inline-flex';
            if (stopBtn) stopBtn.style.display = 'none';

            console.log('Camera recording stopped');
        } catch (error) {
            console.error('Error stopping camera recording:', error);
            isCameraRecording = false;
        }
    }
}

function stopCamera() {
    const video = document.getElementById('selfie-video');
    const placeholder = document.getElementById('camera-placeholder');

    // Stop any ongoing recording first
    if (cameraRecorder && isCameraRecording) {
        stopCameraRecording();
    }

    // Stop camera stream
    if (window.currentCameraStream) {
        window.currentCameraStream.getTracks().forEach(track => {
            track.stop();
            console.log('Camera track stopped:', track.kind);
        });
        window.currentCameraStream = null;
    }

    // Reset video element
    video.srcObject = null;
    video.style.display = 'none';
    placeholder.style.display = 'block';

    // Update UI
    updateCameraControls(false);

    console.log('Camera stopped successfully');
}

function uploadCameraRecording(blob) {
    const formData = new FormData();
    formData.append('recording', blob, 'selfie_recording_' + Date.now() + '.webm');
    formData.append('type', 'selfie');
    formData.append('title', 'Selfie Recording - ' + new Date().toLocaleString());

    // Show upload progress
    const placeholder = document.getElementById('camera-placeholder');
    placeholder.innerHTML = `
        <div style="text-align: center; padding: 1rem;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary); margin-bottom: 1rem;"></i>
            <p>Uploading recording...</p>
        </div>
    `;

    fetch('api/upload_recording.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Recording uploaded successfully! Check the Streams page to see your video.');
            updateCameraControls(true);
        } else {
            alert('Failed to upload recording: ' + (data.message || 'Unknown error'));
            updateCameraControls(true);
        }
    })
    .catch(error => {
        console.error('Error uploading recording:', error);
        alert('Error uploading recording. Please try again.');
        updateCameraControls(true);
    });
}

// Screen Recording Functions
function startScreenRecording() {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        console.warn('Screen recording not supported');
        return;
    }

    navigator.mediaDevices.getDisplayMedia({ video: true, audio: true })
        .then(function(stream) {
            mediaRecorder = new MediaRecorder(stream);
            recordedChunks = [];

            mediaRecorder.ondataavailable = function(event) {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };

            mediaRecorder.onstop = function() {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                uploadRecording(blob);
            };

            mediaRecorder.start();
            isRecording = true;

            document.getElementById('recitation-status').textContent = 'Recording...';
            document.getElementById('recitation-status').className = 'recitation-status status-recording';

            console.log('Screen recording started');
        })
        .catch(function(error) {
            console.error('Error starting screen recording:', error);
        });
}

function stopScreenRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        console.log('Screen recording stopped');
    }
}

function uploadRecording(blob) {
    const formData = new FormData();
    formData.append('recording', blob, 'recitation_' + Date.now() + '.webm');
    formData.append('content_id', currentContentId);
    formData.append('score', currentScore);

    fetch('api/upload_recording.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Recording uploaded successfully');
        } else {
            console.error('Failed to upload recording');
        }
    })
    .catch(error => {
        console.error('Error uploading recording:', error);
    });
}

// Additional utility functions
function processSpokenText(spokenText) {
    // Process the spoken Arabic text and compare with expected text
    console.log('Processing spoken text:', spokenText);

    // Simple word matching logic (can be enhanced)
    const spokenWords = spokenText.split(' ');
    let matches = 0;

    spokenWords.forEach(word => {
        if (arabicWords.includes(word.trim())) {
            matches++;
        }
    });

    // Update score and progress
    wordsCorrect = matches;
    currentScore = Math.max(0, 100 - (arabicWords.length - matches) * 5);

    // Update UI
    document.getElementById('words-correct').textContent = wordsCorrect;
    document.getElementById('current-score').textContent = currentScore;

    // Highlight words in the display
    highlightWords(spokenWords);
}

function highlightWords(spokenWords) {
    const recitationDisplay = document.getElementById('recitation-display');
    let highlightedText = currentArabicText;

    spokenWords.forEach(word => {
        if (arabicWords.includes(word.trim())) {
            highlightedText = highlightedText.replace(word, `<span class="arabic-word correct">${word}</span>`);
        }
    });

    recitationDisplay.innerHTML = `<div style="direction: rtl; text-align: right;">${highlightedText}</div>`;
}

// Single Video Control Function
function clearCurrentVideo() {
    const videoPlaceholder = document.getElementById('video-placeholder');
    const youtubePlayer = document.getElementById('youtube-player');

    // Show placeholder and hide video
    videoPlaceholder.style.display = 'block';
    youtubePlayer.style.display = 'none';
    youtubePlayer.src = '';

    // Clear recitation display
    const recitationDisplay = document.getElementById('recitation-display');
    recitationDisplay.innerHTML = `
        <div style="text-align: center; padding: 2rem; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
            <i class="fas fa-book-quran" style="font-size: 2rem; color: var(--primary); margin-bottom: 1rem;"></i>
            <p style="color: var(--gray-500); font-style: italic; margin: 0;">
                Select a Surah from the dropdown above to begin recitation practice
            </p>
        </div>
    `;

    // Reset current content
    currentContentId = null;
    currentArabicText = '';

    // Disable recitation button
    const startBtn = document.getElementById('start-recitation-btn');
    if (startBtn) {
        startBtn.disabled = true;
        startBtn.style.opacity = '0.5';
    }
}

// Play user recording function
function playRecording(filePath) {
    const recitationDisplay = document.getElementById('recitation-display');

    // Create video element to play the recording
    recitationDisplay.innerHTML = `
        <div style="text-align: center;">
            <h5 style="color: var(--text); margin-bottom: 1rem;">
                <i class="fas fa-play"></i> Playing Your Recording
            </h5>
            <video controls style="width: 100%; max-width: 400px; border-radius: 8px;">
                <source src="${filePath}" type="video/webm">
                <source src="${filePath}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <div style="margin-top: 1rem;">
                <button class="btn btn-secondary" onclick="closeRecordingPlayer()">
                    <i class="fas fa-times"></i> Close
                </button>
            </div>
        </div>
    `;
}

function closeRecordingPlayer() {
    const recitationDisplay = document.getElementById('recitation-display');

    if (currentArabicText) {
        // Show Arabic text if content is selected
        recitationDisplay.innerHTML = `<div style="direction: rtl; text-align: right; font-size: 1.2rem; line-height: 2;">${currentArabicText}</div>`;
    } else {
        // Show default message
        recitationDisplay.innerHTML = `
            <p style="text-align: center; color: var(--gray-500); font-style: italic;">
                Select a Surah from the Content Library to begin recitation practice
            </p>
        `;
    }
}

// Initialize page on load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize with clear state
    clearCurrentVideo();
});
</script>

<?php require_once 'components/user_footer.php'; ?>
