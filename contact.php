<?php
require_once 'config.php';

// Get current language (default to English)
$language = $_GET['lang'] ?? $_SESSION['language'] ?? 'en';
$_SESSION['language'] = $language;

// Handle contact form submission
$messageSent = false;
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'send_message' && validateCSRFToken($_POST['csrf_token'])) {
        $name = sanitize($_POST['name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $subject = sanitize($_POST['subject'] ?? '');
        $message = sanitize($_POST['message'] ?? '');
        $messageType = sanitize($_POST['message_type'] ?? 'general');
        
        if (empty($name) || empty($email) || empty($subject) || empty($message)) {
            $errorMessage = 'Please fill in all required fields.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errorMessage = 'Please enter a valid email address.';
        } else {
            try {
                $conn = getConnection();
                
                // Insert contact message
                $stmt = $conn->prepare("
                    INSERT INTO contact_messages 
                    (name, email, subject, message, message_type, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                
                $stmt->bind_param("sssssss", $name, $email, $subject, $message, $messageType, $ipAddress, $userAgent);
                
                if ($stmt->execute()) {
                    $messageSent = true;
                    
                    // Send notification email to admin (in production)
                    // mail('<EMAIL>', 'New Contact Message: ' . $subject, $message);
                } else {
                    $errorMessage = 'Failed to send message. Please try again.';
                }
                
                $stmt->close();
                $conn->close();
                
            } catch (Exception $e) {
                $errorMessage = 'An error occurred. Please try again later.';
                error_log("Contact form error: " . $e->getMessage());
            }
        }
    }
}

// Language content
$content = [
    'en' => [
        'title' => 'Contact Us - Qur\'an Recite App',
        'heading' => 'Get in Touch',
        'subtitle' => 'We\'d love to hear from you. Send us a message and we\'ll respond as soon as possible.',
        'contact_info' => 'Contact Information',
        'send_message' => 'Send Message',
        'name' => 'Full Name',
        'email' => 'Email Address',
        'subject' => 'Subject',
        'message' => 'Message',
        'message_type' => 'Message Type',
        'types' => [
            'general' => 'General Inquiry',
            'technical' => 'Technical Support',
            'feedback' => 'Feedback',
            'partnership' => 'Partnership',
            'scholarship' => 'Scholarship',
            'complaint' => 'Complaint'
        ],
        'whatsapp_title' => 'Join Our WhatsApp Community',
        'whatsapp_text' => 'Connect with fellow reciters and get instant support from our community.',
        'join_whatsapp' => 'Join WhatsApp Group',
        'office_hours' => 'Office Hours',
        'hours_text' => 'Sunday - Thursday: 9:00 AM - 6:00 PM (GMT+1)',
        'success_message' => 'Thank you for your message! We\'ll get back to you within 24 hours.',
        'faq_title' => 'Frequently Asked Questions',
        'location' => 'Location',
        'phone' => 'Phone',
        'email_contact' => 'Email'
    ],
    'ar' => [
        'title' => 'اتصل بنا - تطبيق تلاوة القرآن',
        'heading' => 'تواصل معنا',
        'subtitle' => 'نحن نحب أن نسمع منك. أرسل لنا رسالة وسنرد في أقرب وقت ممكن.',
        'contact_info' => 'معلومات الاتصال',
        'send_message' => 'إرسال رسالة',
        'name' => 'الاسم الكامل',
        'email' => 'عنوان البريد الإلكتروني',
        'subject' => 'الموضوع',
        'message' => 'الرسالة',
        'message_type' => 'نوع الرسالة',
        'types' => [
            'general' => 'استفسار عام',
            'technical' => 'الدعم الفني',
            'feedback' => 'ملاحظات',
            'partnership' => 'شراكة',
            'scholarship' => 'منحة دراسية',
            'complaint' => 'شكوى'
        ],
        'whatsapp_title' => 'انضم إلى مجتمع الواتساب',
        'whatsapp_text' => 'تواصل مع زملائك القراء واحصل على دعم فوري من مجتمعنا.',
        'join_whatsapp' => 'انضم إلى مجموعة الواتساب',
        'office_hours' => 'ساعات العمل',
        'hours_text' => 'الأحد - الخميس: 9:00 صباحاً - 6:00 مساءً (GMT+1)',
        'success_message' => 'شكراً لك على رسالتك! سنرد عليك خلال 24 ساعة.',
        'faq_title' => 'الأسئلة الشائعة',
        'location' => 'الموقع',
        'phone' => 'الهاتف',
        'email_contact' => 'البريد الإلكتروني'
    ],
    'ha' => [
        'title' => 'Tuntuɓe Mu - App ɗin Karanta Alqur\'ani',
        'heading' => 'Tuntuɓe Mu',
        'subtitle' => 'Muna son jin labarinku. Aika mana da saƙo kuma za mu amsa da sauri.',
        'contact_info' => 'Bayanan Tuntuɓawa',
        'send_message' => 'Aika Saƙo',
        'name' => 'Cikakken Suna',
        'email' => 'Adireshin Email',
        'subject' => 'Batun',
        'message' => 'Saƙo',
        'message_type' => 'Nau\'in Saƙo',
        'types' => [
            'general' => 'Tambaya Na Gaba ɗaya',
            'technical' => 'Taimakon Fasaha',
            'feedback' => 'Ra\'ayi',
            'partnership' => 'Haɗin Gwiwa',
            'scholarship' => 'Tallafin Karatu',
            'complaint' => 'Korafi'
        ],
        'whatsapp_title' => 'Shiga Al\'ummarmu ta WhatsApp',
        'whatsapp_text' => 'Tuntuɓe \'yan uwa masu karanta kuma samun tallafi nan take daga al\'ummarmu.',
        'join_whatsapp' => 'Shiga Ƙungiyar WhatsApp',
        'office_hours' => 'Lokutan Aiki',
        'hours_text' => 'Lahadi - Alhamis: 9:00 NA SAFE - 6:00 NA YAMMA (GMT+1)',
        'success_message' => 'Na gode da saƙonku! Za mu amsa muku cikin sa\'o\'i 24.',
        'faq_title' => 'Tambayoyin Da Ake Yawa Yi',
        'location' => 'Wuri',
        'phone' => 'Wayar Tarho',
        'email_contact' => 'Email'
    ]
];

$lang = $content[$language] ?? $content['en'];

// FAQ data
$faqs = [
    [
        'question' => 'How does the speech recognition work?',
        'answer' => 'Our app uses advanced Web Speech API and AI to analyze your Qur\'anic recitation in real-time, providing instant feedback on pronunciation, tajweed rules, and accuracy.'
    ],
    [
        'question' => 'Is the app free to use?',
        'answer' => 'The app requires a one-time registration fee of ₦1000. After registration, you can earn points and money through recitation, engagement, and referrals.'
    ],
    [
        'question' => 'Which languages are supported?',
        'answer' => 'The app supports English, Arabic, Hausa, Hindi, and Chinese interfaces, making it accessible to Muslims worldwide.'
    ],
    [
        'question' => 'How can I withdraw my earnings?',
        'answer' => 'You can convert your points to money and withdraw after 30 days. We support bank transfers to verified Nigerian bank accounts.'
    ],
    [
        'question' => 'Can I use the app offline?',
        'answer' => 'Currently, the app requires an internet connection for speech recognition and community features. Offline mode is planned for future updates.'
    ]
];
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" <?php echo $language === 'ar' ? 'dir="rtl"' : ''; ?>>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['title']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/main.css" rel="stylesheet">
    <style>
        .contact-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .contact-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .contact-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .contact-info-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            height: 100%;
        }
        .whatsapp-card {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
        }
        .faq-item {
            border: none;
            margin-bottom: 10px;
            border-radius: 10px;
            overflow: hidden;
        }
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .arabic-text {
            font-family: 'Amiri', 'Times New Roman', serif;
            font-size: 1.1em;
        }
        .success-alert {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            border-radius: 10px;
        }
    </style>
</head>
<body class="<?php echo $language === 'ar' ? 'rtl arabic-text' : ''; ?>">
    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="dropdown">
            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-globe me-1"></i>
                <?php echo strtoupper($language); ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?lang=en">English</a></li>
                <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                <li><a class="dropdown-item" href="?lang=ha">Hausa</a></li>
            </ul>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-book-quran text-success me-2"></i>
                Qur'an Recite App
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.php">Contact</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.php">Register</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="contact-section">
        <div class="container">
            <div class="row text-center">
                <div class="col-12">
                    <h1 class="display-4 fw-bold mb-4"><?php echo $lang['heading']; ?></h1>
                    <p class="lead"><?php echo $lang['subtitle']; ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form and Info -->
    <section class="py-5">
        <div class="container">
            <!-- Success/Error Messages -->
            <?php if ($messageSent): ?>
                <div class="alert success-alert alert-dismissible fade show mb-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $lang['success_message']; ?>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $errorMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Contact Form -->
                <div class="col-lg-8 mb-4">
                    <div class="contact-form">
                        <h3 class="mb-4">
                            <i class="fas fa-envelope me-2 text-primary"></i>
                            <?php echo $lang['send_message']; ?>
                        </h3>
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="send_message">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label"><?php echo $lang['name']; ?> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label"><?php echo $lang['email']; ?> <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label"><?php echo $lang['subject']; ?> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="subject" name="subject" required
                                           value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="messageType" class="form-label"><?php echo $lang['message_type']; ?></label>
                                    <select class="form-select" id="messageType" name="message_type">
                                        <?php foreach ($lang['types'] as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo (isset($_POST['message_type']) && $_POST['message_type'] === $key) ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="message" class="form-label"><?php echo $lang['message']; ?> <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="message" name="message" rows="5" required 
                                          placeholder="Please provide as much detail as possible..."><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                <?php echo $lang['send_message']; ?>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="col-lg-4">
                    <div class="contact-info-card">
                        <h4 class="mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo $lang['contact_info']; ?>
                        </h4>
                        
                        <div class="mb-4">
                            <h6><i class="fas fa-map-marker-alt me-2"></i><?php echo $lang['location']; ?></h6>
                            <p class="mb-3">Abuja, Nigeria<br>Federal Capital Territory</p>
                        </div>
                        
                        <div class="mb-4">
                            <h6><i class="fas fa-phone me-2"></i><?php echo $lang['phone']; ?></h6>
                            <p class="mb-3">+234 ************</p>
                        </div>
                        
                        <div class="mb-4">
                            <h6><i class="fas fa-envelope me-2"></i><?php echo $lang['email_contact']; ?></h6>
                            <p class="mb-3"><EMAIL></p>
                        </div>
                        
                        <div class="mb-4">
                            <h6><i class="fas fa-clock me-2"></i><?php echo $lang['office_hours']; ?></h6>
                            <p class="mb-3"><?php echo $lang['hours_text']; ?></p>
                        </div>
                        
                        <div class="social-links">
                            <h6 class="mb-3">Follow Us</h6>
                            <div class="d-flex gap-3">
                                <a href="#" class="text-white fs-4"><i class="fab fa-facebook"></i></a>
                                <a href="#" class="text-white fs-4"><i class="fab fa-twitter"></i></a>
                                <a href="#" class="text-white fs-4"><i class="fab fa-instagram"></i></a>
                                <a href="#" class="text-white fs-4"><i class="fab fa-youtube"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- WhatsApp Community Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="whatsapp-card">
                        <i class="fab fa-whatsapp fa-4x mb-4"></i>
                        <h3 class="mb-3"><?php echo $lang['whatsapp_title']; ?></h3>
                        <p class="mb-4"><?php echo $lang['whatsapp_text']; ?></p>
                        <a href="https://chat.whatsapp.com/quranreciteapp" class="btn btn-light btn-lg" target="_blank">
                            <i class="fab fa-whatsapp me-2"></i>
                            <?php echo $lang['join_whatsapp']; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold"><?php echo $lang['faq_title']; ?></h2>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="faqAccordion">
                        <?php foreach ($faqs as $index => $faq): ?>
                            <div class="accordion-item faq-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button <?php echo $index !== 0 ? 'collapsed' : ''; ?>" 
                                            type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#faq<?php echo $index; ?>">
                                        <?php echo $faq['question']; ?>
                                    </button>
                                </h2>
                                <div id="faq<?php echo $index; ?>" 
                                     class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" 
                                     data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        <?php echo $faq['answer']; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-book-quran text-success me-2"></i>
                        Qur'an Recite App
                    </h5>
                    <p class="text-light">
                        Empowering Muslims worldwide to perfect their Qur'anic recitation through technology and community support.
                    </p>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-light text-decoration-none">Home</a></li>
                        <li><a href="about.php" class="text-light text-decoration-none">About</a></li>
                        <li><a href="contact.php" class="text-light text-decoration-none">Contact</a></li>
                        <li><a href="register.php" class="text-light text-decoration-none">Register</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="fw-bold">Languages</h6>
                    <ul class="list-unstyled">
                        <li><a href="?lang=en" class="text-light text-decoration-none">English</a></li>
                        <li><a href="?lang=ar" class="text-light text-decoration-none">العربية</a></li>
                        <li><a href="?lang=ha" class="text-light text-decoration-none">Hausa</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold">Support</h6>
                    <p class="text-light small">
                        <i class="fas fa-envelope me-2"></i><EMAIL><br>
                        <i class="fas fa-phone me-2"></i>+234 ************<br>
                        <i class="fab fa-whatsapp me-2"></i>WhatsApp Support
                    </p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Universal Reciters Foundation. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-light text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Character count for message textarea
        document.addEventListener('DOMContentLoaded', function() {
            const messageTextarea = document.getElementById('message');
            const maxLength = 1000;
            
            // Add character counter
            const charCounter = document.createElement('small');
            charCounter.className = 'text-muted';
            charCounter.textContent = `0/${maxLength} characters`;
            messageTextarea.parentNode.appendChild(charCounter);
            
            messageTextarea.addEventListener('input', function() {
                const currentLength = this.value.length;
                charCounter.textContent = `${currentLength}/${maxLength} characters`;
                
                if (currentLength > maxLength) {
                    charCounter.className = 'text-danger';
                } else if (currentLength > maxLength * 0.8) {
                    charCounter.className = 'text-warning';
                } else {
                    charCounter.className = 'text-muted';
                }
            });
        });
    </script>
</body>
</html> 