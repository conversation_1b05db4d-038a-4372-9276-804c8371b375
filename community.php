<?php
$page_title = 'Community';
require_once 'components/user_header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Sample community data
$posts = [
    [
        'id' => 1,
        'user_name' => '<PERSON>',
        'user_avatar' => 'assets/images/default-avatar.png',
        'time' => '2 hours ago',
        'content' => 'Just completed my daily recitation of Surah Al-Kahf. The verses about the companions of the cave always give me chills. May <PERSON> grant us steadfastness in our faith.',
        'likes' => 24,
        'comments' => 8,
        'type' => 'text'
    ],
    [
        'id' => 2,
        'user_name' => 'Fatima Zahra',
        'user_avatar' => 'assets/images/default-avatar.png',
        'time' => '4 hours ago',
        'content' => 'Sharing my recitation of Surah Al-Fatiha. Any feedback on my tajweed would be appreciated!',
        'likes' => 31,
        'comments' => 12,
        'type' => 'video',
        'media_url' => 'assets/videos/sample.mp4'
    ],
    [
        'id' => 3,
        'user_name' => 'Muhammad Yusuf',
        'user_avatar' => 'assets/images/default-avatar.png',
        'time' => '6 hours ago',
        'content' => 'Beautiful verse from today\'s recitation session. SubhanAllah!',
        'likes' => 18,
        'comments' => 5,
        'type' => 'image',
        'media_url' => 'assets/images/sample.jpg'
    ],
    [
        'id' => 4,
        'user_name' => 'Aisha Ibrahim',
        'user_avatar' => 'assets/images/default-avatar.png',
        'time' => '8 hours ago',
        'content' => 'Does anyone have tips for improving pronunciation of the letter ض (Daad)? I\'ve been struggling with it.',
        'likes' => 45,
        'comments' => 23,
        'type' => 'text'
    ],
    [
        'id' => 5,
        'user_name' => 'Usman Aliyu',
        'user_avatar' => 'assets/images/default-avatar.png',
        'time' => '1 day ago',
        'content' => 'Alhamdulillah! Just reached 100 recitations. The journey has been incredible. Thank you to everyone in this community for the support.',
        'likes' => 67,
        'comments' => 34,
        'type' => 'text'
    ]
];

// Active discussions
$discussions = [
    ['title' => 'Best time for Quran recitation', 'replies' => 45, 'last_activity' => '2 hours ago'],
    ['title' => 'Learning Tajweed rules', 'replies' => 32, 'last_activity' => '4 hours ago'],
    ['title' => 'Memorization techniques', 'replies' => 78, 'last_activity' => '6 hours ago'],
    ['title' => 'Understanding Arabic meanings', 'replies' => 23, 'last_activity' => '8 hours ago'],
];

// Top contributors
$topContributors = [
    ['name' => 'Sheikh Ibrahim', 'contributions' => 234, 'avatar' => 'assets/images/default-avatar.png'],
    ['name' => 'Fatima Zahra', 'contributions' => 189, 'avatar' => 'assets/images/default-avatar.png'],
    ['name' => 'Abdullah Muhammad', 'contributions' => 156, 'avatar' => 'assets/images/default-avatar.png'],
    ['name' => 'Aisha Ibrahim', 'contributions' => 134, 'avatar' => 'assets/images/default-avatar.png'],
];
?>

<style>
:root {
    --primary-color: #1a5f3f;
    --primary-light: #2d7a5a;
    --primary-hover: #164d34;
    --background: #FFFFFF;
    --surface: #F8F9FA;
    --text: #1E1E1E;
    --text-secondary: #666666;
    --border: #E0E0E0;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
}

body {
    background-color: var(--background);
    color: var(--text);
}

.community-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: center;
}

.community-header h2 {
    margin: 0 0 0.5rem 0;
    font-weight: 700;
}

.community-header p {
    margin: 0;
    opacity: 0.9;
}

.create-post-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.create-post-input {
    width: 100%;
    border: 2px solid var(--border);
    border-radius: 25px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    resize: none;
    background: var(--surface);
    transition: all 0.3s ease;
}

.create-post-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(177, 0, 32, 0.1);
}

.create-post-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.post-actions-left {
    display: flex;
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 2px solid var(--border);
    border-radius: 25px;
    background: white;
    color: var(--text);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.post-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.post-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.post-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border);
}

.post-user-info h4 {
    margin: 0;
    color: var(--text);
    font-weight: 600;
}

.post-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.post-content {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.post-media {
    margin-bottom: 1rem;
    text-align: center;
}

.post-media img {
    max-width: 100%;
    border-radius: 8px;
}

.post-media video {
    max-width: 100%;
    border-radius: 8px;
}

.post-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border);
    padding-top: 1rem;
}

.post-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.post-stat:hover {
    color: var(--primary-color);
}

.sidebar-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.sidebar-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.discussion-item {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border);
}

.discussion-item:last-child {
    border-bottom: none;
}

.discussion-title {
    color: var(--text);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.discussion-meta {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.contributor-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border);
}

.contributor-item:last-child {
    border-bottom: none;
}

.contributor-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border);
}

.contributor-info h4 {
    margin: 0;
    color: var(--text);
    font-weight: 600;
    font-size: 0.9rem;
}

.contributor-count {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.quick-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.quick-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--surface);
    border: 2px solid var(--border);
    border-radius: 25px;
    text-decoration: none;
    color: var(--text);
    font-weight: 500;
    transition: all 0.3s ease;
}

.quick-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

@media (max-width: 768px) {
    .create-post-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .post-actions-left {
        justify-content: center;
    }
    
    .post-stats {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>

<div class="community-header">
    <h2><i class="fas fa-users me-2"></i>Community</h2>
    <p>Connect with fellow reciters, share your journey, and learn together</p>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Create Post -->
        <div class="create-post-card">
            <textarea class="create-post-input" placeholder="Share your thoughts with the community..." rows="3"></textarea>
            <div class="create-post-actions">
                <div class="post-actions-left">
                    <a href="#" class="action-btn">
                        <i class="fas fa-camera"></i>
                        Photo
                    </a>
                    <a href="#" class="action-btn">
                        <i class="fas fa-video"></i>
                        Video
                    </a>
                    <a href="#" class="action-btn">
                        <i class="fas fa-microphone"></i>
                        Audio
                    </a>
                </div>
                <button class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>
                    Share
                </button>
            </div>
        </div>

        <!-- Posts Feed -->
        <?php foreach ($posts as $post): ?>
            <div class="post-card">
                <div class="post-header">
                    <img src="<?php echo $post['user_avatar']; ?>" alt="Avatar" class="post-avatar">
                    <div class="post-user-info">
                        <h4><?php echo htmlspecialchars($post['user_name']); ?></h4>
                        <div class="post-time"><?php echo $post['time']; ?></div>
                    </div>
                </div>
                
                <div class="post-content">
                    <?php echo htmlspecialchars($post['content']); ?>
                </div>
                
                <?php if ($post['type'] === 'image'): ?>
                    <div class="post-media">
                        <img src="<?php echo $post['media_url']; ?>" alt="Post media">
                    </div>
                <?php elseif ($post['type'] === 'video'): ?>
                    <div class="post-media">
                        <video controls>
                            <source src="<?php echo $post['media_url']; ?>" type="video/mp4">
                        </video>
                    </div>
                <?php endif; ?>
                
                <div class="post-stats">
                    <a href="#" class="post-stat">
                        <i class="fas fa-heart"></i>
                        <?php echo $post['likes']; ?> Likes
                    </a>
                    <a href="#" class="post-stat">
                        <i class="fas fa-comment"></i>
                        <?php echo $post['comments']; ?> Comments
                    </a>
                    <a href="#" class="post-stat">
                        <i class="fas fa-share"></i>
                        Share
                    </a>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <div class="col-lg-4">
        <!-- Active Discussions -->
        <div class="sidebar-card">
            <h3><i class="fas fa-comments me-2"></i>Active Discussions</h3>
            <?php foreach ($discussions as $discussion): ?>
                <div class="discussion-item">
                    <div class="discussion-title"><?php echo htmlspecialchars($discussion['title']); ?></div>
                    <div class="discussion-meta">
                        <?php echo $discussion['replies']; ?> replies • <?php echo $discussion['last_activity']; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Top Contributors -->
        <div class="sidebar-card">
            <h3><i class="fas fa-star me-2"></i>Top Contributors</h3>
            <?php foreach ($topContributors as $contributor): ?>
                <div class="contributor-item">
                    <img src="<?php echo $contributor['avatar']; ?>" alt="Avatar" class="contributor-avatar">
                    <div class="contributor-info">
                        <h4><?php echo htmlspecialchars($contributor['name']); ?></h4>
                        <div class="contributor-count"><?php echo $contributor['contributions']; ?> contributions</div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Quick Links -->
        <div class="sidebar-card">
            <h3><i class="fas fa-link me-2"></i>Quick Links</h3>
            <div class="quick-links">
                <a href="dashboard.php" class="quick-link">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <a href="streams.php" class="quick-link">
                    <i class="fas fa-video"></i>
                    Streams
                </a>
                <a href="rankings.php" class="quick-link">
                    <i class="fas fa-trophy"></i>
                    Rankings
                </a>
                <a href="wallet.php" class="quick-link">
                    <i class="fas fa-wallet"></i>
                    Wallet
                </a>
            </div>
        </div>
    </div>
</div>

<?php require_once 'components/user_footer.php'; ?> 