<?php
/**
 * Community Page for Recite! App
 * Clean, Mobile-First Community with User Interactions
 */

require_once 'config/database.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Community';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Get community posts/discussions
$conn = getConnection();

// Get recent community activity (users and their recordings)
$communityQuery = "SELECT u.id, u.full_name, u.profile_picture, u.created_at as join_date,
                   COUNT(sr.id) as recording_count,
                   MAX(sr.created_at) as last_activity
                   FROM users u
                   LEFT JOIN screen_records sr ON u.id = sr.user_id
                   WHERE u.id != ?
                   GROUP BY u.id
                   ORDER BY last_activity DESC, recording_count DESC
                   LIMIT 20";
$stmt = $conn->prepare($communityQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$communityResult = $stmt->get_result();
$communityMembers = [];
while ($row = $communityResult->fetch_assoc()) {
    $communityMembers[] = $row;
}

// Get recent recordings from all users
$recentRecordings = [];
$recordingsQuery = "SELECT sr.*, u.full_name, u.profile_picture,
                    DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date
                    FROM screen_records sr
                    JOIN users u ON sr.user_id = u.id
                    ORDER BY sr.created_at DESC
                    LIMIT 10";
$recordingsResult = $conn->query($recordingsQuery);
if ($recordingsResult) {
    while ($row = $recordingsResult->fetch_assoc()) {
        $recentRecordings[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design - Same as Dashboard */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }

        .header-center {
            flex: 1;
            max-width: 300px;
            margin: 0 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.75rem;
            text-align: center;
        }

        .welcome-card h2 {
            font-size: 1.1rem;
            margin: 0;
            font-weight: 600;
        }

        .welcome-card p {
            font-size: 0.8rem;
            margin: 0;
            opacity: 0.9;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-header {
            background: var(--secondary);
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text);
        }

        .card-body {
            padding: 1rem;
        }

        /* Member Item */
        .member-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border);
        }

        .member-item:last-child {
            border-bottom: none;
        }

        .member-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .member-content {
            flex: 1;
        }

        .member-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .member-stats {
            font-size: 0.8rem;
            color: #666;
        }

        .member-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 36px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.7rem;
            min-height: 28px;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 768px;
                margin: 0 auto;
            }
            
            .grid-2 {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 767px) {
            .header-center {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='profile.php'">
                    <?php if (!empty($user['profile_picture'])): ?>
                        <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>"
                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <?php else: ?>
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="header-center">
                <input type="text" class="search-input" placeholder="Search community members...">
            </div>

            <div class="header-right">
                <div class="notification-icon" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <div class="notification-badge">2</div>
                </div>
            </div>
        </div>

        <div class="welcome-card">
            <h2><i class="fas fa-users"></i> Community</h2>
            <p>Connect with fellow Qur'an reciters</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="grid grid-2">
            <!-- Community Members -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-users"></i> Active Members
                    </div>
                    <small><?php echo count($communityMembers); ?> members</small>
                </div>
                <div class="card-body">
                    <?php if (!empty($communityMembers)): ?>
                        <?php foreach ($communityMembers as $member): ?>
                            <div class="member-item">
                                <div class="member-avatar">
                                    <?php if (!empty($member['profile_picture'])): ?>
                                        <img src="<?php echo htmlspecialchars($member['profile_picture']); ?>"
                                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                                    <?php else: ?>
                                        <?php echo strtoupper(substr($member['full_name'], 0, 1)); ?>
                                    <?php endif; ?>
                                </div>

                                <div class="member-content">
                                    <div class="member-name">
                                        <?php echo htmlspecialchars($member['full_name']); ?>
                                    </div>
                                    <div class="member-stats">
                                        <?php echo $member['recording_count']; ?> recordings •
                                        Joined <?php echo date('M Y', strtotime($member['join_date'])); ?>
                                    </div>
                                </div>

                                <div class="member-actions">
                                    <button class="btn btn-primary btn-sm" onclick="followMember(<?php echo $member['id']; ?>)">
                                        <i class="fas fa-user-plus"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 2rem; color: #666;">
                            <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>No community members yet</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Community Activity -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-activity"></i> Recent Activity
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentRecordings)): ?>
                        <?php foreach ($recentRecordings as $recording): ?>
                            <div class="member-item">
                                <div class="member-avatar">
                                    <?php if (!empty($recording['profile_picture'])): ?>
                                        <img src="<?php echo htmlspecialchars($recording['profile_picture']); ?>"
                                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                                    <?php else: ?>
                                        <?php echo strtoupper(substr($recording['full_name'], 0, 1)); ?>
                                    <?php endif; ?>
                                </div>

                                <div class="member-content">
                                    <div class="member-name">
                                        <?php echo htmlspecialchars($recording['full_name']); ?>
                                    </div>
                                    <div class="member-stats">
                                        Shared a recording • <?php echo $recording['formatted_date']; ?>
                                    </div>
                                </div>

                                <div class="member-actions">
                                    <a href="<?php echo htmlspecialchars($recording['file_path']); ?>"
                                       class="btn btn-primary btn-sm" target="_blank">
                                        <i class="fas fa-play"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 2rem; color: #666;">
                            <i class="fas fa-activity" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>No recent activity</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="community.php" class="nav-item active">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="streams.php" class="nav-item">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <script>
        // Follow member function
        function followMember(memberId) {
            alert('Following member #' + memberId + '! Feature coming soon.');
        }

        // Show notifications
        function showNotifications() {
            alert('Community Notifications:\n• New member joined\n• Someone liked your recording');
        }
    </script>
</body>
</html>
