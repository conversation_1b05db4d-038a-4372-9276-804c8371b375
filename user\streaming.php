<?php
$page_title = 'Live Streams';
require_once __DIR__ . '/../components/user_header.php';

// Fetch active streams (placeholder data)
$active_streams = [
    ['id' => 1, 'title' => 'Evening Qur\'an Recitation Session', 'user' => '<PERSON>', 'viewers' => 1250, 'thumbnail' => '/assets/images/stream_thumb1.jpg'],
    ['id' => 2, 'title' => 'Tajweed Q&A with <PERSON>', 'user' => 'Sheikh <PERSON>', 'viewers' => 890, 'thumbnail' => '/assets/images/stream_thumb2.jpg'],
    ['id' => 3, 'title' => 'Surah Al-Kahf Live Reading', 'user' => 'Fatima Zahra', 'viewers' => 740, 'thumbnail' => '/assets/images/stream_thumb3.jpg'],
    ['id' => 4, 'title' => 'Beginner\'s Guide to Makhaarij', 'user' => '<PERSON>', 'viewers' => 450, 'thumbnail' => '/assets/images/stream_thumb4.jpg'],
];
?>

<div class="row">
    <div class="col-12">
        <div class="dashboard-card mb-4">
            <div class="card-content text-center py-5">
                 <i class="fas fa-broadcast-tower fa-4x text-primary mb-4"></i>
                <h3 class="text-white">Live Streaming Hub</h3>
                <p class="text-white-50">Join live sessions from reciters and scholars around the world.</p>
                <a href="#" class="btn btn-primary"><i class="fas fa-video me-2"></i>Go Live</a>
            </div>
        </div>
    </div>
</div>

<h3 class="text-white mb-4">Active Live Streams</h3>
<div class="row g-4">
    <?php foreach($active_streams as $stream): ?>
        <div class="col-lg-3 col-md-6">
            <a href="#" class="stream-card-link">
                <div class="stream-card">
                    <div class="stream-thumbnail">
                        <img src="<?php echo $stream['thumbnail']; ?>" alt="<?php echo htmlspecialchars($stream['title']); ?>">
                        <div class="stream-live-badge"><i class="fas fa-circle"></i> LIVE</div>
                        <div class="stream-viewers-badge"><i class="fas fa-eye"></i> <?php echo number_format($stream['viewers']); ?></div>
                    </div>
                    <div class="stream-info">
                        <h5 class="stream-title"><?php echo htmlspecialchars($stream['title']); ?></h5>
                        <p class="stream-user text-white-50"><?php echo htmlspecialchars($stream['user']); ?></p>
                    </div>
                </div>
            </a>
        </div>
    <?php endforeach; ?>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>
<style>
.stream-card-link {
    text-decoration: none;
}
.stream-card {
    background: var(--dark-surface);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--dark-border);
}
.stream-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.4);
    border-color: var(--primary);
}
.stream-thumbnail {
    position: relative;
}
.stream-thumbnail img {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: cover;
}
.stream-live-badge, .stream-viewers-badge {
    position: absolute;
    top: 10px;
    padding: 0.3rem 0.6rem;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
}
.stream-live-badge {
    left: 10px;
    background-color: var(--danger);
}
.stream-live-badge .fa-circle {
    animation: pulse 1.5s infinite;
}
.stream-viewers-badge {
    right: 10px;
    background-color: rgba(0,0,0,0.6);
}
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
.stream-info {
    padding: 1rem;
}
.stream-title {
    color: var(--dark-text);
    margin-bottom: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.stream-user {
    margin-bottom: 0;
}
</style>