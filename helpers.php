<?php
// helpers.php

// Note: Most helper functions have been moved into config.php for better organization.
// This file is kept for compatibility but new helpers should be added to config.php.

if (!function_exists('timeAgo')) {
    function timeAgo($time_ago) {
        $time_ago = strtotime($time_ago);
        $cur_time   = time();
        $time_elapsed   = $cur_time - $time_ago;
        $seconds    = $time_elapsed ;
        $minutes    = round($time_elapsed / 60 );
        $hours      = round($time_elapsed / 3600);
        $days       = round($time_elapsed / 86400 );
        $weeks      = round($time_elapsed / 604800);
        $months     = round($time_elapsed / 2600640 );
        $years      = round($time_elapsed / 31207680 );

        if($seconds <= 60){ return "just now"; }
        else if($minutes <=60){ return ($minutes==1) ? "one minute ago" : "$minutes minutes ago"; }
        else if($hours <=24){ return ($hours==1) ? "an hour ago" : "$hours hrs ago"; }
        else if($days <= 7){ return ($days==1) ? "yesterday" : "$days days ago"; }
        else if($weeks <= 4.3){ return ($weeks==1) ? "a week ago" : "$weeks weeks ago"; }
        else if($months <=12){ return ($months==1) ? "a month ago" : "$months months ago"; }
        else{ return ($years==1) ? "one year ago" : "$years years ago"; }
    }
} 