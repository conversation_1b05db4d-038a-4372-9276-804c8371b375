<?php
$page_title = 'Video Call';
require_once 'components/user_header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Get user info
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Get callee information
$callee_id = $_GET['callee'] ?? null;
$callee_name = $_GET['name'] ?? 'Unknown User';

// If no callee specified, redirect to chat
if (!$callee_id) {
    header('Location: chat.php');
    exit;
}

// Get callee details from database
$callee_user = null;
if ($callee_id) {
    $callee_user = getUserById($callee_id);
    if ($callee_user) {
        $callee_name = $callee_user['full_name'];
    }
}
?>

<style>
body {
    background: #111;
    overflow: hidden;
    color: white;
}

.main-content {
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100vw !important;
    background: #000;
}

.video-call-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    background: #000;
    display: flex;
    flex-direction: column;
}

.video-area {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remote-video-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    background: #222;
    display: flex;
    align-items: center;
    justify-content: center;
}

#remoteVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #333;
}

.remote-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #888;
}

.remote-placeholder i {
    font-size: 4rem;
}

.user-info-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 1rem;
    backdrop-filter: blur(10px);
}

.user-info-overlay i {
    font-size: 1.5rem;
}

.local-video-wrapper {
    position: absolute;
    bottom: 100px;
    right: 20px;
    width: 280px;
    height: 160px;
    border: 3px solid #B10020;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    background: #333;
}

#localVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scaleX(-1);
}

.local-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #888;
    font-size: 2rem;
}

.call-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1.5rem;
    background: rgba(0,0,0,0.8);
    padding: 1.5rem 2rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
}

.control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: rgba(255,255,255,0.2);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.05);
}

.control-btn.active {
    background: #B10020;
}

.control-btn.muted {
    background: #dc3545;
}

.control-btn.end-call {
    background: #dc3545;
}

.control-btn.end-call:hover {
    background: #c82333;
}

.call-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(0,0,0,0.8);
    padding: 2rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.call-status {
    color: #B10020;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.call-timer {
    font-size: 2rem;
    font-weight: 600;
    color: white;
    margin-bottom: 0.5rem;
}

.call-quality {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

.quality-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.quality-good {
    background: #28a745;
}

.quality-fair {
    background: #ffc107;
}

.quality-poor {
    background: #dc3545;
}

@media (max-width: 768px) {
    .local-video-wrapper {
        width: 120px;
        height: 80px;
        bottom: 120px;
        right: 15px;
    }
    
    .call-controls {
        gap: 1rem;
        padding: 1rem 1.5rem;
    }
    
    .control-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}
</style>

<div class="video-call-container">
    <!-- Remote Video -->
    <div class="video-area">
        <div class="remote-video-wrapper">
            <video id="remoteVideo" autoplay playsinline style="display: none;"></video>
            <div class="remote-placeholder" id="remotePlaceholder">
                <i class="fas fa-user-circle"></i>
                <span>Connecting to <?php echo htmlspecialchars($callee_name); ?>...</span>
            </div>
        </div>
        
        <!-- User Info Overlay -->
        <div class="user-info-overlay">
            <i class="fas fa-user-circle"></i>
            <div>
                <div id="calleeName"><?php echo htmlspecialchars($callee_name); ?></div>
                <small id="callStatus">Connecting...</small>
            </div>
        </div>
        
        <!-- Call Quality -->
        <div class="call-quality">
            <span class="quality-indicator quality-good"></span>
            <span>Good</span>
        </div>
    </div>
    
    <!-- Local Video -->
    <div class="local-video-wrapper">
        <video id="localVideo" autoplay muted playsinline style="display: none;"></video>
        <div class="local-placeholder" id="localPlaceholder">
            <i class="fas fa-video-slash"></i>
        </div>
    </div>
    
    <!-- Call Info (shown when connecting) -->
    <div class="call-info" id="callInfo">
        <div class="call-status">Connecting...</div>
        <div class="call-timer" id="callTimer">00:00</div>
        <div><?php echo htmlspecialchars($callee_name); ?></div>
    </div>
    
    <!-- Call Controls -->
    <div class="call-controls">
        <button id="toggle-mic" class="control-btn" title="Toggle Microphone">
            <i class="fas fa-microphone"></i>
        </button>
        <button id="toggle-cam" class="control-btn" title="Toggle Camera">
            <i class="fas fa-video"></i>
        </button>
        <button id="toggle-screen" class="control-btn" title="Share Screen">
            <i class="fas fa-desktop"></i>
        </button>
        <button id="end-call" class="control-btn end-call" title="End Call">
            <i class="fas fa-phone-slash"></i>
        </button>
    </div>
</div>

<script>
// Video call functionality
class VideoCall {
    constructor() {
        this.localStream = null;
        this.remoteStream = null;
        this.isConnected = false;
        this.isMuted = false;
        this.isVideoOff = false;
        this.callStartTime = null;
        this.callTimer = null;
        
        this.initializeElements();
        this.initializeControls();
        this.startLocalVideo();
        this.simulateConnection();
    }
    
    initializeElements() {
        this.localVideo = document.getElementById('localVideo');
        this.remoteVideo = document.getElementById('remoteVideo');
        this.localPlaceholder = document.getElementById('localPlaceholder');
        this.remotePlaceholder = document.getElementById('remotePlaceholder');
        this.callInfo = document.getElementById('callInfo');
        this.callStatus = document.getElementById('callStatus');
        this.callTimer = document.getElementById('callTimer');
        this.micBtn = document.getElementById('toggle-mic');
        this.camBtn = document.getElementById('toggle-cam');
        this.screenBtn = document.getElementById('toggle-screen');
        this.endBtn = document.getElementById('end-call');
    }
    
    initializeControls() {
        this.micBtn.addEventListener('click', () => this.toggleMicrophone());
        this.camBtn.addEventListener('click', () => this.toggleCamera());
        this.screenBtn.addEventListener('click', () => this.toggleScreenShare());
        this.endBtn.addEventListener('click', () => this.endCall());
    }
    
    async startLocalVideo() {
        try {
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true
            });
            
            this.localVideo.srcObject = this.localStream;
            this.localVideo.style.display = 'block';
            this.localPlaceholder.style.display = 'none';
            
        } catch (error) {
            console.error('Error accessing media devices:', error);
            this.callStatus.textContent = 'Camera/Microphone access denied';
        }
    }
    
    simulateConnection() {
        // Simulate connection process
        setTimeout(() => {
            this.callStatus.textContent = 'Ringing...';
        }, 1000);
        
        setTimeout(() => {
            this.connectCall();
        }, 3000);
    }
    
    connectCall() {
        this.isConnected = true;
        this.callStartTime = Date.now();
        this.callStatus.textContent = 'Connected';
        this.callInfo.style.display = 'none';
        this.remotePlaceholder.innerHTML = '<i class="fas fa-user-circle"></i><span>Connected</span>';
        
        // Start call timer
        this.startCallTimer();
        
        // In a real app, you'd establish WebRTC connection here
        console.log('Call connected');
    }
    
    startCallTimer() {
        this.callTimer = setInterval(() => {
            if (this.callStartTime) {
                const elapsed = Math.floor((Date.now() - this.callStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // Update timer in user info overlay
                const timerElement = document.querySelector('.user-info-overlay small');
                if (timerElement) {
                    timerElement.textContent = display;
                }
            }
        }, 1000);
    }
    
    toggleMicrophone() {
        if (this.localStream) {
            this.localStream.getAudioTracks().forEach(track => {
                track.enabled = !track.enabled;
            });
            
            this.isMuted = !this.isMuted;
            this.micBtn.classList.toggle('muted', this.isMuted);
            this.micBtn.innerHTML = this.isMuted ? 
                '<i class="fas fa-microphone-slash"></i>' : 
                '<i class="fas fa-microphone"></i>';
        }
    }
    
    toggleCamera() {
        if (this.localStream) {
            this.localStream.getVideoTracks().forEach(track => {
                track.enabled = !track.enabled;
            });
            
            this.isVideoOff = !this.isVideoOff;
            this.camBtn.classList.toggle('muted', this.isVideoOff);
            this.camBtn.innerHTML = this.isVideoOff ? 
                '<i class="fas fa-video-slash"></i>' : 
                '<i class="fas fa-video"></i>';
                
            if (this.isVideoOff) {
                this.localVideo.style.display = 'none';
                this.localPlaceholder.style.display = 'flex';
                this.localPlaceholder.innerHTML = '<i class="fas fa-video-slash"></i>';
            } else {
                this.localVideo.style.display = 'block';
                this.localPlaceholder.style.display = 'none';
            }
        }
    }
    
    toggleScreenShare() {
        // Screen sharing placeholder
        alert('Screen sharing feature coming soon!');
    }
    
    endCall() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
        }
        
        if (this.callTimer) {
            clearInterval(this.callTimer);
        }
        
        // Redirect back to chat or dashboard
        window.location.href = 'chat.php';
    }
}

// Initialize video call when page loads
document.addEventListener('DOMContentLoaded', () => {
    new VideoCall();
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.videoCall && window.videoCall.localStream) {
        window.videoCall.localStream.getTracks().forEach(track => track.stop());
    }
});
</script>

<?php require_once 'components/user_footer.php'; ?> 