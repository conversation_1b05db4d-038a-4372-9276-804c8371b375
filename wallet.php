<?php
/**
 * Comprehensive Wallet Page for Recite! App
 * User wallet management, transactions, and points conversion
 */

require_once 'config/database.php';

// Set page title for navigation
$page_title = 'Wallet';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Get wallet and points balances
$walletBalance = floatval($user['wallet_balance'] ?? 0);
$pointsBalance = intval($user['points_balance'] ?? 0);

// Exchange rates
$pointBuyRate = 70;  // ₦70 per point when buying
$pointSellRate = 50; // ₦50 per point when selling

// Get recent transactions
$transactions = [];
try {
    $result = executeQuery(
        "SELECT *, DATE_FORMAT(created_at, '%M %d, %Y at %h:%i %p') as formatted_date
         FROM transactions 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 20",
        'i',
        [$userId]
    );
    
    while ($row = $result->fetch_assoc()) {
        $transactions[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching transactions: " . $e->getMessage());
}

// Get pending withdrawals
$pendingWithdrawals = [];
try {
    $result = executeQuery(
        "SELECT * FROM withdrawals 
         WHERE user_id = ? AND status = 'pending' 
         ORDER BY requested_at DESC",
        'i',
        [$userId]
    );
    
    while ($row = $result->fetch_assoc()) {
        $pendingWithdrawals[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching withdrawals: " . $e->getMessage());
}

// Include the header with sidebar
require_once 'components/user_header.php';
?>

<div class="container-fluid mt-4">
    <h1 class="mb-4 text-dark-red">My Wallet</h1>
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Balance Cards -->
            <div class="row mb-4">
                <div class="col-md-6 mb-4">
                    <div class="card wallet-card text-white shadow-lg">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-wallet me-2"></i>Naira Wallet</h5>
                            <p class="balance-display">₦<?php echo number_format($walletBalance, 2); ?></p>
                            <p class="mb-0 text-white-50">Available for withdrawals</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card points-card text-white shadow-lg">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-star me-2"></i>Points Balance</h5>
                            <p class="balance-display"><?php echo number_format($pointsBalance); ?></p>
                            <p class="mb-0 text-white-50">Convertible to cash</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Wallet Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#fundWalletModal"><i class="fas fa-plus-circle d-block mb-2"></i>Fund</button>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#buyPointsModal"><i class="fas fa-shopping-cart d-block mb-2"></i>Buy Points</button>
                        </div>
                         <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#sellPointsModal"><i class="fas fa-dollar-sign d-block mb-2"></i>Sell Points</button>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                             <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#withdrawModal"><i class="fas fa-university d-block mb-2"></i>Withdraw</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Recent Transactions</h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <?php foreach ($transactions as $tx): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="d-block text-capitalize"><?php echo str_replace('_', ' ', htmlspecialchars($tx['transaction_type'])); ?></strong>
                                <small class="text-muted"><?php echo htmlspecialchars($tx['description']); ?></small>
                            </div>
                            <div class="text-end">
                                <strong class="d-block <?php echo floatval($tx['amount'] ?? 0) >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php if ($tx['amount']): ?>
                                        ₦<?php echo number_format(floatval($tx['amount']), 2); ?>
                                    <?php else: ?>
                                        <?php echo number_format(intval($tx['points'])); ?> Points
                                    <?php endif; ?>
                                </strong>
                                <small class="text-muted"><?php echo $tx['formatted_date']; ?></small>
                            </div>
                        </li>
                        <?php endforeach; ?>
                        <?php if (empty($transactions)): ?>
                            <li class="list-group-item text-center text-muted">No transactions yet.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Exchange Info -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Exchange Rate</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Buy 1 Point</span>
                            <strong>₦<?php echo $pointBuyRate; ?></strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Sell 1 Point</span>
                            <strong>₦<?php echo $pointSellRate; ?></strong>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Pending Withdrawals -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Pending Withdrawals</h5>
                </div>
                <div class="card-body p-0">
                     <ul class="list-group list-group-flush">
                        <?php foreach ($pendingWithdrawals as $wd): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="d-block">₦<?php echo number_format($wd['amount'], 2); ?></strong>
                                <small class="text-muted">to <?php echo htmlspecialchars($wd['account_number']); ?></small>
                            </div>
                            <span class="badge bg-warning text-dark">Pending</span>
                        </li>
                        <?php endforeach; ?>
                         <?php if (empty($pendingWithdrawals)): ?>
                            <li class="list-group-item text-center text-muted">No pending withdrawals.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Fund Wallet Modal -->
<div class="modal fade" id="fundWalletModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Fund Wallet</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Enter amount to fund your wallet:</p>
                <div class="input-group">
                    <span class="input-group-text">₦</span>
                    <input type="number" id="fund-amount" class="form-control" placeholder="e.g., 5000">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="payWithPaystack()">Proceed to Pay</button>
            </div>
        </div>
    </div>
</div>

<!-- All other modals (Buy Points, Sell Points, Withdraw) would be structured similarly -->
<!-- ... -->

<?php require_once 'components/user_footer.php'; ?>
<script>
function payWithPaystack() {
    const amount = document.getElementById('fund-amount').value;
    if (!amount || amount <= 0) {
        alert('Please enter a valid amount.');
        return;
    }

    var handler = PaystackPop.setup({
        key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
        email: '<?php echo $user['email']; ?>',
        amount: amount * 100, // in kobo
        currency: 'NGN',
        ref: 'FUND_<?php echo $userId; ?>_' + Math.floor((Math.random() * 1000000000) + 1),
        metadata: {
            user_id: <?php echo $userId; ?>,
            purpose: 'wallet_funding'
        },
        callback: function(response) {
            window.location.href = 'verify_payment.php?reference=' + response.reference;
        },
        onClose: function() {
            alert('Payment was not completed.');
        }
    });
    handler.openIframe();
}

// Additional JS for other modals would go here
</script>

</body>
</html> 