<?php
/**
 * Comprehensive Wallet Page for Recite! App
 * User wallet management, transactions, and points conversion
 */

require_once 'config/database.php';

// Set page title for navigation
$page_title = 'Wallet';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Get wallet and points balances
$walletBalance = floatval($user['wallet_balance'] ?? 0);
$pointsBalance = intval($user['points_balance'] ?? 0);

// Exchange rates
$pointBuyRate = 70;  // ₦70 per point when buying
$pointSellRate = 50; // ₦50 per point when selling

// Get recent transactions
$transactions = [];
try {
    $result = executeQuery(
        "SELECT *, DATE_FORMAT(created_at, '%M %d, %Y at %h:%i %p') as formatted_date
         FROM transactions 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 20",
        'i',
        [$userId]
    );
    
    while ($row = $result->fetch_assoc()) {
        $transactions[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching transactions: " . $e->getMessage());
}

// Get pending withdrawals
$pendingWithdrawals = [];
try {
    $result = executeQuery(
        "SELECT * FROM withdrawals 
         WHERE user_id = ? AND status = 'pending' 
         ORDER BY requested_at DESC",
        'i',
        [$userId]
    );
    
    while ($row = $result->fetch_assoc()) {
        $pendingWithdrawals[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching withdrawals: " . $e->getMessage());
}

// Include the header with sidebar
require_once 'components/user_header.php';
?>

<div class="container-fluid mt-4">
    <h1 class="mb-4 text-dark-red">My Wallet</h1>
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Balance Cards -->
            <div class="row mb-4">
                <div class="col-md-6 mb-4">
                    <div class="card wallet-card text-white shadow-lg">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-wallet me-2"></i>Naira Wallet</h5>
                            <p class="balance-display">₦<?php echo number_format($walletBalance, 2); ?></p>
                            <p class="mb-0 text-white-50">Available for withdrawals</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card points-card text-white shadow-lg">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-star me-2"></i>Points Balance</h5>
                            <p class="balance-display"><?php echo number_format($pointsBalance); ?></p>
                            <p class="mb-0 text-white-50">Convertible to cash</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Wallet Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#fundWalletModal"><i class="fas fa-plus-circle d-block mb-2"></i>Fund</button>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#buyPointsModal"><i class="fas fa-shopping-cart d-block mb-2"></i>Buy Points</button>
                        </div>
                         <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#sellPointsModal"><i class="fas fa-dollar-sign d-block mb-2"></i>Sell Points</button>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                             <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#withdrawModal"><i class="fas fa-university d-block mb-2"></i>Withdraw</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Recent Transactions</h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <?php foreach ($transactions as $tx): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="d-block text-capitalize"><?php echo str_replace('_', ' ', htmlspecialchars($tx['transaction_type'])); ?></strong>
                                <small class="text-muted"><?php echo htmlspecialchars($tx['description']); ?></small>
                            </div>
                            <div class="text-end">
                                <strong class="d-block <?php echo floatval($tx['amount'] ?? 0) >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php if ($tx['amount']): ?>
                                        ₦<?php echo number_format(floatval($tx['amount']), 2); ?>
                                    <?php else: ?>
                                        <?php echo number_format(intval($tx['points'])); ?> Points
                                    <?php endif; ?>
                                </strong>
                                <small class="text-muted"><?php echo $tx['formatted_date']; ?></small>
                            </div>
                        </li>
                        <?php endforeach; ?>
                        <?php if (empty($transactions)): ?>
                            <li class="list-group-item text-center text-muted">No transactions yet.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Exchange Info -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Exchange Rate</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Buy 1 Point</span>
                            <strong>₦<?php echo $pointBuyRate; ?></strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Sell 1 Point</span>
                            <strong>₦<?php echo $pointSellRate; ?></strong>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Pending Withdrawals -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Pending Withdrawals</h5>
                </div>
                <div class="card-body p-0">
                     <ul class="list-group list-group-flush">
                        <?php foreach ($pendingWithdrawals as $wd): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="d-block">₦<?php echo number_format($wd['amount'], 2); ?></strong>
                                <small class="text-muted">to <?php echo htmlspecialchars($wd['account_number']); ?></small>
                            </div>
                            <span class="badge bg-warning text-dark">Pending</span>
                        </li>
                        <?php endforeach; ?>
                         <?php if (empty($pendingWithdrawals)): ?>
                            <li class="list-group-item text-center text-muted">No pending withdrawals.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Fund Wallet Modal -->
<div class="modal fade" id="fundWalletModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Fund Wallet</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Enter amount to fund your wallet:</p>
                <div class="input-group">
                    <span class="input-group-text">₦</span>
                    <input type="number" id="fund-amount" class="form-control" placeholder="e.g., 5000">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="payWithPaystack()">Proceed to Pay</button>
            </div>
        </div>
    </div>
</div>

<!-- Buy Points Modal -->
<div class="modal fade" id="buyPointsModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Buy Points</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Exchange rate: ₦<?php echo $pointBuyRate; ?> per point</p>
                <div class="mb-3">
                    <label for="buy-points-amount" class="form-label">Number of points to buy:</label>
                    <input type="number" id="buy-points-amount" class="form-control" placeholder="e.g., 10" min="1">
                </div>
                <div class="alert alert-info">
                    <strong>Cost:</strong> ₦<span id="buy-cost">0</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="buyPoints()">Buy Points</button>
            </div>
        </div>
    </div>
</div>

<!-- Sell Points Modal -->
<div class="modal fade" id="sellPointsModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sell Points</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Exchange rate: ₦<?php echo $pointSellRate; ?> per point</p>
                <p>Available points: <strong><?php echo number_format($pointsBalance); ?></strong></p>
                <div class="mb-3">
                    <label for="sell-points-amount" class="form-label">Number of points to sell:</label>
                    <input type="number" id="sell-points-amount" class="form-control" placeholder="e.g., 5" min="1" max="<?php echo $pointsBalance; ?>">
                </div>
                <div class="alert alert-success">
                    <strong>You'll receive:</strong> ₦<span id="sell-amount">0</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="sellPoints()">Sell Points</button>
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Withdraw Funds</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Available balance: <strong>₦<?php echo number_format($walletBalance, 2); ?></strong></p>
                <form id="withdrawForm">
                    <div class="mb-3">
                        <label for="withdraw-amount" class="form-label">Amount to withdraw:</label>
                        <div class="input-group">
                            <span class="input-group-text">₦</span>
                            <input type="number" id="withdraw-amount" class="form-control" placeholder="e.g., 1000" min="100" max="<?php echo $walletBalance; ?>">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="bank-name" class="form-label">Bank Name:</label>
                        <input type="text" id="bank-name" class="form-control" placeholder="e.g., First Bank" required>
                    </div>
                    <div class="mb-3">
                        <label for="account-number" class="form-label">Account Number:</label>
                        <input type="text" id="account-number" class="form-control" placeholder="e.g., **********" required>
                    </div>
                    <div class="mb-3">
                        <label for="account-name" class="form-label">Account Holder Name:</label>
                        <input type="text" id="account-name" class="form-control" placeholder="e.g., John Doe" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="requestWithdrawal()">Request Withdrawal</button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'components/user_footer.php'; ?>
<script>
function payWithPaystack() {
    const amount = document.getElementById('fund-amount').value;
    if (!amount || amount <= 0) {
        alert('Please enter a valid amount.');
        return;
    }

    var handler = PaystackPop.setup({
        key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
        email: '<?php echo $user['email']; ?>',
        amount: amount * 100, // in kobo
        currency: 'NGN',
        ref: 'FUND_<?php echo $userId; ?>_' + Math.floor((Math.random() * 1000000000) + 1),
        metadata: {
            user_id: <?php echo $userId; ?>,
            purpose: 'wallet_funding'
        },
        callback: function(response) {
            window.location.href = 'verify_payment.php?reference=' + response.reference;
        },
        onClose: function() {
            alert('Payment was not completed.');
        }
    });
    handler.openIframe();
}

// Buy Points functionality
document.getElementById('buy-points-amount').addEventListener('input', function() {
    const points = parseInt(this.value) || 0;
    const cost = points * <?php echo $pointBuyRate; ?>;
    document.getElementById('buy-cost').textContent = cost.toLocaleString();
});

function buyPoints() {
    const points = parseInt(document.getElementById('buy-points-amount').value);
    const cost = points * <?php echo $pointBuyRate; ?>;

    if (!points || points <= 0) {
        alert('Please enter a valid number of points.');
        return;
    }

    if (cost > <?php echo $walletBalance; ?>) {
        alert('Insufficient wallet balance. Please fund your wallet first.');
        return;
    }

    if (confirm(`Buy ${points} points for ₦${cost.toLocaleString()}?`)) {
        fetch('api/wallet_transaction.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'buy_points',
                points: points,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Points purchased successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to buy points'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error occurred');
        });
    }
}

// Sell Points functionality
document.getElementById('sell-points-amount').addEventListener('input', function() {
    const points = parseInt(this.value) || 0;
    const amount = points * <?php echo $pointSellRate; ?>;
    document.getElementById('sell-amount').textContent = amount.toLocaleString();
});

function sellPoints() {
    const points = parseInt(document.getElementById('sell-points-amount').value);
    const amount = points * <?php echo $pointSellRate; ?>;

    if (!points || points <= 0) {
        alert('Please enter a valid number of points.');
        return;
    }

    if (points > <?php echo $pointsBalance; ?>) {
        alert('You don\'t have enough points.');
        return;
    }

    if (confirm(`Sell ${points} points for ₦${amount.toLocaleString()}?`)) {
        fetch('api/wallet_transaction.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'sell_points',
                points: points,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Points sold successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to sell points'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error occurred');
        });
    }
}

// Withdrawal functionality
function requestWithdrawal() {
    const amount = parseFloat(document.getElementById('withdraw-amount').value);
    const bankName = document.getElementById('bank-name').value.trim();
    const accountNumber = document.getElementById('account-number').value.trim();
    const accountName = document.getElementById('account-name').value.trim();

    if (!amount || amount <= 0) {
        alert('Please enter a valid amount.');
        return;
    }

    if (amount > <?php echo $walletBalance; ?>) {
        alert('Insufficient wallet balance.');
        return;
    }

    if (!bankName || !accountNumber || !accountName) {
        alert('Please fill in all bank details.');
        return;
    }

    if (confirm(`Request withdrawal of ₦${amount.toLocaleString()} to ${bankName} account ${accountNumber}?`)) {
        fetch('api/wallet_transaction.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'request_withdrawal',
                amount: amount,
                bank_name: bankName,
                account_number: accountNumber,
                account_name: accountName,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Withdrawal request submitted successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to request withdrawal'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error occurred');
        });
    }
}
</script>

</body>
</html> 