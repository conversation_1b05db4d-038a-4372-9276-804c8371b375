<?php
/**
 * Comprehensive Wallet Page for Recite! App
 * User wallet management, transactions, and points conversion
 */

require_once 'config/database.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Wallet';

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Include points system
require_once 'includes/points_system.php';

// Get wallet and points balances
$walletBalance = floatval($user['wallet_balance'] ?? 0);
$userPoints = getUserPointsSummary($userId);
$pointsBalance = $userPoints['total_points'];

// Exchange rates
$pointBuyRate = 70;  // ₦70 per point when buying
$pointSellRate = 50; // ₦50 per point when selling

// Handle points transactions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'buy_points') {
        $pointsToBuy = intval($_POST['points_amount']);
        $totalCost = $pointsToBuy * $pointBuyRate;

        if ($pointsToBuy > 0 && $walletBalance >= $totalCost) {
            $conn = getConnection();
            $conn->begin_transaction();

            try {
                // Deduct money from wallet
                $newWalletBalance = $walletBalance - $totalCost;
                $updateWallet = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
                $updateWallet->bind_param("di", $newWalletBalance, $userId);
                $updateWallet->execute();

                // Award points
                awardPoints($userId, $pointsToBuy, 'admin_adjustment', "Purchased {$pointsToBuy} points for ₦{$totalCost}");

                $conn->commit();
                $message = "Successfully purchased {$pointsToBuy} points for ₦{$totalCost}!";

                // Refresh balances
                $walletBalance = $newWalletBalance;
                $userPoints = getUserPointsSummary($userId);
                $pointsBalance = $userPoints['total_points'];

            } catch (Exception $e) {
                $conn->rollback();
                $error = "Transaction failed. Please try again.";
            }
        } else {
            $error = "Insufficient wallet balance or invalid amount.";
        }
    } elseif ($action === 'sell_points') {
        $pointsToSell = intval($_POST['points_amount']);
        $totalEarning = $pointsToSell * $pointSellRate;

        if ($pointsToSell > 0 && $pointsBalance >= $pointsToSell) {
            $conn = getConnection();
            $conn->begin_transaction();

            try {
                // Deduct points
                awardPoints($userId, -$pointsToSell, 'admin_adjustment', "Sold {$pointsToSell} points for ₦{$totalEarning}");

                // Add money to wallet
                $newWalletBalance = $walletBalance + $totalEarning;
                $updateWallet = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
                $updateWallet->bind_param("di", $newWalletBalance, $userId);
                $updateWallet->execute();

                $conn->commit();
                $message = "Successfully sold {$pointsToSell} points for ₦{$totalEarning}!";

                // Refresh balances
                $walletBalance = $newWalletBalance;
                $userPoints = getUserPointsSummary($userId);
                $pointsBalance = $userPoints['total_points'];

            } catch (Exception $e) {
                $conn->rollback();
                $error = "Transaction failed. Please try again.";
            }
        } else {
            $error = "Insufficient points balance or invalid amount.";
        }
    }
}

// Get recent transactions
$transactions = [];
try {
    $result = executeQuery(
        "SELECT *, DATE_FORMAT(created_at, '%M %d, %Y at %h:%i %p') as formatted_date
         FROM transactions 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 20",
        'i',
        [$userId]
    );
    
    while ($row = $result->fetch_assoc()) {
        $transactions[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching transactions: " . $e->getMessage());
}

// Get pending withdrawals
$pendingWithdrawals = [];
try {
    $result = executeQuery(
        "SELECT * FROM withdrawals 
         WHERE user_id = ? AND status = 'pending' 
         ORDER BY requested_at DESC",
        'i',
        [$userId]
    );
    
    while ($row = $result->fetch_assoc()) {
        $pendingWithdrawals[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching withdrawals: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design - Same as Dashboard */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .user-info {
            font-size: 0.9rem;
            margin-top: 0.5rem;
            opacity: 0.9;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-header {
            background: var(--secondary);
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text);
        }

        .card-body {
            padding: 1rem;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 44px; /* Touch-friendly */
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: #333;
        }

        .btn-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
            min-height: 36px;
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border: 1px solid transparent;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        /* Form Styles */
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(26, 95, 63, 0.1);
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        .grid-4 {
            grid-template-columns: repeat(2, 1fr);
        }

        /* Balance Display */
        .balance-card {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            text-align: center;
            padding: 2rem 1rem;
        }

        .balance-amount {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .balance-label {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        /* Transaction Item */
        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border);
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-info h6 {
            margin: 0;
            font-weight: 600;
        }

        .transaction-info small {
            color: #666;
        }

        .transaction-amount {
            text-align: right;
        }

        .amount-positive {
            color: var(--success);
        }

        .amount-negative {
            color: var(--danger);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 768px;
                margin: 0 auto;
            }

            .grid-4 {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 767px) {
            .header h1 {
                font-size: 1.3rem;
            }

            .user-info {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-wallet"></i> My Wallet</h1>
        <div class="user-info">
            Welcome, <?php echo htmlspecialchars($user['full_name']); ?>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Balance Cards Grid -->
        <div class="grid grid-2" style="margin-bottom: 2rem;">
            <!-- Wallet Balance -->
            <div class="card">
                <div class="balance-card">
                    <div class="balance-amount">₦<?php echo number_format($walletBalance, 2); ?></div>
                    <div class="balance-label">Wallet Balance</div>
                </div>
            </div>

            <!-- Points Balance -->
            <div class="card">
                <div class="balance-card" style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);">
                    <div class="balance-amount"><?php echo number_format($pointsBalance); ?></div>
                    <div class="balance-label">Points Balance</div>
                </div>
            </div>
        </div>

        <!-- Points Exchange Section -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-exchange-alt"></i> Points Exchange
                </div>
            </div>
            <div class="card-body">
                <div class="grid grid-2" style="gap: 2rem;">
                    <!-- Buy Points -->
                    <div style="text-align: center; padding: 1rem; border: 1px solid var(--border); border-radius: 8px;">
                        <h6 style="color: var(--primary); margin-bottom: 1rem;">
                            <i class="fas fa-shopping-cart"></i> Buy Points
                        </h6>
                        <p style="font-size: 0.9rem; color: #666; margin-bottom: 1rem;">
                            Rate: ₦<?php echo $pointBuyRate; ?> per point
                        </p>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="buy_points">
                            <div style="margin-bottom: 1rem;">
                                <input type="number" name="points_amount" placeholder="Points to buy"
                                       min="1" max="<?php echo floor($walletBalance / $pointBuyRate); ?>"
                                       class="form-control" style="text-align: center;" required>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Buy Points
                            </button>
                        </form>
                    </div>

                    <!-- Sell Points -->
                    <div style="text-align: center; padding: 1rem; border: 1px solid var(--border); border-radius: 8px;">
                        <h6 style="color: #ff6b35; margin-bottom: 1rem;">
                            <i class="fas fa-coins"></i> Sell Points
                        </h6>
                        <p style="font-size: 0.9rem; color: #666; margin-bottom: 1rem;">
                            Rate: ₦<?php echo $pointSellRate; ?> per point
                        </p>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="sell_points">
                            <div style="margin-bottom: 1rem;">
                                <input type="number" name="points_amount" placeholder="Points to sell"
                                       min="1" max="<?php echo $pointsBalance; ?>"
                                       class="form-control" style="text-align: center;" required>
                            </div>
                            <button type="submit" class="btn btn-warning btn-sm">
                                <i class="fas fa-minus"></i> Sell Points
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Points Earning Info -->
                <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                    <h6 style="margin-bottom: 1rem;"><i class="fas fa-info-circle"></i> How to Earn Points</h6>
                    <div class="grid grid-2" style="gap: 1rem; font-size: 0.85rem;">
                        <div>
                            <strong>🎯 Recitations:</strong> 1+ points per completion<br>
                            <strong>🔥 Streak Bonus:</strong> 100 points for 100 recitations in 72hrs<br>
                            <strong>📤 Social:</strong> 1 point per like/comment/share received
                        </div>
                        <div>
                            <strong>📩 Referrals:</strong> 1 point per signup<br>
                            <strong>🏆 Leaderboard:</strong> Weekly ranking bonuses<br>
                            <strong>⭐ Performance:</strong> Accuracy & duration bonuses
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <!-- Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Wallet Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#fundWalletModal"><i class="fas fa-plus-circle d-block mb-2"></i>Fund</button>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#buyPointsModal"><i class="fas fa-shopping-cart d-block mb-2"></i>Buy Points</button>
                        </div>
                         <div class="col-md-3 text-center mb-3">
                            <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#sellPointsModal"><i class="fas fa-dollar-sign d-block mb-2"></i>Sell Points</button>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                             <button class="btn btn-lg btn-outline-danger" data-bs-toggle="modal" data-bs-target="#withdrawModal"><i class="fas fa-university d-block mb-2"></i>Withdraw</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Recent Transactions</h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <?php foreach ($transactions as $tx): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="d-block text-capitalize"><?php echo str_replace('_', ' ', htmlspecialchars($tx['transaction_type'])); ?></strong>
                                <small class="text-muted"><?php echo htmlspecialchars($tx['description']); ?></small>
                            </div>
                            <div class="text-end">
                                <strong class="d-block <?php echo floatval($tx['amount'] ?? 0) >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php if ($tx['amount']): ?>
                                        ₦<?php echo number_format(floatval($tx['amount']), 2); ?>
                                    <?php else: ?>
                                        <?php echo number_format(intval($tx['points'])); ?> Points
                                    <?php endif; ?>
                                </strong>
                                <small class="text-muted"><?php echo $tx['formatted_date']; ?></small>
                            </div>
                        </li>
                        <?php endforeach; ?>
                        <?php if (empty($transactions)): ?>
                            <li class="list-group-item text-center text-muted">No transactions yet.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Exchange Info -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Exchange Rate</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Buy 1 Point</span>
                            <strong>₦<?php echo $pointBuyRate; ?></strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Sell 1 Point</span>
                            <strong>₦<?php echo $pointSellRate; ?></strong>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Pending Withdrawals -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Pending Withdrawals</h5>
                </div>
                <div class="card-body p-0">
                     <ul class="list-group list-group-flush">
                        <?php foreach ($pendingWithdrawals as $wd): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="d-block">₦<?php echo number_format($wd['amount'], 2); ?></strong>
                                <small class="text-muted">to <?php echo htmlspecialchars($wd['account_number']); ?></small>
                            </div>
                            <span class="badge bg-warning text-dark">Pending</span>
                        </li>
                        <?php endforeach; ?>
                         <?php if (empty($pendingWithdrawals)): ?>
                            <li class="list-group-item text-center text-muted">No pending withdrawals.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Fund Wallet Modal -->
<div class="modal fade" id="fundWalletModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Fund Wallet</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Enter amount to fund your wallet:</p>
                <div class="input-group">
                    <span class="input-group-text">₦</span>
                    <input type="number" id="fund-amount" class="form-control" placeholder="e.g., 5000">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="payWithPaystack()">Proceed to Pay</button>
            </div>
        </div>
    </div>
</div>

<!-- Buy Points Modal -->
<div class="modal fade" id="buyPointsModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Buy Points</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Exchange rate: ₦<?php echo $pointBuyRate; ?> per point</p>
                <div class="mb-3">
                    <label for="buy-points-amount" class="form-label">Number of points to buy:</label>
                    <input type="number" id="buy-points-amount" class="form-control" placeholder="e.g., 10" min="1">
                </div>
                <div class="alert alert-info">
                    <strong>Cost:</strong> ₦<span id="buy-cost">0</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="buyPoints()">Buy Points</button>
            </div>
        </div>
    </div>
</div>

<!-- Sell Points Modal -->
<div class="modal fade" id="sellPointsModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sell Points</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Exchange rate: ₦<?php echo $pointSellRate; ?> per point</p>
                <p>Available points: <strong><?php echo number_format($pointsBalance); ?></strong></p>
                <div class="mb-3">
                    <label for="sell-points-amount" class="form-label">Number of points to sell:</label>
                    <input type="number" id="sell-points-amount" class="form-control" placeholder="e.g., 5" min="1" max="<?php echo $pointsBalance; ?>">
                </div>
                <div class="alert alert-success">
                    <strong>You'll receive:</strong> ₦<span id="sell-amount">0</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="sellPoints()">Sell Points</button>
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Withdraw Funds</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Available balance: <strong>₦<?php echo number_format($walletBalance, 2); ?></strong></p>
                <form id="withdrawForm">
                    <div class="mb-3">
                        <label for="withdraw-amount" class="form-label">Amount to withdraw:</label>
                        <div class="input-group">
                            <span class="input-group-text">₦</span>
                            <input type="number" id="withdraw-amount" class="form-control" placeholder="e.g., 1000" min="100" max="<?php echo $walletBalance; ?>">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="bank-name" class="form-label">Bank Name:</label>
                        <input type="text" id="bank-name" class="form-control" placeholder="e.g., First Bank" required>
                    </div>
                    <div class="mb-3">
                        <label for="account-number" class="form-label">Account Number:</label>
                        <input type="text" id="account-number" class="form-control" placeholder="e.g., **********" required>
                    </div>
                    <div class="mb-3">
                        <label for="account-name" class="form-label">Account Holder Name:</label>
                        <input type="text" id="account-name" class="form-control" placeholder="e.g., John Doe" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="requestWithdrawal()">Request Withdrawal</button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'components/user_footer.php'; ?>
<script>
function payWithPaystack() {
    const amount = document.getElementById('fund-amount').value;
    if (!amount || amount <= 0) {
        alert('Please enter a valid amount.');
        return;
    }

    var handler = PaystackPop.setup({
        key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
        email: '<?php echo $user['email']; ?>',
        amount: amount * 100, // in kobo
        currency: 'NGN',
        ref: 'FUND_<?php echo $userId; ?>_' + Math.floor((Math.random() * 1000000000) + 1),
        metadata: {
            user_id: <?php echo $userId; ?>,
            purpose: 'wallet_funding'
        },
        callback: function(response) {
            window.location.href = 'verify_payment.php?reference=' + response.reference;
        },
        onClose: function() {
            alert('Payment was not completed.');
        }
    });
    handler.openIframe();
}

// Buy Points functionality
document.getElementById('buy-points-amount').addEventListener('input', function() {
    const points = parseInt(this.value) || 0;
    const cost = points * <?php echo $pointBuyRate; ?>;
    document.getElementById('buy-cost').textContent = cost.toLocaleString();
});

function buyPoints() {
    const points = parseInt(document.getElementById('buy-points-amount').value);
    const cost = points * <?php echo $pointBuyRate; ?>;

    if (!points || points <= 0) {
        alert('Please enter a valid number of points.');
        return;
    }

    if (cost > <?php echo $walletBalance; ?>) {
        alert('Insufficient wallet balance. Please fund your wallet first.');
        return;
    }

    if (confirm(`Buy ${points} points for ₦${cost.toLocaleString()}?`)) {
        fetch('api/wallet_transaction.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'buy_points',
                points: points,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Points purchased successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to buy points'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error occurred');
        });
    }
}

// Sell Points functionality
document.getElementById('sell-points-amount').addEventListener('input', function() {
    const points = parseInt(this.value) || 0;
    const amount = points * <?php echo $pointSellRate; ?>;
    document.getElementById('sell-amount').textContent = amount.toLocaleString();
});

function sellPoints() {
    const points = parseInt(document.getElementById('sell-points-amount').value);
    const amount = points * <?php echo $pointSellRate; ?>;

    if (!points || points <= 0) {
        alert('Please enter a valid number of points.');
        return;
    }

    if (points > <?php echo $pointsBalance; ?>) {
        alert('You don\'t have enough points.');
        return;
    }

    if (confirm(`Sell ${points} points for ₦${amount.toLocaleString()}?`)) {
        fetch('api/wallet_transaction.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'sell_points',
                points: points,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Points sold successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to sell points'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error occurred');
        });
    }
}

// Withdrawal functionality
function requestWithdrawal() {
    const amount = parseFloat(document.getElementById('withdraw-amount').value);
    const bankName = document.getElementById('bank-name').value.trim();
    const accountNumber = document.getElementById('account-number').value.trim();
    const accountName = document.getElementById('account-name').value.trim();

    if (!amount || amount <= 0) {
        alert('Please enter a valid amount.');
        return;
    }

    if (amount > <?php echo $walletBalance; ?>) {
        alert('Insufficient wallet balance.');
        return;
    }

    if (!bankName || !accountNumber || !accountName) {
        alert('Please fill in all bank details.');
        return;
    }

    if (confirm(`Request withdrawal of ₦${amount.toLocaleString()} to ${bankName} account ${accountNumber}?`)) {
        fetch('api/wallet_transaction.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'request_withdrawal',
                amount: amount,
                bank_name: bankName,
                account_number: accountNumber,
                account_name: accountName,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Withdrawal request submitted successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to request withdrawal'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error occurred');
        });
    }
}
</script>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item active">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="community.php" class="nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="streams.php" class="nav-item">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <style>
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: #1a5f3f;
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Add bottom padding to body */
        body {
            padding-bottom: 80px;
        }
    </style>

</body>
</html>