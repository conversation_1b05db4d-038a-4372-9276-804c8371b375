/* Modern Qur'an Recite App UI Framework */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    /* Modern Color Palette - Dark Red Theme */
    --primary: #B10020;
    --primary-light: #D32F2F;
    --primary-dark: #8B0000;
    --secondary: #B10020;
    --success: #B10020;
    --danger: #8B0000;
    --warning: #D32F2F;
    --info: #B10020;
    
    /* Grays */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Dark Theme */
    --dark-bg: #0f172a;
    --dark-surface: #1e293b;
    --dark-card: #334155;
    --dark-border: #475569;
    --dark-text: #f1f5f9;
    --dark-text-muted: #94a3b8;
    
    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
}

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--gray-900);
    background: #FFFFFF;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Modern Sidebar for Admin */
.admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--dark-border);
    z-index: 1000;
    transition: var(--transition);
    transform: translateX(0);
}

.admin-sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--dark-border);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-header .logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.sidebar-header h2 {
    color: var(--dark-text);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
    transition: var(--transition);
}

.admin-sidebar.collapsed .sidebar-header h2 {
    opacity: 0;
    width: 0;
}

.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
    overflow-y: auto;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: var(--dark-text-muted);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: var(--transition);
    font-weight: 500;
    font-size: 0.875rem;
}

.nav-link:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
    transform: translateX(4px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 1.125rem;
}

.admin-sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
}

.sidebar-toggle {
    position: absolute;
    top: 1rem;
    right: -12px;
    width: 24px;
    height: 24px;
    background: var(--primary);
    border: none;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* Main Content for Admin */
.admin-main {
    margin-left: 280px;
    min-height: 100vh;
    background: var(--gray-50);
    transition: var(--transition);
}

.admin-sidebar.collapsed + .admin-main {
    margin-left: 80px;
}

/* Modern Dashboard Cards */
.dashboard-container {
    padding: 2rem;
    max-width: 100%;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: var(--gray-600);
    font-size: 1rem;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
}

.stat-icon.success {
    background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
}

.stat-icon.warning {
    background: linear-gradient(135deg, var(--warning) 0%, #fbbf24 100%);
}

.stat-icon.danger {
    background: linear-gradient(135deg, var(--danger) 0%, #f87171 100%);
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: var(--gray-900);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--danger);
}

/* Modern Data Table */
.data-table-container {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.table-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table th {
    background: var(--gray-50);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    border-bottom: 1px solid var(--gray-200);
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-100);
    color: var(--gray-600);
    font-size: 0.875rem;
}

.modern-table tbody tr:hover {
    background: var(--gray-50);
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    line-height: 1;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
    color: white;
    box-shadow: var(--shadow);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger) 0%, #f87171 100%);
    color: white;
    box-shadow: var(--shadow);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* Modern Forms */
.form-floating {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-control {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: var(--transition);
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: var(--gray-500);
    transition: var(--transition);
    pointer-events: none;
}

.form-control:focus + .form-label,
.form-control:not(:placeholder-shown) + .form-label {
    transform: translateY(-0.5rem) scale(0.875);
    color: var(--primary);
    background: white;
    padding: 0 0.25rem;
}

/* User Dashboard Styles */
.user-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
}

.user-nav {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 1rem 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow);
}

.user-nav .navbar-brand {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.user-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    transition: var(--transition);
}

.user-nav .nav-link:hover,
.user-nav .nav-link.active {
    color: white;
    background: var(--glass-bg);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--glass-shadow);
    transition: var(--transition);
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    background: var(--glass-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.card-title {
    color: white;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
}

.card-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .dashboard-container {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .user-dashboard {
        padding: 1rem;
    }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        color: var(--dark-text);
    }
    
    .stat-card {
        background: var(--dark-card);
        border-color: var(--dark-border);
    }
    
    .data-table-container {
        background: var(--dark-card);
        border-color: var(--dark-border);
    }
    
    .form-control {
        background: var(--dark-surface);
        border-color: var(--dark-border);
        color: var(--dark-text);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.d-flex { display: flex; }
.d-none { display: none; }
.d-block { display: block; }
.align-items-center { align-items: center; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.w-100 { width: 100%; }
.h-100 { height: 100%; }