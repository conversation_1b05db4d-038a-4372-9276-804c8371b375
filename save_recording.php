<?php
// Disable error display to prevent HTML in JSON response
error_reporting(0);
ini_set('display_errors', 0);

// Set JSON response header first
header('Content-Type: application/json');

require_once 'config/database.php';

try {
    // Require login
    requireLogin();

    $userId = $_SESSION['user_id'];

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_recording') {

        if (!isset($_FILES['recording']) || $_FILES['recording']['error'] !== UPLOAD_ERR_OK) {
            $error = $_FILES['recording']['error'] ?? 'Unknown error';
            echo json_encode(['success' => false, 'message' => 'File upload error: ' . $error]);
            exit;
        }
    
    $uploadedFile = $_FILES['recording'];
    $originalName = $uploadedFile['name'];
    $tempPath = $uploadedFile['tmp_name'];
    
    // Create uploads directory if it doesn't exist
    $uploadsDir = 'uploads/recordings/';
    if (!is_dir($uploadsDir)) {
        mkdir($uploadsDir, 0755, true);
    }
    
    // Generate unique filename
    $fileExtension = pathinfo($originalName, PATHINFO_EXTENSION);
    $uniqueFilename = 'recording_' . $userId . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadsDir . $uniqueFilename;
    
    // Move uploaded file
    if (move_uploaded_file($tempPath, $filePath)) {
        
        // Save to database
        $conn = getConnection();
        $title = 'Recitation Recording - ' . date('M j, Y H:i');
        $description = 'User recording from dashboard';

        $query = "INSERT INTO screen_records (user_id, title, description, file_path, file_size, created_at)
                  VALUES (?, ?, ?, ?, ?, NOW())";

        $stmt = $conn->prepare($query);
        $fileSize = filesize($filePath);
        $stmt->bind_param("isssi", $userId, $title, $description, $filePath, $fileSize);
        
        if ($stmt->execute()) {
            $recordingId = $conn->insert_id;

            // Award points for recording completion
            require_once 'includes/points_system.php';

            // Estimate duration based on file size (rough calculation)
            $estimatedDuration = max(30, min(300, $fileSize / (1024 * 50))); // 30s to 5min estimate
            $pointsEarned = processRecitationCompletion($userId, $recordingId, 'screen_record', $estimatedDuration, 85);

            echo json_encode([
                'success' => true,
                'message' => "Recording saved successfully! You earned {$pointsEarned} points!",
                'file_path' => $filePath,
                'recording_id' => $recordingId,
                'points_earned' => $pointsEarned
            ]);
        } else {
            // Delete the file if database insert failed
            unlink($filePath);
            echo json_encode(['success' => false, 'message' => 'Database error: ' . $stmt->error]);
        }

    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save recording file']);
    }

} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Server error: Authentication failed']);
}
?>
