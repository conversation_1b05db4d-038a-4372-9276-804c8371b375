<?php
require_once 'config/database.php';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];

// Set JSON response header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_recording') {
    
    if (!isset($_FILES['recording']) || $_FILES['recording']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(['success' => false, 'message' => 'No recording file uploaded']);
        exit;
    }
    
    $uploadedFile = $_FILES['recording'];
    $originalName = $uploadedFile['name'];
    $tempPath = $uploadedFile['tmp_name'];
    
    // Create uploads directory if it doesn't exist
    $uploadsDir = 'uploads/recordings/';
    if (!is_dir($uploadsDir)) {
        mkdir($uploadsDir, 0755, true);
    }
    
    // Generate unique filename
    $fileExtension = pathinfo($originalName, PATHINFO_EXTENSION);
    $uniqueFilename = 'recording_' . $userId . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadsDir . $uniqueFilename;
    
    // Move uploaded file
    if (move_uploaded_file($tempPath, $filePath)) {
        
        // Save to database
        $conn = getConnection();
        $title = 'Recitation Recording - ' . date('M j, Y H:i');
        $description = 'User recording from dashboard';
        
        $query = "INSERT INTO screen_records (user_id, title, description, file_path, file_size, created_at) 
                  VALUES (?, ?, ?, ?, ?, NOW())";
        
        $stmt = $conn->prepare($query);
        $fileSize = filesize($filePath);
        $stmt->bind_param("isssi", $userId, $title, $description, $filePath, $fileSize);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true, 
                'message' => 'Recording saved successfully',
                'file_path' => $filePath,
                'recording_id' => $conn->insert_id
            ]);
        } else {
            // Delete the file if database insert failed
            unlink($filePath);
            echo json_encode(['success' => false, 'message' => 'Failed to save recording to database']);
        }
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save recording file']);
    }
    
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}
?>
