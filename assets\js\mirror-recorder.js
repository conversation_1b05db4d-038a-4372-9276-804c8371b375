/**
 * Mirror Recorder Class
 * Handles mirror camera recording, storage, and community display
 */
class MirrorRecorder {
    constructor() {
        this.mirrorVideo = document.getElementById('mirrorVideo');
        this.toggleButton = document.getElementById('toggleCamera');
        this.flipButton = document.getElementById('flipCamera');
        this.recordButton = document.getElementById('recordMirror');
        this.cameraPlaceholder = document.getElementById('cameraPlaceholder');
        
        this.stream = null;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.isRecording = false;
        this.isCameraActive = false;
        this.currentFacingMode = 'user'; // 'user' for front, 'environment' for back
        
        this.init();
    }

    init() {
        if (this.toggleButton) {
            this.toggleButton.addEventListener('click', () => this.toggleCamera());
        }
        
        if (this.flipButton) {
            this.flipButton.addEventListener('click', () => this.flipCamera());
        }
        
        if (this.recordButton) {
            this.recordButton.addEventListener('click', () => this.toggleRecording());
        }

        // Add recording button to mirror panel if not exists
        this.addRecordingButton();
    }

    addRecordingButton() {
        const mirrorControls = document.querySelector('.camera-controls');
        if (mirrorControls && !document.getElementById('recordMirror')) {
            const recordBtn = document.createElement('button');
            recordBtn.id = 'recordMirror';
            recordBtn.className = 'btn btn-sm btn-danger ms-2';
            recordBtn.style.display = 'none';
            recordBtn.innerHTML = '<i class="fas fa-record-vinyl me-1"></i>Record';
            mirrorControls.appendChild(recordBtn);
            
            recordBtn.addEventListener('click', () => this.toggleRecording());
        }
    }

    async toggleCamera() {
        if (this.isCameraActive) {
            this.stopCamera();
        } else {
            await this.startCamera();
        }
    }

    async startCamera() {
        try {
            const constraints = {
                video: {
                    facingMode: this.currentFacingMode,
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: true
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.mirrorVideo.srcObject = this.stream;
            
            // Show video and hide placeholder
            this.mirrorVideo.style.display = 'block';
            this.cameraPlaceholder.style.display = 'none';
            
            // Update button states
            this.toggleButton.innerHTML = '<i class="fas fa-power-off me-1"></i>Stop Camera';
            this.toggleButton.className = 'btn btn-sm btn-danger';
            this.flipButton.style.display = 'inline-block';
            
            const recordBtn = document.getElementById('recordMirror');
            if (recordBtn) recordBtn.style.display = 'inline-block';
            
            this.isCameraActive = true;
            this.showNotification('Camera started successfully', 'success');
            
        } catch (error) {
            console.error('Error accessing camera:', error);
            this.showNotification('Failed to access camera. Please check permissions.', 'error');
        }
    }

    stopCamera() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
        }
        
        // Hide video and show placeholder
        this.mirrorVideo.style.display = 'none';
        this.cameraPlaceholder.style.display = 'flex';
        
        // Update button states
        this.toggleButton.innerHTML = '<i class="fas fa-power-off me-1"></i>Start Camera';
        this.toggleButton.className = 'btn btn-sm btn-outline-info';
        this.flipButton.style.display = 'none';
        
        const recordBtn = document.getElementById('recordMirror');
        if (recordBtn) recordBtn.style.display = 'none';
        
        this.isCameraActive = false;
        
        // Stop recording if active
        if (this.isRecording) {
            this.stopRecording();
        }
    }

    async flipCamera() {
        if (!this.isCameraActive) return;
        
        this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user';
        
        // Restart camera with new facing mode
        this.stopCamera();
        await this.startCamera();
    }

    async toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    startRecording() {
        if (!this.stream || !this.isCameraActive) {
            this.showNotification('Please start the camera first', 'warning');
            return;
        }

        try {
            const options = {
                mimeType: 'video/webm;codecs=vp9,opus',
                videoBitsPerSecond: 2500000
            };

            // Fallback for browsers that don't support vp9
            if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                options.mimeType = 'video/webm;codecs=vp8,opus';
                if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                    options.mimeType = 'video/webm';
                }
            }

            this.mediaRecorder = new MediaRecorder(this.stream, options);
            this.recordedChunks = [];

            this.mediaRecorder.addEventListener('dataavailable', event => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            });

            this.mediaRecorder.addEventListener('stop', () => {
                this.processMirrorRecording();
            });

            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;

            // Update UI
            const recordBtn = document.getElementById('recordMirror');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-stop me-1"></i>Stop Recording';
                recordBtn.className = 'btn btn-sm btn-warning ms-2';
            }

            this.showNotification('Mirror recording started', 'info');
            this.updateRecordingTimer();

        } catch (error) {
            console.error('Error starting mirror recording:', error);
            this.showNotification('Failed to start recording', 'error');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;

            // Update UI
            const recordBtn = document.getElementById('recordMirror');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-record-vinyl me-1"></i>Record';
                recordBtn.className = 'btn btn-sm btn-danger ms-2';
            }

            clearInterval(this.recordingTimer);
            this.showNotification('Mirror recording stopped', 'info');
        }
    }

    updateRecordingTimer() {
        let seconds = 0;
        this.recordingTimer = setInterval(() => {
            seconds++;
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
            
            // Update recording status
            const statusElement = document.getElementById('recordingStatus');
            if (statusElement) {
                statusElement.textContent = `Recording: ${timeString}`;
            }
        }, 1000);
    }

    async processMirrorRecording() {
        if (this.recordedChunks.length === 0) {
            this.showNotification('No recording data available', 'warning');
            return;
        }

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);

        // Show preview modal
        this.showPreviewModal(url, blob);
    }

    showPreviewModal(videoUrl, videoBlob) {
        const modalHtml = `
            <div class="modal fade" id="mirrorPreviewModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Mirror Recording Preview</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <video controls class="w-100 mb-3" style="max-height: 400px;">
                                <source src="${videoUrl}" type="video/webm">
                            </video>
                            <form id="mirrorUploadForm">
                                <div class="mb-3">
                                    <label for="mirrorTitle" class="form-label">Recording Title</label>
                                    <input type="text" class="form-control" id="mirrorTitle" 
                                           placeholder="Enter a title for your mirror recording" required>
                                </div>
                                <div class="mb-3">
                                    <label for="mirrorType" class="form-label">Recording Type</label>
                                    <select class="form-select" id="mirrorType">
                                        <option value="selfie">Selfie Practice</option>
                                        <option value="posture">Posture Check</option>
                                        <option value="practice">Recitation Practice</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="makePublic" checked>
                                        <label class="form-check-label" for="makePublic">
                                            Share with community
                                        </label>
                                    </div>
                                    <small class="text-muted">Others can view, like, and comment on your recording</small>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Discard</button>
                            <button type="button" class="btn btn-primary" onclick="mirrorRecorder.saveMirrorRecording('${videoUrl}')">
                                <i class="fas fa-save me-1"></i>Save Recording
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('mirrorPreviewModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('mirrorPreviewModal'));
        modal.show();

        // Store blob for later use
        this.currentRecordingBlob = videoBlob;
    }

    async saveMirrorRecording(videoUrl) {
        const title = document.getElementById('mirrorTitle').value.trim();
        const type = document.getElementById('mirrorType').value;
        const isPublic = document.getElementById('makePublic').checked;

        if (!title) {
            this.showNotification('Please enter a title for your recording', 'warning');
            return;
        }

        const loadingBtn = document.querySelector('#mirrorPreviewModal .btn-primary');
        const originalText = loadingBtn.innerHTML;
        loadingBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
        loadingBtn.disabled = true;

        try {
            const formData = new FormData();
            formData.append('mirror_video', this.currentRecordingBlob, `mirror_${Date.now()}.webm`);
            formData.append('title', title);
            formData.append('mirror_type', type);
            formData.append('is_public', isPublic ? '1' : '0');

            const response = await fetch('../api/save-mirror-recording.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Mirror recording saved successfully!', 'success');
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('mirrorPreviewModal'));
                modal.hide();
                
                // Clean up
                URL.revokeObjectURL(videoUrl);
                this.currentRecordingBlob = null;
                
                // Refresh streaming page if we're on it
                if (window.location.pathname.includes('streaming.php')) {
                    setTimeout(() => window.location.reload(), 1000);
                }
            } else {
                throw new Error(result.message || 'Failed to save recording');
            }

        } catch (error) {
            console.error('Error saving mirror recording:', error);
            this.showNotification('Failed to save recording: ' + error.message, 'error');
            
            loadingBtn.innerHTML = originalText;
            loadingBtn.disabled = false;
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Cleanup method
    destroy() {
        this.stopCamera();
        clearInterval(this.recordingTimer);
        
        if (this.currentRecordingBlob) {
            URL.revokeObjectURL(this.currentRecordingBlob);
        }
    }
}

// Initialize mirror recorder when DOM is loaded
let mirrorRecorder;
document.addEventListener('DOMContentLoaded', function() {
    mirrorRecorder = new MirrorRecorder();
});

// Cleanup when page unloads
window.addEventListener('beforeunload', function() {
    if (mirrorRecorder) {
        mirrorRecorder.destroy();
    }
}); 