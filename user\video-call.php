<?php
$page_title = 'Video Call';
require_once __DIR__ . '/../components/user_header.php';

$callee_id = $_GET['callee'] ?? null;
// You would fetch callee info here
?>

<div class="video-call-container">
    <div class="remote-video-wrapper">
        <video id="remoteVideo" autoplay playsinline></video>
        <div class="user-info-overlay">
            <i class="fas fa-user-circle fa-2x"></i>
            <span id="calleeName">Connecting...</span>
        </div>
    </div>
    <div class="local-video-wrapper">
        <video id="localVideo" autoplay muted playsinline></video>
    </div>
    <div class="call-controls">
        <button id="toggle-mic" class="control-btn"><i class="fas fa-microphone"></i></button>
        <button id="toggle-cam" class="control-btn"><i class="fas fa-video"></i></button>
        <button id="end-call" class="control-btn end-call"><i class="fas fa-phone-slash"></i></button>
    </div>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>
<script>
// Placeholder for WebRTC logic
document.addEventListener('DOMContentLoaded', async () => {
    const localVideo = document.getElementById('localVideo');
    const remoteVideo = document.getElementById('remoteVideo');
    let localStream;

    try {
        localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
        localVideo.srcObject = localStream;
    } catch (e) {
        alert('You must allow camera and microphone access to make video calls.');
        console.error('Error accessing media devices.', e);
    }
    
    // Simulate connecting to a remote peer
    setTimeout(() => {
        document.getElementById('calleeName').innerText = 'Connected to Peer';
        // In a real app, the remote stream would be attached here
    }, 3000);

    // Control button logic
    const micBtn = document.getElementById('toggle-mic');
    micBtn.addEventListener('click', () => {
        localStream.getAudioTracks().forEach(track => track.enabled = !track.enabled);
        micBtn.classList.toggle('active');
    });

    const camBtn = document.getElementById('toggle-cam');
    camBtn.addEventListener('click', () => {
        localStream.getVideoTracks().forEach(track => track.enabled = !track.enabled);
        camBtn.classList.toggle('active');
    });

    const endBtn = document.getElementById('end-call');
    endBtn.addEventListener('click', () => {
        localStream.getTracks().forEach(track => track.stop());
        window.close(); // or redirect
    });
});
</script>
<style>
body {
    background: #111;
    overflow: hidden;
}
main {
    padding: 0 !important;
    max-width: 100vw !important;
}
.video-call-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    background: #000;
}
.remote-video-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
#remoteVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.local-video-wrapper {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 25vw;
    max-width: 300px;
    height: auto;
    border: 3px solid var(--primary);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}
#localVideo {
    width: 100%;
    display: block;
}
.call-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    background: rgba(0,0,0,0.5);
    padding: 1rem 2rem;
    border-radius: 50px;
}
.control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: rgba(255,255,255,0.2);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.2s;
}
.control-btn:hover {
    background: rgba(255,255,255,0.3);
}
.control-btn.active {
    background: var(--primary);
}
.control-btn.end-call {
    background: var(--danger);
}
.user-info-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0,0,0,0.5);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
</style>
