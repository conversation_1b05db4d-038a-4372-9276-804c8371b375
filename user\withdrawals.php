<?php
require_once '../config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle withdrawal request
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['request_withdrawal'])) {
    $bankName = sanitize($_POST['bank_name']);
    $accountNumber = sanitize($_POST['account_number']);
    $accountHolderName = sanitize($_POST['account_holder_name']);
    $pointsToWithdraw = intval($_POST['points_to_withdraw']);
    
    if (empty($bankName) || empty($accountNumber) || empty($accountHolderName) || $pointsToWithdraw < 50) {
        $error = 'All fields are required and minimum withdrawal is 50 points.';
    } else {
        try {
            $conn = getConnection();
            $conn->autocommit(false);
            
            // Get user's current points balance
            $stmt = $conn->prepare("SELECT points_balance FROM users WHERE id = ?");
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            $stmt->close();
            
            if (!$user) {
                throw new Exception('User not found.');
            }
            
            if ($user['points_balance'] < $pointsToWithdraw) {
                throw new Exception('Insufficient points balance.');
            }
            
            // Calculate withdrawal amount (₦100 per point)
            $withdrawalAmount = $pointsToWithdraw * 100;
            
            // Deduct points from user account
            $newPointsBalance = $user['points_balance'] - $pointsToWithdraw;
            $stmt = $conn->prepare("UPDATE users SET points_balance = ? WHERE id = ?");
            $stmt->bind_param("ii", $newPointsBalance, $userId);
            $stmt->execute();
            $stmt->close();
            
            // Create withdrawal request
            $stmt = $conn->prepare("
                INSERT INTO withdrawal_requests (user_id, bank_name, account_number, account_holder_name, amount, points_converted) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("isssdi", $userId, $bankName, $accountNumber, $accountHolderName, $withdrawalAmount, $pointsToWithdraw);
            $stmt->execute();
            $stmt->close();
            
            // Record transaction
            $stmt = $conn->prepare("
                INSERT INTO transactions (user_id, transaction_type, amount, points, description, status) 
                VALUES (?, 'withdrawal', ?, ?, 'Points withdrawal request', 'pending')
            ");
            $stmt->bind_param("idis", $userId, $withdrawalAmount, $pointsToWithdraw);
            $stmt->execute();
            $stmt->close();
            
            $conn->commit();
            $conn->close();
            
            $message = "Withdrawal request submitted successfully! Points will be processed within 24-48 hours.";
            
        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->rollback();
                $conn->close();
            }
            $error = $e->getMessage();
            logError("Withdrawal request error: " . $e->getMessage());
        }
    }
}

// Get user information
$user = getUserById($userId);

// Get withdrawal history
try {
    $conn = getConnection();
    $stmt = $conn->prepare("
        SELECT * FROM withdrawal_requests 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $withdrawalHistory = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    $conn->close();
} catch (Exception $e) {
    $withdrawalHistory = [];
    logError("Withdrawal history error: " . $e->getMessage());
}

// Calculate available withdrawal amount
$maxWithdrawablePoints = $user['points_balance'];
$maxWithdrawableAmount = $maxWithdrawablePoints * 100;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawals - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/main.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-book-quran me-2"></i>Qur'an Recite App
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="streaming.php">
                    <i class="fas fa-video me-1"></i>Streaming
                </a>
                <a class="nav-link" href="wallet.php">
                    <i class="fas fa-wallet me-1"></i>Wallet
                </a>
                <a class="nav-link active" href="withdrawals.php">
                    <i class="fas fa-money-bill-wave me-1"></i>Withdrawals
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h2><i class="fas fa-money-bill-wave me-2"></i>Withdraw Funds</h2>
                
                <!-- Alert Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Withdrawal Form -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-university me-2"></i>Bank Account Details</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="withdrawalForm">
                            <div class="mb-3">
                                <label for="bank_name" class="form-label">Bank Name *</label>
                                <select class="form-select" id="bank_name" name="bank_name" required>
                                    <option value="">Select Bank</option>
                                    <option value="Access Bank">Access Bank</option>
                                    <option value="Zenith Bank">Zenith Bank</option>
                                    <option value="GTBank">GTBank</option>
                                    <option value="First Bank">First Bank</option>
                                    <option value="UBA">UBA</option>
                                    <option value="Fidelity Bank">Fidelity Bank</option>
                                    <option value="Union Bank">Union Bank</option>
                                    <option value="Stanbic IBTC">Stanbic IBTC</option>
                                    <option value="Sterling Bank">Sterling Bank</option>
                                    <option value="Wema Bank">Wema Bank</option>
                                    <option value="Polaris Bank">Polaris Bank</option>
                                    <option value="Kuda Bank">Kuda Bank</option>
                                    <option value="Opay">Opay</option>
                                    <option value="Palmpay">Palmpay</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_number" class="form-label">Account Number *</label>
                                <input type="text" class="form-control" id="account_number" name="account_number" 
                                       pattern="[0-9]{10}" maxlength="10" required>
                                <div class="form-text">Enter your 10-digit account number</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_holder_name" class="form-label">Account Holder Name *</label>
                                <input type="text" class="form-control" id="account_holder_name" name="account_holder_name" 
                                       value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                <div class="form-text">This should match your bank account name</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="points_to_withdraw" class="form-label">Points to Withdraw *</label>
                                <input type="number" class="form-control" id="points_to_withdraw" name="points_to_withdraw" 
                                       min="50" max="<?php echo $maxWithdrawablePoints; ?>" required>
                                <div class="form-text">
                                    Minimum: 50 points | Available: <?php echo number_format($maxWithdrawablePoints); ?> points | 
                                    Rate: ₦100 per point
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Withdrawal Amount:</strong> ₦<span id="withdrawalAmount">0</span>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" name="request_withdrawal" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Request Withdrawal
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Current Balance -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-coins me-2"></i>Current Balance</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="balance-display">
                            <div class="points-balance">
                                <h3 class="text-primary"><?php echo number_format($user['points_balance']); ?></h3>
                                <p class="mb-0">Points Available</p>
                            </div>
                            <hr>
                            <div class="cash-equivalent">
                                <h4 class="text-success">₦<?php echo number_format($maxWithdrawableAmount); ?></h4>
                                <p class="mb-0">Cash Value</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal Info -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>Withdrawal Info</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Minimum withdrawal: 50 points</li>
                            <li><i class="fas fa-check text-success me-2"></i>Rate: ₦100 per point</li>
                            <li><i class="fas fa-check text-success me-2"></i>Processing time: 24-48 hours</li>
                            <li><i class="fas fa-check text-success me-2"></i>No withdrawal fees</li>
                            <li><i class="fas fa-check text-success me-2"></i>Direct bank transfer</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Withdrawal History -->
        <div class="row mt-5">
            <div class="col-12">
                <h3><i class="fas fa-history me-2"></i>Withdrawal History</h3>
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($withdrawalHistory)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted"></i>
                                <p class="mt-3 text-muted">No withdrawal requests yet</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Bank Details</th>
                                            <th>Points</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($withdrawalHistory as $withdrawal): ?>
                                            <tr>
                                                <td><?php echo date('M j, Y', strtotime($withdrawal['created_at'])); ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($withdrawal['bank_name']); ?></strong><br>
                                                    <small><?php echo htmlspecialchars($withdrawal['account_number']); ?></small>
                                                </td>
                                                <td><?php echo number_format($withdrawal['points_converted']); ?></td>
                                                <td>₦<?php echo number_format($withdrawal['amount'], 2); ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($withdrawal['status']) {
                                                        case 'pending':
                                                            $statusClass = 'bg-warning';
                                                            break;
                                                        case 'processing':
                                                            $statusClass = 'bg-info';
                                                            break;
                                                        case 'completed':
                                                            $statusClass = 'bg-success';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($withdrawal['status']); ?>
                                                    </span>
                                                    <?php if ($withdrawal['admin_notes']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($withdrawal['admin_notes']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const pointsInput = document.getElementById('points_to_withdraw');
            const withdrawalAmountDisplay = document.getElementById('withdrawalAmount');
            
            function updateWithdrawalAmount() {
                const points = parseInt(pointsInput.value) || 0;
                const amount = points * 100;
                withdrawalAmountDisplay.textContent = amount.toLocaleString();
            }
            
            pointsInput.addEventListener('input', updateWithdrawalAmount);
            
            // Account number validation
            const accountNumberInput = document.getElementById('account_number');
            accountNumberInput.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 10);
            });
        });
    </script>
</body>
</html> 