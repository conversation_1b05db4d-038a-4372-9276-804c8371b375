<?php
require_once '../config.php';

// Check if admin is already logged in
if (isAdmin()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    if (validateCSRFToken($_POST['csrf_token'])) {
        $username = sanitize($_POST['username']);
        $password = $_POST['password'];
        
        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
            $conn = getConnection();
            
            // For initial setup, check if password matches the master password
            if ($username === 'admin' && $password === '1@3Usazladan') {
                // Create or update admin account
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $conn->prepare("
                    INSERT INTO admins (username, password) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE password = ?
                ");
                $stmt->bind_param("sss", $username, $hashedPassword, $hashedPassword);
                $stmt->execute();
                $adminId = $conn->insert_id ?: 1; // Use 1 if updating existing admin
                $stmt->close();
                
                // Set admin session
                $_SESSION['admin_id'] = $adminId;
                $_SESSION['admin_username'] = $username;
                
                $conn->close();
                header('Location: dashboard.php');
                exit;
            } else {
                // Check against database
                $stmt = $conn->prepare("SELECT id, username, password FROM admins WHERE username = ?");
                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($admin = $result->fetch_assoc()) {
                    if (password_verify($password, $admin['password'])) {
                        // Login successful
                        $_SESSION['admin_id'] = $admin['id'];
                        $_SESSION['admin_username'] = $admin['username'];
                        
                        $stmt->close();
                        $conn->close();
                        
                        header('Location: dashboard.php');
                        exit;
                    } else {
                        $error = 'Invalid username or password.';
                    }
                } else {
                    $error = 'Invalid username or password.';
                }
                
                $stmt->close();
                $conn->close();
            }
        }
    } else {
        $error = 'Invalid security token. Please try again.';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/main.css" rel="stylesheet">
</head>
<body class="bg-dark">
    <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-lg-4 col-md-6">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-danger text-white text-center py-4">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Admin Portal
                        </h2>
                        <p class="mb-0">Qur'an Recite App Administration</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="Username" required value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                                <label for="username">
                                    <i class="fas fa-user me-2"></i>Username
                                </label>
                                <div class="invalid-feedback">Please enter your username.</div>
                            </div>
                            
                            <div class="form-floating mb-4">
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Password" required>
                                <label for="password">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <div class="invalid-feedback">Please enter your password.</div>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" name="login" class="btn btn-danger btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Login to Admin Panel
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <a href="../index.php" class="text-muted text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to Main Site
                            </a>
                        </div>
                        
                        <!-- Security Notice -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Security Notice
                            </h6>
                            <ul class="small text-muted mb-0">
                                <li>Admin access is restricted and monitored</li>
                                <li>Unauthorized access attempts are logged</li>
                                <li>Default credentials: admin / 1@3Usazladan</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Auto-focus username field
        document.getElementById('username').focus();
    </script>
</body>
</html> 