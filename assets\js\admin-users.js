/**
 * Admin Users Management JavaScript
 */

// User management functions
function editUser(userId) {
    // Fetch user data and populate modal
    fetch(`../api/get_user.php?id=${userId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Check if response is actually JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response is not JSON');
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            const user = data.user;
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editFullName').value = user.full_name || '';
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editWalletBalance').value = user.wallet_balance || 0;
            document.getElementById('editPointsBalance').value = user.points_balance || 0;
            document.getElementById('editIsBlocked').checked = user.is_blocked == 1;

            // Show modal using Bootstrap 5
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        } else {
            alert('Failed to load user data: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error loading user data:', error);
        alert('Error loading user data. Please try again.');
    });
}

function toggleBlock(userId) {
    if (confirm('Are you sure you want to change this user\'s block status?')) {
        document.getElementById('toggleUserId').value = userId;
        document.getElementById('toggleBlockForm').submit();
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone and will remove all user data including transactions, recordings, and posts.')) {
        document.getElementById('deleteUserId').value = userId;
        document.getElementById('deleteUserForm').submit();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin Users Management initialized');
    
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});
