<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Layout Test - ReciteApp</title>
    <style>
        /* Test styles to simulate the layout */
        :root {
            --primary-red: #B10020;
            --primary-red-light: #D32F2F;
            --primary-red-dark: #8B0000;
            --white: #FFFFFF;
            --dark-text: #1E1E1E;
            --subtle-gray: #F3F3F3;
            --light-gray: #E8E8E8;
            --sidebar-width: 280px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--white);
            color: var(--dark-text);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }
        
        .user-sidebar {
            width: var(--sidebar-width);
            background: var(--white);
            border-right: 1px solid var(--light-gray);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 999;
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: 2px 0 20px rgba(177, 0, 32, 0.1);
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            background: var(--white);
            transition: margin-left 0.3s ease;
            position: relative;
            width: calc(100% - var(--sidebar-width));
            overflow-x: hidden;
        }
        
        .main-header {
            background: var(--white);
            border-bottom: 1px solid var(--light-gray);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: relative;
            z-index: 10;
        }
        
        .content-wrapper {
            padding: 0;
            min-height: calc(100vh - 80px);
            background: var(--white);
        }
        
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem 2rem 2rem 2rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        .test-content {
            background: var(--subtle-gray);
            padding: 2rem;
            border-radius: 15px;
            margin: 1rem 0;
            text-align: center;
        }
        
        .mobile-menu-button {
            display: none;
            background: var(--primary-red);
            color: white;
            border: none;
            font-size: 1.25rem;
            padding: 0.75rem;
            border-radius: 12px;
            cursor: pointer;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .user-sidebar {
                transform: translateX(-100%);
                width: 280px;
                z-index: 1001;
            }
            
            .user-sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
                width: 100% !important;
                position: relative;
            }
            
            .mobile-menu-button {
                display: block;
            }
            
            .main-header {
                padding: 1rem 1.5rem;
                position: relative;
                z-index: 10;
            }
            
            .content-wrapper {
                padding: 0;
                min-height: calc(100vh - 70px);
            }
            
            .dashboard-container {
                padding: 1rem;
                margin: 0;
            }
        }
        
        @media (max-width: 480px) {
            .user-sidebar {
                width: 100vw;
                z-index: 1001;
            }
            
            .main-header {
                padding: 0.75rem 1rem;
            }
            
            .content-wrapper {
                padding: 0;
                min-height: calc(100vh - 65px);
            }
            
            .dashboard-container {
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="user-sidebar" id="sidebar">
        <div style="padding: 2rem; background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light)); color: white; text-align: center;">
            <h3>ReciteApp</h3>
        </div>
        <div style="padding: 1rem;">
            <div style="padding: 1rem; margin: 0.5rem 0; background: var(--subtle-gray); border-radius: 10px;">Dashboard</div>
            <div style="padding: 1rem; margin: 0.5rem 0; background: var(--light-gray); border-radius: 10px;">Streams</div>
            <div style="padding: 1rem; margin: 0.5rem 0; background: var(--light-gray); border-radius: 10px;">Rankings</div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="main-header">
            <div style="display: flex; align-items: center; gap: 1rem;">
                <button class="mobile-menu-button" onclick="toggleSidebar()">☰</button>
                <h1>Dashboard</h1>
            </div>
            <div>User Menu</div>
        </div>
        
        <!-- Content -->
        <div class="content-wrapper">
            <div class="dashboard-container">
                <div class="test-content">
                    <h2>Main Dashboard Content</h2>
                    <p>This content should not hide behind the sidebar on any screen size.</p>
                    <p>Resize your browser window to test responsiveness.</p>
                </div>
                
                <div class="test-content">
                    <h3>Test Section 1</h3>
                    <p>This is a test section to verify layout.</p>
                </div>
                
                <div class="test-content">
                    <h3>Test Section 2</h3>
                    <p>Another test section for layout verification.</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }
        
        // Show current screen size for testing
        function showScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            console.log(`Screen size: ${width}x${height}`);
            
            if (width <= 480) {
                console.log('Mobile (Small)');
            } else if (width <= 768) {
                console.log('Mobile');
            } else if (width <= 1024) {
                console.log('Tablet');
            } else {
                console.log('Desktop');
            }
        }
        
        window.addEventListener('resize', showScreenSize);
        showScreenSize();
    </script>
</body>
</html>
