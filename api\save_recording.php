<?php
/**
 * Save Recording API for Recite! App
 * Handles screen recording uploads and stream creation
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];

// Validate CSRF token
if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$contentId = intval($_POST['content_id'] ?? 0);
$score = intval($_POST['score'] ?? 0);

if (!$contentId) {
    echo json_encode(['success' => false, 'message' => 'Invalid content ID']);
    exit;
}

// Check if recording file was uploaded
if (!isset($_FILES['recording']) || $_FILES['recording']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'No recording file uploaded']);
    exit;
}

$uploadFile = $_FILES['recording'];
$uploadDir = '../uploads/screen_records/';

// Create directory if it doesn't exist
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

// Generate unique filename
$fileName = 'recording_' . $userId . '_' . time() . '_' . uniqid() . '.webm';
$filePath = $uploadDir . $fileName;

try {
    // Move uploaded file
    if (!move_uploaded_file($uploadFile['tmp_name'], $filePath)) {
        throw new Exception('Failed to save recording file');
    }
    
    $conn = getConnection();
    $conn->autocommit(false);
    
    try {
        // Get content details
        $contentResult = executeQuery(
            "SELECT surah_name FROM content WHERE id = ?",
            'i',
            [$contentId]
        );
        
        $content = $contentResult->fetch_assoc();
        
        // Save recitation record
        executeQuery(
            "INSERT INTO recitations (user_id, content_id, final_score, screen_recording_path, is_completed) VALUES (?, ?, ?, ?, 1)",
            'iiis',
            [$userId, $contentId, $score, 'screen_records/' . $fileName]
        );
        
        $recitationId = $conn->insert_id;
        
        // Create stream post for the recording
        $description = "Recitation of " . $content['surah_name'] . " (Score: " . $score . "%)";
        
        executeQuery(
            "INSERT INTO streams (user_id, content_type, text_content, media_path) VALUES (?, 'recitation', ?, ?)",
            'iss',
            [$userId, $description, 'screen_records/' . $fileName]
        );
        
        // Award points if score is good (70% or above)
        if ($score >= 70) {
            executeQuery(
                "UPDATE users SET points_balance = points_balance + 1 WHERE id = ?",
                'i',
                [$userId]
            );
            
            // Log points transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'bonus', 1, 'Recitation completion bonus', 'completed')",
                'i',
                [$userId]
            );
        }
        
        // Update rankings
        updateUserRanking($userId);
        
        $conn->commit();
        $conn->autocommit(true);
        
        echo json_encode([
            'success' => true,
            'message' => 'Recording saved successfully',
            'recitation_id' => $recitationId,
            'points_earned' => $score >= 70 ? 1 : 0
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        
        // Delete uploaded file if database operation failed
        if (file_exists($filePath)) {
            unlink($filePath);
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Save recording error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to save recording']);
}

/**
 * Update user ranking based on recitation performance
 */
function updateUserRanking($userId) {
    try {
        $conn = getConnection();
        
        // Get user location info
        $userResult = executeQuery(
            "SELECT ward, lga, state, country FROM users WHERE id = ?",
            'i',
            [$userId]
        );
        
        $user = $userResult->fetch_assoc();
        
        // Calculate total score and recitation count
        $statsResult = executeQuery(
            "SELECT COUNT(*) as total_recitations, AVG(final_score) as avg_score, SUM(final_score) as total_score 
             FROM recitations WHERE user_id = ? AND is_completed = 1",
            'i',
            [$userId]
        );
        
        $stats = $statsResult->fetch_assoc();
        $totalScore = intval($stats['total_score'] ?? 0);
        $totalRecitations = intval($stats['total_recitations'] ?? 0);
        
        // Calculate rankings
        $wardRank = calculateRank($conn, $totalScore, $user['ward'], 'ward');
        $lgaRank = calculateRank($conn, $totalScore, $user['lga'], 'lga');
        $stateRank = calculateRank($conn, $totalScore, $user['state'], 'state');
        $countryRank = calculateRank($conn, $totalScore, $user['country'], 'country');
        
        // Update or insert ranking record
        executeQuery(
            "INSERT INTO rankings (user_id, ward_rank, lga_rank, state_rank, country_rank, total_score, total_recitations, last_recitation_date) 
             VALUES (?, ?, ?, ?, ?, ?, ?, CURDATE())
             ON DUPLICATE KEY UPDATE 
             ward_rank = VALUES(ward_rank),
             lga_rank = VALUES(lga_rank), 
             state_rank = VALUES(state_rank),
             country_rank = VALUES(country_rank),
             total_score = VALUES(total_score),
             total_recitations = VALUES(total_recitations),
             last_recitation_date = VALUES(last_recitation_date)",
            'iiiiiii',
            [$userId, $wardRank, $lgaRank, $stateRank, $countryRank, $totalScore, $totalRecitations]
        );
        
    } catch (Exception $e) {
        error_log("Ranking update error: " . $e->getMessage());
    }
}

/**
 * Calculate user rank within a location group
 */
function calculateRank($conn, $userScore, $location, $locationField) {
    $result = executeQuery(
        "SELECT COUNT(*) + 1 as rank 
         FROM rankings r 
         JOIN users u ON r.user_id = u.id 
         WHERE u.{$locationField} = ? AND r.total_score > ? AND u.is_active = 1",
        'si',
        [$location, $userScore]
    );
    
    $rank = $result->fetch_assoc();
    return intval($rank['rank']);
}
?> 