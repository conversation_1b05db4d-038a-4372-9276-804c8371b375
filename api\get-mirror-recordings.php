<?php
require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

try {
    $conn = getConnection();
    
    // Get parameters
    $search = sanitize($_GET['search'] ?? '');
    $filter = sanitize($_GET['filter'] ?? 'recent');
    $mirrorType = sanitize($_GET['mirror_type'] ?? 'all');
    $page = max(1, intval($_GET['page'] ?? 1));
    $perPage = intval($_GET['per_page'] ?? 12);
    $offset = ($page - 1) * $perPage;
    
    // Build where clause
    $whereClause = "mr.is_public = 1";
    $params = [];
    $types = "";
    
    if (!empty($search)) {
        $whereClause .= " AND (mr.title LIKE ? OR u.full_name LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $types .= "ss";
    }
    
    if ($mirrorType !== 'all') {
        $whereClause .= " AND mr.mirror_type = ?";
        $params[] = $mirrorType;
        $types .= "s";
    }
    
    // Build order clause
    switch ($filter) {
        case 'most_viewed':
            $orderClause = "mr.views_count DESC";
            break;
        case 'most_liked':
            $orderClause = "mr.likes_count DESC";
            break;
        case 'highest_ranked':
            $orderClause = "r.total_score DESC";
            break;
        default:
            $orderClause = "mr.created_at DESC";
    }
    
    // Get mirror recordings
    $query = "
        SELECT mr.*, u.full_name, u.profile_picture, u.state, u.ward,
               COALESCE(r.total_score, 0) as user_score
        FROM mirror_recordings mr 
        JOIN users u ON mr.user_id = u.id 
        LEFT JOIN rankings r ON u.id = r.user_id
        WHERE $whereClause 
        ORDER BY $orderClause 
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $conn->prepare($query);
    
    // Add limit and offset parameters
    $params[] = $perPage;
    $params[] = $offset;
    $types .= "ii";
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $recordings = [];
    while ($row = $result->fetch_assoc()) {
        // Check if current user has liked this recording
        $likeStmt = $conn->prepare("SELECT id FROM mirror_likes WHERE mirror_recording_id = ? AND user_id = ?");
        $likeStmt->bind_param("ii", $row['id'], $_SESSION['user_id']);
        $likeStmt->execute();
        $hasLiked = $likeStmt->get_result()->num_rows > 0;
        $likeStmt->close();
        
        $row['has_liked'] = $hasLiked;
        $row['time_ago'] = timeAgo($row['created_at']);
        $row['formatted_file_size'] = formatFileSize($row['file_size']);
        
        $recordings[] = $row;
    }
    
    // Get total count for pagination
    $countQuery = "
        SELECT COUNT(*) as total 
        FROM mirror_recordings mr 
        JOIN users u ON mr.user_id = u.id 
        WHERE $whereClause
    ";
    
    $countStmt = $conn->prepare($countQuery);
    if (!empty($search) || $mirrorType !== 'all') {
        $countParams = array_slice($params, 0, -2); // Remove limit and offset
        $countTypes = substr($types, 0, -2);
        if (!empty($countParams)) {
            $countStmt->bind_param($countTypes, ...$countParams);
        }
    }
    
    $countStmt->execute();
    $totalRecords = $countStmt->get_result()->fetch_assoc()['total'];
    $totalPages = ceil($totalRecords / $perPage);
    
    $stmt->close();
    $countStmt->close();
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'recordings' => $recordings,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_records' => $totalRecords,
            'per_page' => $perPage
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Get mirror recordings error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch recordings: ' . $e->getMessage()
    ]);
}

// Helper function to format file size
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?> 