 Product Requirements Document (PRD)
Product Name: Qur’an Recite App
Version: 1.0
Prepared by: Universal Reciters Foundation
Date: June 2025
📌 1. Executive Summary
Qur’an Recite App is a multilingual, AI-powered Islamic education platform that helps users improve their Qur’anic recitation through speech-to-text correction, voice analysis, and screen-recorded feedback. The app rewards accuracy with points, rankings, and cash conversion, and features a vibrant user ecosystem with referral bonuses, social engagement, and live video/chat functionality.

🎯 2. Goals and Objectives
Goal	Objective
Promote accurate Qur’an recitation	Real-time voice correction using in-browser or AI speech-to-text
Encourage daily practice	Gamify with streaks, ranking, and reward system
Enable community learning	Public streaming, sharing, and feedback system
Make Islamic education rewarding	Monetize effort via points, referral, and ranking-based earnings
Enable local & global competition	Location-based leaderboard and user analytics
Provide social interaction	Video calls, comments, likes, and chats

👥 3. Target Users
Young Muslims learning Qur’an

Madrasas and schools

Parents & scholars

Digital da’wah workers

Sponsors and NGOs

Multilingual users (Hausa, Arabic, English, Hindi, Chinese)

🌍 4. Supported Languages
English

Hausa

Arabic

Hindi

Chinese

🧱 5. Key Features
1. User Registration & Login
Registration with: name, email, password, location

Upload profile picture

Pay ₦1000 via Paystack to complete registration

Login/logout system

Session management

2. 4-Part Interactive Dashboard
Part	Description
1	Embedded YouTube video selected by admin
2	Selfie mirror using webcam (not saved)
3	Speech-to-text Qur’an verse matching with AI or Web Speech API
4	Ranking by Ward, LGEA, State, Country

3. Speech Feedback and Marking System
Highlights:

Correct words in blue

Errors in red

Up to 3 attempts per error:

1st = −1 mark

2nd = −2 marks

3rd = −3 marks

Once completed, score is saved and points added

4. Screen Recording
Starts automatically after video ends

Records user reciting from speech panel

Saved to Streaming Home Page

Includes title, profile, location, date

5. Streaming Home Page
Public list of all user recordings

Functions:

Watch (₦3)

Like (₦3)

Comment (₦3)

Share (₦3)

Delete (if creator)

Report content

₦1 goes to video owner, ₦2 to app admin

6. Wallet System
View:

Money balance (₦)

Point balance

Buy points (₦70/point via Paystack)

Sell points (₦50/point, min 50)

Withdraw (₦100/point after 30 days)

Auto convert matured points to money

7. Bank Account Verification
User enters bank name & account number

Auto-fills account holder name

Manual/automatic payout to user wallet

8. Referral System
Unique referral link per user

Earn 1 point per successful invite

Referral dashboard: number of invites, history

9. Admin Panel
Login with secure password (1@3Usazladan)

Upload new YouTube videos

Ban users

Remove content

View reports

View app earnings

10. User Posting (Image/Video)
Upload new post: costs ₦1000 from wallet

Displayed on streaming page

Can delete (password required)

Deletion removes all earnings from that post

11. Streak System
100 recitations in 72 hours = 100-point bonus

Notifications for streak reminders

12. Live Social Features
Chat

One-to-one video call

Group voice rooms

Online status by location

13. Search and Filters
Search by username, video title, location

Filter streaming page by:

Most viewed

Highest ranked

Recent

14. Content Reporting & Moderation
Any user can report content

Admins review and decide to:

Ban content

Deduct earnings

Suspend user

15. About Us & Contact Us Pages
Info about the Universal Reciters Foundation

Founders and scholars

WhatsApp group link

Feedback form

🧑‍💻 6. Functional Requirements
Module	Requirement
Auth	User must register with payment before access
Video	Admin must input video links via secure portal
Speech	Use Web Speech API or AI for real-time word match
Wallet	Handle payments, deductions, referrals, withdrawal
Ranking	Auto-update rank after every recitation
Streaming	Save and show screen recordings, handle engagement
Admin	Admins moderate, upload, and manage users
Chat	Users can message, call, and video-call others
Post	Users can upload extra content (fee applies)

🧮 7. Data Models (Entities)
users

videos

recitations

screen_records

wallets

transactions

points

comments

likes

shares

referrals

withdrawal_requests

posts

reports

admins

chat_logs

💳 8. Payment Gateway
Paystack used for:

Registration (₦1000)

Buying points (₦70 per point)

Integration must support:

Test mode (for dev)

Production mode (for live use)

Webhook listener for payment confirmation

📐 9. UI/UX Design Principles
Fully responsive

No scroll dashboard (mobile adapts to stack layout)

Use Bootstrap or Tailwind CSS

User-friendly icons and highlights

Arabic-right-to-left support for text display

🔒 10. Security Requirements
CSRF protection

Input validation & sanitization

Secure file uploads

Session management

Admin panel protection