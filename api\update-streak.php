<?php
require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        throw new Exception('Invalid CSRF token');
    }
    
    $conn = getConnection();
    $userId = $_SESSION['user_id'];
    $recitationId = intval($_POST['recitation_id'] ?? 0);
    
    if ($recitationId <= 0) {
        throw new Exception('Invalid recitation ID');
    }
    
    // Get current time and 72 hours ago
    $currentTime = date('Y-m-d H:i:s');
    $timeLimit = date('Y-m-d H:i:s', strtotime('-72 hours'));
    
    // Count completed recitations in the last 72 hours
    $stmt = $conn->prepare("
        SELECT COUNT(*) as recent_recitations 
        FROM recitations 
        WHERE user_id = ? AND is_completed = 1 AND created_at >= ?
    ");
    $stmt->bind_param("is", $userId, $timeLimit);
    $stmt->execute();
    $result = $stmt->get_result();
    $recentRecitations = $result->fetch_assoc()['recent_recitations'];
    $stmt->close();
    
    // Update or create streak record
    $stmt = $conn->prepare("
        INSERT INTO streaks (user_id, current_streak, best_streak, last_recitation_date) 
        VALUES (?, 1, 1, ?) 
        ON DUPLICATE KEY UPDATE 
            current_streak = ?,
            best_streak = GREATEST(best_streak, ?),
            last_recitation_date = ?
    ");
    $stmt->bind_param("issis", $userId, $currentTime, $recentRecitations, $recentRecitations, $currentTime);
    $stmt->execute();
    $stmt->close();
    
    $bonusAwarded = false;
    $bonusPoints = 0;
    
    // Check for 100 recitations in 72 hours milestone
    if ($recentRecitations >= 100) {
        // Check if user has already received this bonus in the last 72 hours
        $stmt = $conn->prepare("
            SELECT COUNT(*) as bonus_count 
            FROM transactions 
            WHERE user_id = ? AND description LIKE '%100 recitations in 72 hours%' AND created_at >= ?
        ");
        $stmt->bind_param("is", $userId, $timeLimit);
        $stmt->execute();
        $result = $stmt->get_result();
        $bonusCount = $result->fetch_assoc()['bonus_count'];
        $stmt->close();
        
        // Award bonus if not already given
        if ($bonusCount == 0) {
            $bonusPoints = 100;
            
            // Update user points
            $stmt = $conn->prepare("UPDATE users SET points_balance = points_balance + ? WHERE id = ?");
            $stmt->bind_param("ii", $bonusPoints, $userId);
            $stmt->execute();
            $stmt->close();
            
            // Log transaction
            $stmt = $conn->prepare("
                INSERT INTO transactions 
                (user_id, transaction_type, amount, points, description, status) 
                VALUES (?, 'earning', 0.00, ?, '100 recitations in 72 hours streak bonus', 'completed')
            ");
            $stmt->bind_param("ii", $userId, $bonusPoints);
            $stmt->execute();
            $stmt->close();
            
            $bonusAwarded = true;
        }
    }
    
    // Get updated user stats
    $stmt = $conn->prepare("
        SELECT s.current_streak, s.best_streak, u.points_balance, u.total_recitations
        FROM streaks s
        JOIN users u ON s.user_id = u.id
        WHERE s.user_id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $userStats = $result->fetch_assoc();
    $stmt->close();
    
    $conn->close();
    
    $response = [
        'success' => true,
        'message' => 'Streak updated successfully',
        'current_streak' => $userStats['current_streak'],
        'best_streak' => $userStats['best_streak'],
        'recent_recitations' => $recentRecitations,
        'points_balance' => $userStats['points_balance'],
        'total_recitations' => $userStats['total_recitations']
    ];
    
    if ($bonusAwarded) {
        $response['bonus_awarded'] = true;
        $response['bonus_points'] = $bonusPoints;
        $response['bonus_message'] = "Congratulations! You've completed 100 recitations in 72 hours and earned 100 bonus points!";
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Update streak error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to update streak: ' . $e->getMessage()
    ]);
}
?> 