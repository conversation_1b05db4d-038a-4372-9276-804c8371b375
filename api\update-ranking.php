<?php
require_once '../config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'User not authenticated']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
$userId = intval($input['user_id'] ?? $_SESSION['user_id']);
$score = floatval($input['score'] ?? 0);
$accuracy = floatval($input['accuracy'] ?? 0);

try {
    $conn = getConnection();
    $conn->autocommit(false); // Start transaction
    
    // Get user location information
    $stmt = $conn->prepare("SELECT country, state, ward, lgea, total_recitations FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Update or insert ranking record
    $stmt = $conn->prepare("
        INSERT INTO rankings (user_id, total_score) 
        VALUES (?, ?) 
        ON DUPLICATE KEY UPDATE 
        total_score = total_score + VALUES(total_score),
        last_updated = CURRENT_TIMESTAMP
    ");
    $stmt->bind_param("id", $userId, $score);
    $stmt->execute();
    $stmt->close();
    
    // Calculate ward ranking
    $wardRank = calculateRank($userId, 'ward', $user['ward'], $conn);
    
    // Calculate LGEA ranking  
    $lgeaRank = calculateRank($userId, 'lgea', $user['lgea'], $conn);
    
    // Calculate state ranking
    $stateRank = calculateRank($userId, 'state', $user['state'], $conn);
    
    // Calculate country ranking
    $countryRank = calculateRank($userId, 'country', $user['country'], $conn);
    
    // Update ranking positions
    $stmt = $conn->prepare("
        UPDATE rankings 
        SET ward_rank = ?, lgea_rank = ?, state_rank = ?, country_rank = ?, last_updated = CURRENT_TIMESTAMP
        WHERE user_id = ?
    ");
    $stmt->bind_param("iiiii", $wardRank, $lgeaRank, $stateRank, $countryRank, $userId);
    $stmt->execute();
    $stmt->close();
    
    // Award points for completed recitation
    $pointsToAward = 0;
    if ($accuracy >= 100) {
        $pointsToAward = 1; // Perfect recitation = 1 point
    }
    
    if ($pointsToAward > 0) {
        updateUserPoints($userId, $pointsToAward, 'Recitation completed with ' . $accuracy . '% accuracy');
    }
    
    // Check for streak bonus (100 recitations in 72 hours)
    $bonusAwarded = checkAndAwardStreakBonus($userId, $conn);
    
    // Get updated ranking information
    $stmt = $conn->prepare("
        SELECT r.ward_rank, r.lgea_rank, r.state_rank, r.country_rank, r.total_score,
               u.points_balance, u.total_recitations
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE r.user_id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $rankingData = $result->fetch_assoc();
    $stmt->close();
    
    $conn->commit(); // Commit transaction
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'rankings' => [
            'ward' => $wardRank,
            'lgea' => $lgeaRank, 
            'state' => $stateRank,
            'country' => $countryRank
        ],
        'total_score' => $rankingData['total_score'],
        'points_earned' => $pointsToAward,
        'streak_bonus' => $bonusAwarded,
        'total_points' => $rankingData['points_balance'],
        'total_recitations' => $rankingData['total_recitations'],
        'message' => 'Rankings updated successfully!'
    ]);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollback();
        $conn->close();
    }
    
    logError("Update ranking error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Function to calculate rank based on location and category
function calculateRank($userId, $category, $location, $conn) {
    $whereClause = "";
    $params = [];
    $types = "";
    
    switch ($category) {
        case 'ward':
            $whereClause = "WHERE u.ward = ?";
            $params[] = $location;
            $types = "s";
            break;
        case 'lgea':
            $whereClause = "WHERE u.lgea = ?";
            $params[] = $location;
            $types = "s";
            break;
        case 'state':
            $whereClause = "WHERE u.state = ?";
            $params[] = $location;
            $types = "s";
            break;
        case 'country':
            $whereClause = "WHERE u.country = ?";
            $params[] = $location;
            $types = "s";
            break;
        default:
            return 0;
    }
    
    $sql = "
        SELECT COUNT(*) + 1 as rank 
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        $whereClause 
        AND r.total_score > (
            SELECT total_score 
            FROM rankings 
            WHERE user_id = ?
        )
    ";
    
    $params[] = $userId;
    $types .= "i";
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    return $row['rank'] ?? 0;
}

// Function to check and award streak bonus
function checkAndAwardStreakBonus($userId, $conn) {
    // Check if user has 100 recitations in the last 72 hours
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM recitations 
        WHERE user_id = ? 
        AND is_completed = 1 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    $stmt->close();
    
    if ($count >= 100) {
        // Check if bonus already awarded for this period
        $stmt = $conn->prepare("
            SELECT COUNT(*) as bonus_count 
            FROM streaks 
            WHERE user_id = ? 
            AND bonus_awarded = 1 
            AND start_date >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
        ");
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $bonusCount = $result->fetch_assoc()['bonus_count'];
        $stmt->close();
        
        if ($bonusCount == 0) {
            // Award 100 point bonus
            updateUserPoints($userId, 100, '100 recitations in 72 hours streak bonus');
            
            // Record the streak
            $stmt = $conn->prepare("
                INSERT INTO streaks (user_id, recitations_count, bonus_awarded, bonus_points) 
                VALUES (?, 100, 1, 100)
            ");
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $stmt->close();
            
            return true;
        }
    }
    
    return false;
}
?> 