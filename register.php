<?php
// Configuration needs to be loaded first
require_once 'config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

/**
 * User Registration Page for Recite! App
 * Handles user registration and Paystack payment integration
 */

require_once 'config/database.php';

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Sanitize and validate input
        $fullName = sanitize($_POST['full_name'] ?? '');
        $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
        $password = $_POST['password'] ?? '';
        $ward = sanitize($_POST['ward'] ?? '');
        $lga = sanitize($_POST['lga'] ?? '');
        $state = sanitize($_POST['state'] ?? '');
        $referralCode = sanitize($_POST['referral_code'] ?? '');
        
        // Validation
        if (empty($fullName) || empty($email) || empty($password) || empty($ward) || empty($lga) || empty($state)) {
            $error = 'All fields are required.';
        } elseif (strlen($password) < 6) {
            $error = 'Password must be at least 6 characters long.';
        } else {
            try {
                // Check if email already exists
                $result = executeQuery(
                    "SELECT id FROM users WHERE email = ?",
                    's',
                    [$email]
                );
                
                if ($result->num_rows > 0) {
                    $error = 'Email already registered. Please use a different email.';
                } else {
                    // Hash password
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert user (inactive until payment)
                    $insertResult = executeQuery(
                        "INSERT INTO users (full_name, email, password_hash, ward, lga, state, is_active, payment_verified) VALUES (?, ?, ?, ?, ?, ?, 0, 0)",
                        'ssssss',
                        [$fullName, $email, $passwordHash, $ward, $lga, $state]
                    );
                    
                    if ($insertResult) {
                        $conn = getConnection();
                        $userId = $conn->insert_id;
                        
                        // Generate referral code
                        $userReferralCode = generateReferralCode($userId);
                        executeQuery(
                            "UPDATE users SET referral_code = ? WHERE id = ?",
                            'si',
                            [$userReferralCode, $userId]
                        );
                        
                        // Handle referral if provided
                        if (!empty($referralCode)) {
                            $referrerResult = executeQuery(
                                "SELECT id FROM users WHERE referral_code = ? AND is_active = 1",
                                's',
                                [$referralCode]
                            );
                            
                            if ($referrerResult->num_rows > 0) {
                                $referrer = $referrerResult->fetch_assoc();
                                executeQuery(
                                    "UPDATE users SET referred_by = ? WHERE id = ?",
                                    'ii',
                                    [$referrer['id'], $userId]
                                );
                            }
                        }
                        
                        // Store user ID in session for payment verification
                        $_SESSION['pending_user_id'] = $userId;
                        $_SESSION['user_email'] = $email;
                        
                        $success = 'Registration successful! Please complete the payment to activate your account.';
                    }
                }
            } catch (Exception $e) {
                error_log("Registration error: " . $e->getMessage());
                $error = 'Registration failed. Please try again.';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <?php echo APP_NAME; ?></title>

    <!-- Fresh Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">
    <script src="https://js.paystack.co/v1/inline.js"></script>

    <style>
        /* Auth Page Specific Styles */
        body {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-md);
            margin: 0;
        }

        .auth-container {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            padding: var(--space-2xl);
        }

        .auth-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }

        .auth-logo {
            font-size: var(--font-size-3xl);
            color: var(--primary);
            margin-bottom: var(--space-lg);
        }

        .auth-title {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            margin-bottom: var(--space-md);
            color: var(--gray-900);
        }

        .auth-subtitle {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            margin-bottom: var(--space-xl);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-lg);
        }

        @media (max-width: 640px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }


    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-user-plus"></i>
            </div>
            <h1 class="auth-title">Join RECITE</h1>
            <p class="auth-subtitle">
                Create your account and start your recitation journey
            </p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo $success; ?>
            </div>

            <!-- Payment Section -->
            <div class="text-center mt-8 p-6 bg-gray-50 rounded-lg">
                <div class="text-3xl font-bold text-primary mb-4">₦1,000</div>
                <p class="text-gray-600 mb-6">
                    Complete your registration by making a one-time payment to activate your account.
                </p>
                <button onclick="payWithPaystack()" class="btn btn-primary btn-lg w-full">
                    <i class="fas fa-credit-card"></i>
                    Pay Now to Activate Account
                </button>
                <p class="text-sm text-gray-500 mt-4">
                    <i class="fas fa-shield-alt"></i>
                    Secure payment powered by Paystack
                </p>
            </div>
        <?php else: ?>
                            
        <form method="POST" action="" id="registerForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

            <div class="form-group">
                <label for="full_name" class="form-label">
                    <i class="fas fa-user"></i> Full Name
                </label>
                <input type="text" class="form-input" id="full_name" name="full_name"
                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                       placeholder="Enter your full name" required>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input type="email" class="form-input" id="email" name="email"
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                       placeholder="Enter your email address" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i> Password
                </label>
                <input type="password" class="form-input" id="password" name="password"
                       placeholder="Create a secure password (min. 6 characters)" minlength="6" required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="ward" class="form-label">
                        <i class="fas fa-map-marker-alt"></i> Ward
                    </label>
                    <input type="text" class="form-input" id="ward" name="ward"
                           value="<?php echo htmlspecialchars($_POST['ward'] ?? ''); ?>"
                           placeholder="Your ward" required>
                </div>

                <div class="form-group">
                    <label for="lga" class="form-label">
                        <i class="fas fa-map"></i> LGA
                    </label>
                    <input type="text" class="form-input" id="lga" name="lga"
                           value="<?php echo htmlspecialchars($_POST['lga'] ?? ''); ?>"
                           placeholder="Your LGA" required>
                </div>
            </div>

            <div class="form-group">
                <label for="state" class="form-label">
                    <i class="fas fa-flag"></i> State
                </label>
                <input type="text" class="form-input" id="state" name="state"
                       value="<?php echo htmlspecialchars($_POST['state'] ?? ''); ?>"
                       placeholder="Your state" required>
            </div>

            <div class="form-group">
                <label for="referral_code" class="form-label">
                    <i class="fas fa-gift"></i> Referral Code (Optional)
                </label>
                <input type="text" class="form-input" id="referral_code" name="referral_code"
                       value="<?php echo htmlspecialchars($_GET['ref'] ?? ''); ?>"
                       placeholder="Enter referral code if you have one">
            </div>

            <button type="submit" class="btn btn-primary btn-lg w-full">
                <i class="fas fa-user-plus"></i>
                Create My Account
            </button>
        </form>
        <?php endif; ?>

        <div class="text-center mt-8">
            <p class="text-gray-600">
                Already have an account?
                <a href="login.php" class="text-primary font-medium">Sign in here</a>
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Form enhancement
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner loading"></i> Creating Account...';
            submitBtn.disabled = true;

            // Re-enable button if form submission fails
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 10000);
        });

        // Auto-focus first input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('full_name').focus();
        });
        
        // Paystack payment function
        function payWithPaystack() {
            <?php if (isset($_SESSION['pending_user_id'])): ?>
            var handler = PaystackPop.setup({
                key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
                email: '<?php echo $_SESSION['user_email']; ?>',
                amount: 100000, // ₦1000 in kobo
                currency: 'NGN',
                ref: 'REG_<?php echo $_SESSION['pending_user_id']; ?>_' + Math.floor((Math.random() * 1000000000) + 1),
                metadata: {
                    user_id: <?php echo $_SESSION['pending_user_id']; ?>,
                    purpose: 'registration'
                },
                callback: function(response) {
                    // Payment successful, verify on server
                    window.location.href = 'verify_payment.php?reference=' + response.reference;
                },
                onClose: function() {
                    alert('Payment was not completed. Please try again.');
                }
            });
            
            handler.openIframe();
            <?php endif; ?>
        }
    </script>
</body>
</html>