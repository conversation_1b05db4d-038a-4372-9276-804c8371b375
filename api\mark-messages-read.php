<?php
require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        throw new Exception('Invalid CSRF token');
    }
    
    $conn = getConnection();
    
    $senderId = intval($_POST['user_id'] ?? 0);
    $recipientId = $_SESSION['user_id'];
    
    if ($senderId <= 0) {
        throw new Exception('Invalid sender ID');
    }
    
    // Mark all unread messages from sender as read
    $stmt = $conn->prepare("
        UPDATE chat_logs 
        SET is_read = 1 
        WHERE sender_id = ? AND recipient_id = ? AND is_read = 0
    ");
    $stmt->bind_param("ii", $senderId, $recipientId);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to mark messages as read');
    }
    
    $affectedRows = $stmt->affected_rows;
    
    $stmt->close();
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'Messages marked as read',
        'marked_count' => $affectedRows
    ]);
    
} catch (Exception $e) {
    error_log("Mark messages read error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 