<?php
/**
 * Database Setup Script for RECITE App
 * Creates essential tables if they don't exist
 */

require_once 'config/database.php';

try {
    $conn = getConnection();
    
    echo "<h2>🚀 Setting up RECITE App Database</h2>";
    
    // Create videos table
    $createVideosTable = "
        CREATE TABLE IF NOT EXISTS videos (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(200) NOT NULL,
            youtube_url VARCHAR(500) NOT NULL,
            youtube_id VARCHAR(50),
            category VARCHAR(100),
            reciter VARCHAR(100),
            description TEXT,
            transcript TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_active (is_active)
        )
    ";
    
    if ($conn->query($createVideosTable)) {
        echo "✅ Videos table created/verified successfully<br>";
    }
    
    // Create screen_records table
    $createScreenRecordsTable = "
        CREATE TABLE IF NOT EXISTS screen_records (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            recitation_id INT,
            title VARCHAR(200),
            file_path VARCHAR(500) NOT NULL,
            file_size BIGINT,
            duration INT,
            views_count INT DEFAULT 0,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            earnings DECIMAL(10,2) DEFAULT 0.00,
            is_public BOOLEAN DEFAULT TRUE,
            is_reported BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_public (is_public),
            INDEX idx_created_at (created_at)
        )
    ";
    
    if ($conn->query($createScreenRecordsTable)) {
        echo "✅ Screen records table created/verified successfully<br>";
    }
    
    // Create recitations table
    $createRecitationsTable = "
        CREATE TABLE IF NOT EXISTS recitations (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            video_id INT,
            content_id INT,
            transcript TEXT,
            correct_words INT DEFAULT 0,
            total_words INT DEFAULT 0,
            accuracy_percentage DECIMAL(5,2) DEFAULT 0.00,
            marks_deducted INT DEFAULT 0,
            attempts_used INT DEFAULT 0,
            points_earned INT DEFAULT 0,
            final_score INT DEFAULT 0,
            is_completed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_completed (is_completed)
        )
    ";
    
    if ($conn->query($createRecitationsTable)) {
        echo "✅ Recitations table created/verified successfully<br>";
    }

    // Create admins table
    $createAdminsTable = "
        CREATE TABLE IF NOT EXISTS admins (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100),
            full_name VARCHAR(100),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_active (is_active)
        )
    ";

    if ($conn->query($createAdminsTable)) {
        echo "✅ Admins table created/verified successfully<br>";
    }

    // Create default admin if not exists
    $adminExists = $conn->query("SELECT COUNT(*) as count FROM admins WHERE username = 'admin'")->fetch_assoc()['count'];

    if ($adminExists == 0) {
        $adminPassword = password_hash('1@3Usazladan', PASSWORD_DEFAULT);
        $insertAdmin = $conn->prepare("INSERT INTO admins (username, password, email, full_name) VALUES (?, ?, ?, ?)");
        $username = 'admin';
        $email = '<EMAIL>';
        $fullName = 'System Administrator';
        $insertAdmin->bind_param("ssss", $username, $adminPassword, $email, $fullName);

        if ($insertAdmin->execute()) {
            echo "✅ Default admin created (username: admin, password: 1@3Usazladan)<br>";
        }
    } else {
        echo "✅ Admin account already exists<br>";
    }

    // Create content table (Qur'an Surahs)
    $createContentTable = "
        CREATE TABLE IF NOT EXISTS content (
            id INT PRIMARY KEY AUTO_INCREMENT,
            surah_number INT NOT NULL,
            surah_name VARCHAR(100) NOT NULL,
            youtube_id VARCHAR(50) NOT NULL,
            arabic_text LONGTEXT NOT NULL,
            unlock_price DECIMAL(10,2) DEFAULT 30.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_surah_number (surah_number),
            INDEX idx_active (is_active)
        )
    ";

    if ($conn->query($createContentTable)) {
        echo "✅ Content table created/verified successfully<br>";
    }

    // Create unlocked_content table
    $createUnlockedContentTable = "
        CREATE TABLE IF NOT EXISTS unlocked_content (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            content_id INT NOT NULL,
            unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_content_id (content_id),
            UNIQUE KEY unique_user_content (user_id, content_id)
        )
    ";

    if ($conn->query($createUnlockedContentTable)) {
        echo "✅ Unlocked content table created/verified successfully<br>";
    }

    // Create transactions table
    $createTransactionsTable = "
        CREATE TABLE IF NOT EXISTS transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            transaction_type ENUM('deposit', 'withdrawal', 'content_unlock', 'point_purchase', 'point_sale', 'registration_fee') NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            points_involved INT DEFAULT 0,
            description TEXT,
            reference VARCHAR(100),
            status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_type (transaction_type),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        )
    ";

    if ($conn->query($createTransactionsTable)) {
        echo "✅ Transactions table created/verified successfully<br>";
    }

    // Create withdrawal_requests table
    $createWithdrawalRequestsTable = "
        CREATE TABLE IF NOT EXISTS withdrawal_requests (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            points_converted INT NOT NULL,
            bank_name VARCHAR(100),
            account_number VARCHAR(20),
            account_holder_name VARCHAR(100),
            status ENUM('pending', 'processing', 'completed', 'rejected') DEFAULT 'pending',
            admin_notes TEXT,
            processed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        )
    ";

    if ($conn->query($createWithdrawalRequestsTable)) {
        echo "✅ Withdrawal requests table created/verified successfully<br>";
    }

    // Create streams table
    $createStreamsTable = "
        CREATE TABLE IF NOT EXISTS streams (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            content_type ENUM('text', 'image', 'video') DEFAULT 'text',
            text_content TEXT,
            media_path VARCHAR(500),
            views_count INT DEFAULT 0,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            earnings DECIMAL(10,2) DEFAULT 0.00,
            is_public BOOLEAN DEFAULT TRUE,
            is_reported BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_content_type (content_type),
            INDEX idx_public (is_public),
            INDEX idx_created_at (created_at)
        )
    ";

    if ($conn->query($createStreamsTable)) {
        echo "✅ Streams table created/verified successfully<br>";
    }

    // Create views table
    $createViewsTable = "
        CREATE TABLE IF NOT EXISTS views (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            content_type ENUM('screen_record', 'mirror_recording', 'stream', 'video') NOT NULL,
            content_id INT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_content (content_type, content_id),
            INDEX idx_created_at (created_at)
        )
    ";

    if ($conn->query($createViewsTable)) {
        echo "✅ Views table created/verified successfully<br>";
    }

    // Create likes table
    $createLikesTable = "
        CREATE TABLE IF NOT EXISTS likes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            screen_record_id INT,
            mirror_recording_id INT,
            stream_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_screen_record (screen_record_id),
            INDEX idx_mirror_recording (mirror_recording_id),
            INDEX idx_stream (stream_id),
            UNIQUE KEY unique_user_screen (user_id, screen_record_id),
            UNIQUE KEY unique_user_mirror (user_id, mirror_recording_id),
            UNIQUE KEY unique_user_stream (user_id, stream_id)
        )
    ";

    if ($conn->query($createLikesTable)) {
        echo "✅ Likes table created/verified successfully<br>";
    }

    // Create comments table
    $createCommentsTable = "
        CREATE TABLE IF NOT EXISTS comments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            screen_record_id INT,
            mirror_recording_id INT,
            stream_id INT,
            comment_text TEXT NOT NULL,
            is_reported BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_screen_record (screen_record_id),
            INDEX idx_mirror_recording (mirror_recording_id),
            INDEX idx_stream (stream_id),
            INDEX idx_created_at (created_at)
        )
    ";

    if ($conn->query($createCommentsTable)) {
        echo "✅ Comments table created/verified successfully<br>";
    }

    // Create mirror_recordings table
    $createMirrorRecordingsTable = "
        CREATE TABLE IF NOT EXISTS mirror_recordings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            title VARCHAR(200),
            file_path VARCHAR(500) NOT NULL,
            file_size BIGINT,
            duration INT,
            views_count INT DEFAULT 0,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            earnings DECIMAL(10,2) DEFAULT 0.00,
            is_public BOOLEAN DEFAULT TRUE,
            is_reported BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_public (is_public),
            INDEX idx_created_at (created_at)
        )
    ";

    if ($conn->query($createMirrorRecordingsTable)) {
        echo "✅ Mirror recordings table created/verified successfully<br>";
    }

    // Insert sample videos if table is empty
    $videoCount = $conn->query("SELECT COUNT(*) as count FROM videos")->fetch_assoc()['count'];
    
    if ($videoCount == 0) {
        $sampleVideos = [
            ['Surah Al-Fatiha - Beautiful Recitation', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Abdul Rahman', 'Beautiful recitation of the opening chapter of the Quran'],
            ['Surah Al-Ikhlas - Perfect Pronunciation', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Muhammad', 'Learn perfect pronunciation of Surah Al-Ikhlas'],
            ['Surah Al-Falaq - Protection Prayer', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Ahmad', 'Seeking protection through Quranic recitation'],
            ['Surah An-Nas - Final Chapter', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Ibrahim', 'The final chapter of the Holy Quran'],
            ['Surah Al-Kahf - Friday Special', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Long Surahs', 'Sheikh Yusuf', 'Special Friday recitation of Surah Al-Kahf'],
            ['Surah Yasin - Heart of Quran', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Medium Surahs', 'Sheikh Hassan', 'The heart of the Quran - Surah Yasin']
        ];
        
        $insertVideo = $conn->prepare("INSERT INTO videos (title, youtube_url, youtube_id, category, reciter, description, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)");
        
        foreach ($sampleVideos as $video) {
            $insertVideo->bind_param("ssssss", $video[0], $video[1], $video[2], $video[3], $video[4], $video[5]);
            $insertVideo->execute();
        }
        
        echo "✅ Sample videos inserted successfully<br>";
    }

    // Insert sample Qur'an content if table is empty
    $contentCount = $conn->query("SELECT COUNT(*) as count FROM content")->fetch_assoc()['count'];

    if ($contentCount == 0) {
        $sampleContent = [
            [1, 'Al-Fatiha', 'dQw4w9WgXcQ', 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ الرَّحْمَٰنِ الرَّحِيمِ مَالِكِ يَوْمِ الدِّينِ إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ', 30.00],
            [112, 'Al-Ikhlas', 'dQw4w9WgXcQ', 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ قُلْ هُوَ اللَّهُ أَحَدٌ اللَّهُ الصَّمَدُ لَمْ يَلِدْ وَلَمْ يُولَدْ وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ', 25.00],
            [113, 'Al-Falaq', 'dQw4w9WgXcQ', 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ مِن شَرِّ مَا خَلَقَ وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ', 25.00],
            [114, 'An-Nas', 'dQw4w9WgXcQ', 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ قُلْ أَعُوذُ بِرَبِّ النَّاسِ مَلِكِ النَّاسِ إِلَٰهِ النَّاسِ مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ مِنَ الْجِنَّةِ وَالنَّاسِ', 25.00]
        ];

        $insertContent = $conn->prepare("INSERT INTO content (surah_number, surah_name, youtube_id, arabic_text, unlock_price) VALUES (?, ?, ?, ?, ?)");

        foreach ($sampleContent as $content) {
            $insertContent->bind_param("isssd", $content[0], $content[1], $content[2], $content[3], $content[4]);
            $insertContent->execute();
        }

        echo "✅ Sample Qur'an content inserted successfully<br>";
    }

    echo "<br><h3>🎉 Database setup completed successfully!</h3>";
    echo "<p><a href='index.php'>← Back to Home</a> | <a href='admin/login.php'>Admin Login</a></p>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>";
    echo "<h3>❌ Database Setup Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    error_log("Database setup error: " . $e->getMessage());
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #1a5f3f;
}

p {
    line-height: 1.6;
}

a {
    color: #1a5f3f;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
