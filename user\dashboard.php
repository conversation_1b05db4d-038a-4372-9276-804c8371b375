<?php
$page_title = 'User Dashboard';
require_once __DIR__ . '/../components/user_header.php';

// Fetch the latest active video for the user to recite
$conn = getConnection();
$video_stmt = $conn->prepare("SELECT * FROM videos WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1");
$video_stmt->execute();
$video = $video_stmt->get_result()->fetch_assoc();
$video_stmt->close();

// Fetch user ranking info (placeholders for now)
$user_rank = [
    'ward_rank' => 1,
    'lgea_rank' => 5,
    'state_rank' => 25,
    'country_rank' => 103,
];

$conn->close();
?>

<div class="interactive-dashboard" data-transcript="<?php echo htmlspecialchars($video['transcript'] ?? ''); ?>">
    <!-- Part 1: Video Player -->
    <div class="dashboard-card video-card">
        <div class="card-header">
            <h4><i class="fas fa-video me-2"></i> <?php echo htmlspecialchars($video['title'] ?? 'No Video Available'); ?></h4>
            <span class="text-muted">by <?php echo htmlspecialchars($video['reciter'] ?? 'N/A'); ?></span>
        </div>
        <div class="card-body">
            <?php if ($video && !empty($video['youtube_id'])): ?>
                <div class="video-container">
                    <iframe src="https://www.youtube.com/embed/<?php echo htmlspecialchars($video['youtube_id']); ?>" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
            <?php else: ?>
                <div class="text-center p-5">
                    <p>No recitation video has been uploaded by the admin yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Part 2: Mirror Camera -->
    <div class="dashboard-card mirror-card">
        <div class="card-header">
            <h4><i class="fas fa-camera-retro me-2"></i> Reciter's Mirror</h4>
        </div>
        <div class="card-body">
            <div id="mirror-container">
                <video id="mirrorVideo" autoplay muted playsinline></video>
                <div id="cameraPlaceholder" class="placeholder-content">
                    <i class="fas fa-video fa-3x mb-3"></i>
                    <p>Camera is off</p>
                </div>
            </div>
            <button id="startCameraBtn" class="btn btn-primary w-100 mt-3"><i class="fas fa-video me-2"></i>Start Camera</button>
        </div>
    </div>

    <!-- Part 3: Recitation Panel -->
    <div class="dashboard-card recitation-card">
        <div class="card-header">
            <h4><i class="fas fa-microphone-alt me-2"></i> Recite & Get Feedback</h4>
        </div>
        <div class="card-body">
            <div id="transcript-container">
                <p class="transcript-placeholder">The video transcript will appear here. Press "Start Reciting" when you are ready.</p>
            </div>
            <div class="recitation-controls">
                 <button id="startRecitationBtn" class="btn btn-success btn-lg" disabled><i class="fas fa-play-circle me-2"></i>Start Reciting</button>
            </div>
            <div id="recitation-status" class="mt-3 text-center" style="display: none;">
                <span class="badge bg-primary">Listening...</span>
            </div>
        </div>
    </div>

    <!-- Part 4: Ranking System -->
    <div class="dashboard-card ranking-card">
        <div class="card-header">
            <h4><i class="fas fa-trophy me-2"></i> Your Current Rank</h4>
        </div>
        <div class="card-body">
            <ul class="ranking-list">
                <li><span>Ward</span><span class="rank-value">#<?php echo $user_rank['ward_rank']; ?></span></li>
                <li><span>LGEA</span><span class="rank-value">#<?php echo $user_rank['lgea_rank']; ?></span></li>
                <li><span>State</span><span class="rank-value">#<?php echo $user_rank['state_rank']; ?></span></li>
                <li><span>Country</span><span class="rank-value">#<?php echo $user_rank['country_rank']; ?></span></li>
            </ul>
            <a href="rankings.php" class="btn btn-outline-primary w-100 mt-3">View Full Leaderboard</a>
        </div>
    </div>
</div>


<?php require_once __DIR__ . '/../components/user_footer.php'; ?>

<script>
// Mirror camera functionality
const mirrorVideo = document.getElementById('mirrorVideo');
const startCameraBtn = document.getElementById('startCameraBtn');
const cameraPlaceholder = document.getElementById('cameraPlaceholder');
let stream = null;

startCameraBtn.addEventListener('click', async () => {
    try {
        if (!stream) {
            stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'user' },
                audio: false
            });
            
            mirrorVideo.srcObject = stream;
            mirrorVideo.style.display = 'block';
            cameraPlaceholder.style.display = 'none';
            startCameraBtn.innerHTML = '<i class="fas fa-stop me-2"></i>Stop Camera';
            startCameraBtn.classList.remove('btn-primary');
            startCameraBtn.classList.add('btn-danger');
        } else {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
            mirrorVideo.style.display = 'none';
            cameraPlaceholder.style.display = 'block';
            startCameraBtn.innerHTML = '<i class="fas fa-video me-2"></i>Start Camera';
            startCameraBtn.classList.remove('btn-danger');
            startCameraBtn.classList.add('btn-primary');
        }
    } catch (error) {
        console.error('Error accessing camera:', error);
        alert('Unable to access camera. Please check permissions.');
    }
});

// Auto-update time-based elements
function updateTimeElements() {
    // Update any time-based content if needed
}

setInterval(updateTimeElements, 60000); // Update every minute

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script> 