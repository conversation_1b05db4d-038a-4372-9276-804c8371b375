-- Qur'an Recite App Database Schema
-- Created: 2024
-- Version: 1.0

-- Create database
CREATE DATABASE IF NOT EXISTS quran_recite_app;
USE quran_recite_app;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    profile_picture VARCHAR(255),
    country VARCHAR(50),
    state VARCHAR(50),
    ward VARCHAR(50),
    lgea VARCHAR(50),
    registration_paid BOOLEAN DEFAULT FALSE,
    payment_reference VARCHAR(100),
    referral_code VARCHAR(20) UNIQUE,
    referred_by INT,
    wallet_balance DECIMAL(10,2) DEFAULT 0.00,
    points_balance INT DEFAULT 0,
    total_recitations INT DEFAULT 0,
    streak_count INT DEFAULT 0,
    last_recitation_date DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    is_banned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_referral_code (referral_code),
    INDEX idx_location (state, ward, lgea)
);

-- Admin table
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Videos table (YouTube videos added by admin)
CREATE TABLE videos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    youtube_url VARCHAR(500) NOT NULL,
    youtube_id VARCHAR(50),
    category VARCHAR(100),
    reciter VARCHAR(100),
    description TEXT,
    transcript TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    FOREIGN KEY (created_by) REFERENCES admins(id)
);

-- Recitations table (user recitation attempts)
CREATE TABLE recitations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    video_id INT NOT NULL,
    transcript TEXT,
    correct_words INT DEFAULT 0,
    total_words INT DEFAULT 0,
    accuracy_percentage DECIMAL(5,2) DEFAULT 0.00,
    marks_deducted INT DEFAULT 0,
    attempts_used INT DEFAULT 0,
    points_earned INT DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_completed (is_completed),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
);

-- Screen recordings table
CREATE TABLE screen_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    recitation_id INT,
    title VARCHAR(200),
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    duration INT,
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0.00,
    is_public BOOLEAN DEFAULT TRUE,
    is_reported BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_public (is_public),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recitation_id) REFERENCES recitations(id) ON DELETE SET NULL
);

-- Wallet transactions table
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    transaction_type ENUM('deposit', 'withdrawal', 'earning', 'deduction', 'point_purchase', 'point_sale') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    points INT DEFAULT 0,
    description VARCHAR(255),
    reference VARCHAR(100),
    payment_method VARCHAR(50),
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (transaction_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Rankings table
CREATE TABLE rankings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    ward_rank INT,
    lgea_rank INT,
    state_rank INT,
    country_rank INT,
    total_score DECIMAL(10,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_ward_rank (ward_rank),
    INDEX idx_state_rank (state_rank),
    INDEX idx_country_rank (country_rank),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Comments table
CREATE TABLE comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    screen_record_id INT NOT NULL,
    user_id INT NOT NULL,
    comment_text TEXT NOT NULL,
    is_reported BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_screen_record_id (screen_record_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (screen_record_id) REFERENCES screen_records(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Likes table
CREATE TABLE likes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    screen_record_id INT NOT NULL,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_like (screen_record_id, user_id),
    INDEX idx_screen_record_id (screen_record_id),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (screen_record_id) REFERENCES screen_records(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Shares table
CREATE TABLE shares (
    id INT PRIMARY KEY AUTO_INCREMENT,
    screen_record_id INT NOT NULL,
    user_id INT NOT NULL,
    share_platform VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_screen_record_id (screen_record_id),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (screen_record_id) REFERENCES screen_records(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Referrals table
CREATE TABLE referrals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    points_earned INT DEFAULT 1,
    status ENUM('pending', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referred_id (referred_id),
    INDEX idx_status (status),
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Withdrawal requests table
CREATE TABLE withdrawal_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(20) NOT NULL,
    account_holder_name VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    points_converted INT NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'rejected') DEFAULT 'pending',
    admin_notes TEXT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Posts table (user uploaded content)
CREATE TABLE posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200),
    content_type ENUM('image', 'video') NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    description TEXT,
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0.00,
    upload_fee_paid DECIMAL(10,2) DEFAULT 1000.00,
    is_active BOOLEAN DEFAULT TRUE,
    is_reported BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_content_type (content_type),
    INDEX idx_active (is_active),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Reports table
CREATE TABLE reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reporter_id INT NOT NULL,
    reported_content_type ENUM('screen_record', 'post', 'comment', 'user') NOT NULL,
    reported_content_id INT NOT NULL,
    reason VARCHAR(255) NOT NULL,
    description TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    admin_action TEXT,
    resolved_by INT,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_reporter_id (reporter_id),
    INDEX idx_content_type (reported_content_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Views table (track who viewed what)
CREATE TABLE views (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_type ENUM('screen_record', 'post') NOT NULL,
    content_id INT NOT NULL,
    view_fee DECIMAL(10,2) DEFAULT 3.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_content_type_id (content_type, content_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Chat logs table
CREATE TABLE chat_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT NOT NULL,
    receiver_id INT,
    room_id VARCHAR(50),
    message_type ENUM('text', 'voice', 'video', 'file') DEFAULT 'text',
    message_content TEXT NOT NULL,
    file_path VARCHAR(500),
    is_group_message BOOLEAN DEFAULT FALSE,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_room_id (room_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Streaks table
CREATE TABLE streaks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    streak_type ENUM('daily', 'challenge') DEFAULT 'daily',
    start_date DATE NOT NULL,
    end_date DATE,
    recitations_count INT DEFAULT 0,
    target_count INT DEFAULT 100,
    bonus_awarded BOOLEAN DEFAULT FALSE,
    bonus_points INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_streak_type (streak_type),
    INDEX idx_active (is_active),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Settings table
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Mirror recordings table (for storing mirror camera recordings)
CREATE TABLE mirror_recordings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    recitation_id INT,
    title VARCHAR(200) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    thumbnail_path VARCHAR(500),
    file_size BIGINT,
    duration INT,
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0.00,
    is_public BOOLEAN DEFAULT TRUE,
    is_reported BOOLEAN DEFAULT FALSE,
    mirror_type ENUM('selfie', 'posture', 'practice') DEFAULT 'selfie',
    recording_quality VARCHAR(20) DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_public (is_public),
    INDEX idx_created_at (created_at),
    INDEX idx_mirror_type (mirror_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recitation_id) REFERENCES recitations(id) ON DELETE SET NULL
);

-- Mirror recording comments table
CREATE TABLE mirror_comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    mirror_recording_id INT NOT NULL,
    user_id INT NOT NULL,
    comment_text TEXT NOT NULL,
    is_reported BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_mirror_recording_id (mirror_recording_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (mirror_recording_id) REFERENCES mirror_recordings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Mirror recording likes table
CREATE TABLE mirror_likes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    mirror_recording_id INT NOT NULL,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_mirror_like (mirror_recording_id, user_id),
    INDEX idx_mirror_recording_id (mirror_recording_id),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (mirror_recording_id) REFERENCES mirror_recordings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Mirror recording shares table
CREATE TABLE mirror_shares (
    id INT PRIMARY KEY AUTO_INCREMENT,
    mirror_recording_id INT NOT NULL,
    user_id INT NOT NULL,
    share_platform VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_mirror_recording_id (mirror_recording_id),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (mirror_recording_id) REFERENCES mirror_recordings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Bank accounts table for withdrawal verification
CREATE TABLE bank_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    bank_code VARCHAR(10) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(20) NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_bank (user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_verified (is_verified),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Contact messages table for contact form submissions
CREATE TABLE contact_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('general', 'technical', 'feedback', 'partnership', 'scholarship', 'complaint') DEFAULT 'general',
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('new', 'read', 'responded', 'resolved') DEFAULT 'new',
    admin_response TEXT,
    responded_by INT,
    responded_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_message_type (message_type),
    INDEX idx_created_at (created_at),
    INDEX idx_email (email),
    FOREIGN KEY (responded_by) REFERENCES admins(id) ON DELETE SET NULL
);

-- Insert default admin
INSERT INTO admins (username, password) VALUES 
('admin', '$2y$10$YourHashedPasswordHere'); -- Password: 1@3Usazladan

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, description) VALUES
('paystack_public_key', 'pk_test_79ab46bc7ad4029705e3593e00dd2feb7c7cdb77', 'Paystack public key'),
('paystack_secret_key', 'sk_test_your_secret_key_here', 'Paystack secret key'),
('registration_fee', '1000', 'Registration fee in Naira'),
('point_buy_rate', '70', 'Naira per point when buying'),
('point_sell_rate', '50', 'Naira per point when selling'),
('point_withdraw_rate', '100', 'Naira per point when withdrawing'),
('view_fee', '3', 'Fee for viewing content'),
('like_fee', '3', 'Fee for liking content'),
('comment_fee', '3', 'Fee for commenting'),
('share_fee', '3', 'Fee for sharing'),
('upload_fee', '1000', 'Fee for uploading new posts'),
('creator_share', '1', 'Creator share from engagement fees'),
('app_share', '2', 'App share from engagement fees'),
('withdrawal_maturity_days', '30', 'Days before points can be withdrawn'),
('min_withdrawal_points', '50', 'Minimum points for withdrawal');

-- User Stats table
CREATE TABLE user_stats (
    user_id INT PRIMARY KEY,
    recitations_completed INT DEFAULT 0,
    videos_watched INT DEFAULT 0,
    total_points_earned INT DEFAULT 0,
    current_streak INT DEFAULT 0,
    longest_streak INT DEFAULT 0,
    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
); 



