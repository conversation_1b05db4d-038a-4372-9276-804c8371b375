{"brand": {"name": "recite_app", "logoPosition": "top-left", "primaryColor": "#5C4DFF", "accentColor": "#8A00FF", "background": "#F5F7FA", "borderRadius": "12px", "boxShadow": "0 8px 24px rgba(0, 0, 0, 0.05)"}, "colors": {"white": "#FFFFFF", "black": "#000000", "primary": "#5C4DFF", "secondary": "#8A00FF", "grayLight": "#E5E7EB", "grayMedium": "#D1D5DB", "grayDark": "#6B7280", "success": "#22C55E", "danger": "#EF4444", "info": "#3B82F6", "warning": "#F59E0B", "background": "#F9FAFB"}, "typography": {"fontFamily": "'Inter', sans-serif", "headingWeight": "600", "bodyWeight": "400", "fontSizes": {"heading1": "32px", "heading2": "24px", "heading3": "20px", "bodyLarge": "18px", "body": "16px", "small": "14px", "xs": "12px"}}, "spacing": {"none": "0px", "xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px"}, "buttons": {"primary": {"bg": "#5C4DFF", "color": "#FFFFFF", "padding": "12px 24px", "borderRadius": "8px", "hover": "#4F3BFF"}, "secondary": {"bg": "#F3F4F6", "color": "#111827", "padding": "12px 24px", "borderRadius": "8px", "hover": "#E5E7EB"}}, "inputs": {"bg": "#FFFFFF", "border": "1px solid #D1D5DB", "padding": "12px", "borderRadius": "8px", "placeholderColor": "#9CA3AF"}, "cards": {"bg": "#FFFFFF", "shadow": "0 4px 12px rgba(0, 0, 0, 0.05)", "borderRadius": "16px", "padding": "24px", "hoverShadow": "0 6px 16px rgba(0, 0, 0, 0.08)"}, "charts": {"primaryChart": {"type": "donut", "colorScheme": ["#5C4DFF", "#D8D6FF", "#B9B7F9"]}, "secondaryChart": {"type": "donut", "colorScheme": ["#8A00FF", "#D9B9FF", "#C387F0"]}}, "icons": {"size": "20px", "stroke": "1.5px", "defaultColor": "#6B7280", "hoverColor": "#5C4DFF"}, "layout": {"sidebar": {"width": "80px", "bg": "#FFFFFF", "icons": "vertical", "hover": "#F3F4F6"}, "navbar": {"height": "60px", "bg": "#FFFFFF", "padding": "16px", "shadow": true}, "mainContent": {"padding": "32px", "maxWidth": "1400px", "margin": "0 auto"}, "cardsPerRow": 2}, "animations": {"hover": "all 0.3s ease", "click": "scale(0.98)", "tooltipFade": "opacity 0.2s ease"}}