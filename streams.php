<?php
/**
 * Streams Page for Recite! App
 * Clean, Mobile-First Streams with Database Recordings
 */

require_once 'config/database.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Streams';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Include points system for social interactions
require_once 'includes/points_system.php';

// Handle social interactions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'like_recording') {
        $recordingId = intval($_POST['recording_id']);
        $recordingType = $_POST['recording_type'] ?? 'screen_record';
        $targetUserId = intval($_POST['target_user_id']);

        if ($recordingId > 0 && $targetUserId > 0 && $targetUserId !== $userId) {
            $success = processUserInteraction($userId, $targetUserId, $recordingId, $recordingType, 'like');
            if ($success) {
                $message = 'Recording liked! The creator earned 1 point.';
            } else {
                $error = 'You have already liked this recording.';
            }
        }
    } elseif ($action === 'comment_recording') {
        $recordingId = intval($_POST['recording_id']);
        $recordingType = $_POST['recording_type'] ?? 'screen_record';
        $targetUserId = intval($_POST['target_user_id']);
        $commentText = trim($_POST['comment_text'] ?? '');

        if ($recordingId > 0 && $targetUserId > 0 && $targetUserId !== $userId && !empty($commentText)) {
            $success = processUserInteraction($userId, $targetUserId, $recordingId, $recordingType, 'comment', $commentText);
            if ($success) {
                $message = 'Comment added! The creator earned 1 point.';
            } else {
                $error = 'Failed to add comment.';
            }
        }
    }
}

// Get all recordings from database with interaction data
$conn = getConnection();

// Get screen recordings with interaction counts
$screenRecords = [];
$screenRecordsQuery = "SELECT sr.*, u.full_name, u.profile_picture,
                       DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                       (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'like') as like_count,
                       (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'comment') as comment_count,
                       (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND user_id = ? AND interaction_type = 'like') as user_liked
                       FROM screen_records sr
                       JOIN users u ON sr.user_id = u.id
                       ORDER BY sr.created_at DESC";
$stmt = $conn->prepare($screenRecordsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$screenRecordsResult = $stmt->get_result();
if ($screenRecordsResult) {
    while ($row = $screenRecordsResult->fetch_assoc()) {
        $screenRecords[] = $row;
    }
}

// Get mirror recordings with interaction counts
$mirrorRecords = [];
$mirrorRecordsQuery = "SELECT mr.*, u.full_name, u.profile_picture,
                       DATE_FORMAT(mr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                       (SELECT COUNT(*) FROM user_interactions WHERE recording_id = mr.id AND recording_type = 'mirror_record' AND interaction_type = 'like') as like_count,
                       (SELECT COUNT(*) FROM user_interactions WHERE recording_id = mr.id AND recording_type = 'mirror_record' AND interaction_type = 'comment') as comment_count,
                       (SELECT COUNT(*) FROM user_interactions WHERE recording_id = mr.id AND recording_type = 'mirror_record' AND user_id = ? AND interaction_type = 'like') as user_liked
                       FROM mirror_recordings mr
                       JOIN users u ON mr.user_id = u.id
                       ORDER BY mr.created_at DESC";
$stmt = $conn->prepare($mirrorRecordsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$mirrorRecordsResult = $stmt->get_result();
if ($mirrorRecordsResult) {
    while ($row = $mirrorRecordsResult->fetch_assoc()) {
        $mirrorRecords[] = $row;
    }
}

// Combine all recordings
$allRecordings = array_merge($screenRecords, $mirrorRecords);

// Sort by created_at descending
usort($allRecordings, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design - Same as Dashboard */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }

        .header-center {
            flex: 1;
            max-width: 300px;
            margin: 0 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.75rem;
            text-align: center;
        }

        .welcome-card h2 {
            font-size: 1.1rem;
            margin: 0;
            font-weight: 600;
        }

        .welcome-card p {
            font-size: 0.8rem;
            margin: 0;
            opacity: 0.9;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-body {
            padding: 1rem;
        }

        /* Video Grid Layout */
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
            padding: 1rem;
        }

        /* Video Card */
        .video-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        /* Video Thumbnail Container */
        .video-thumbnail {
            position: relative;
            width: 100%;
            height: 180px;
            background: #000;
            overflow: hidden;
        }

        .video-thumbnail video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-thumbnail .thumbnail-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .video-card:hover .thumbnail-overlay {
            opacity: 1;
        }

        .play-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--primary);
            transition: transform 0.2s;
        }

        .play-button:hover {
            transform: scale(1.1);
        }

        /* Video Duration Badge */
        .duration-badge {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Video Info */
        .video-info {
            padding: 1rem;
        }

        .video-title {
            font-weight: 600;
            font-size: 0.95rem;
            line-height: 1.3;
            margin-bottom: 0.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .video-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .video-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .video-details {
            flex: 1;
            min-width: 0;
        }

        .video-author {
            font-weight: 500;
            font-size: 0.85rem;
            color: var(--text);
            margin-bottom: 2px;
        }

        .video-stats {
            font-size: 0.75rem;
            color: #666;
        }

        /* Video Actions */
        .video-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .btn-video {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .btn-play {
            background: var(--primary);
            color: white;
        }

        .btn-play:hover {
            background: var(--primary-light);
        }

        .btn-download {
            background: #f8f9fa;
            color: var(--text);
            border: 1px solid var(--border);
        }

        .btn-download:hover {
            background: #e9ecef;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 36px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--border);
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border: 1px solid transparent;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Video Modal */
        .video-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            padding: 2rem;
        }

        .video-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            max-width: 90vw;
            max-height: 90vh;
            width: 800px;
            position: relative;
        }

        .modal-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0.25rem;
            border-radius: 4px;
        }

        .modal-close:hover {
            background: #f8f9fa;
        }

        .modal-video {
            width: 100%;
            height: 450px;
            background: #000;
        }

        .modal-video video {
            width: 100%;
            height: 100%;
        }

        .modal-info {
            padding: 1.5rem;
        }

        .modal-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .modal-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .modal-details h3 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
        }

        .modal-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--border);
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }

            .video-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }

        @media (max-width: 767px) {
            .header-center {
                display: none;
            }

            .video-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 0.5rem;
            }

            .video-thumbnail {
                height: 200px;
            }

            .modal-content {
                width: 95vw;
                max-height: 95vh;
            }

            .modal-video {
                height: 250px;
            }

            .video-modal {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='profile.php'">
                    <?php if (!empty($user['profile_picture'])): ?>
                        <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>"
                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <?php else: ?>
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="header-center">
                <input type="text" class="search-input" placeholder="Search recordings...">
            </div>

            <div class="header-right">
                <div class="notification-icon" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <div class="notification-badge">1</div>
                </div>
            </div>
        </div>

        <div class="welcome-card">
            <h2><i class="fas fa-stream"></i> Streams</h2>
            <p>Watch and share Qur'an recitations</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($allRecordings)): ?>
            <div class="video-grid">
                <?php foreach ($allRecordings as $index => $recording): ?>
                    <div class="video-card" onclick="openVideoModal(<?php echo $index; ?>)">
                        <div class="video-thumbnail">
                            <video preload="metadata" muted playsinline>
                                <source src="<?php echo htmlspecialchars($recording['file_path']); ?>" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                            <div class="thumbnail-overlay">
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                            <div class="duration-badge" id="duration-<?php echo $index; ?>">
                                --:--
                            </div>
                        </div>

                        <div class="video-info">
                            <div class="video-title">
                                <?php echo htmlspecialchars($recording['title'] ?? 'Recitation Recording'); ?>
                            </div>

                            <div class="video-meta">
                                <div class="video-avatar">
                                    <?php if (!empty($recording['profile_picture'])): ?>
                                        <img src="<?php echo htmlspecialchars($recording['profile_picture']); ?>"
                                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                                    <?php else: ?>
                                        <?php echo strtoupper(substr($recording['full_name'], 0, 1)); ?>
                                    <?php endif; ?>
                                </div>

                                <div class="video-details">
                                    <div class="video-author">
                                        <?php echo htmlspecialchars($recording['full_name']); ?>
                                    </div>
                                    <div class="video-stats">
                                        <?php echo $recording['formatted_date']; ?>
                                        <?php if (isset($recording['file_size']) && $recording['file_size'] > 0): ?>
                                            • <?php echo number_format($recording['file_size'] / 1024 / 1024, 1); ?>MB
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Interaction Stats -->
                            <div style="display: flex; gap: 1rem; margin-bottom: 0.75rem; font-size: 0.8rem; color: #666;">
                                <span><i class="fas fa-heart"></i> <?php echo $recording['like_count'] ?? 0; ?> likes</span>
                                <span><i class="fas fa-comment"></i> <?php echo $recording['comment_count'] ?? 0; ?> comments</span>
                            </div>

                            <div class="video-actions">
                                <button class="btn-video btn-play" onclick="event.stopPropagation(); playVideo(<?php echo $index; ?>)">
                                    <i class="fas fa-play"></i> Play
                                </button>

                                <?php if ($recording['user_id'] != $userId): ?>
                                    <!-- Like Button -->
                                    <form method="POST" style="display: inline;" onclick="event.stopPropagation();">
                                        <input type="hidden" name="action" value="like_recording">
                                        <input type="hidden" name="recording_id" value="<?php echo $recording['id']; ?>">
                                        <input type="hidden" name="recording_type" value="<?php echo $recording['record_type'] ?? 'screen_record'; ?>">
                                        <input type="hidden" name="target_user_id" value="<?php echo $recording['user_id']; ?>">
                                        <button type="submit" class="btn-video"
                                                style="background: <?php echo ($recording['user_liked'] ?? 0) > 0 ? '#ff4757' : '#f1f2f6'; ?>;
                                                       color: <?php echo ($recording['user_liked'] ?? 0) > 0 ? 'white' : '#666'; ?>;"
                                                <?php echo ($recording['user_liked'] ?? 0) > 0 ? 'disabled' : ''; ?>>
                                            <i class="fas fa-heart"></i> <?php echo ($recording['user_liked'] ?? 0) > 0 ? 'Liked' : 'Like'; ?>
                                        </button>
                                    </form>

                                    <!-- Comment Button -->
                                    <button class="btn-video" onclick="event.stopPropagation(); toggleComment(<?php echo $recording['id']; ?>)"
                                            style="background: #f1f2f6; color: #666;">
                                        <i class="fas fa-comment"></i> Comment
                                    </button>
                                <?php endif; ?>
                            </div>

                            <?php if ($recording['user_id'] != $userId): ?>
                                <!-- Comment Form (Hidden by default) -->
                                <div id="comment-form-<?php echo $recording['id']; ?>" style="display: none; margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--border);">
                                    <form method="POST" onclick="event.stopPropagation();">
                                        <input type="hidden" name="action" value="comment_recording">
                                        <input type="hidden" name="recording_id" value="<?php echo $recording['id']; ?>">
                                        <input type="hidden" name="recording_type" value="<?php echo $recording['record_type'] ?? 'screen_record'; ?>">
                                        <input type="hidden" name="target_user_id" value="<?php echo $recording['user_id']; ?>">
                                        <div style="display: flex; gap: 0.5rem;">
                                            <input type="text" name="comment_text" placeholder="Add a comment..."
                                                   style="flex: 1; padding: 0.5rem; border: 1px solid var(--border); border-radius: 4px; font-size: 0.8rem;" required>
                                            <button type="submit" class="btn-video btn-play" style="padding: 0.5rem;">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-video"></i>
                        <h3>No Recordings Yet</h3>
                        <p>Start recording from the dashboard to see your streams here!</p>
                        <a href="dashboard.php" class="btn btn-primary" style="margin-top: 1rem;">
                            <i class="fas fa-home"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Video Modal -->
    <div class="video-modal" id="videoModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">Video Player</div>
                <button class="modal-close" onclick="closeVideoModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-video">
                <video id="modalVideo" controls preload="metadata" style="width: 100%; height: 100%;" volume="1.0">
                    <source id="modalVideoSource" src="" type="video/webm">
                    <source id="modalVideoSourceMp4" src="" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>

            <div class="modal-info">
                <div class="modal-meta">
                    <div class="modal-avatar" id="modalAvatar">
                        U
                    </div>
                    <div class="modal-details">
                        <h3 id="modalAuthor">User Name</h3>
                        <p id="modalDate">Upload Date</p>
                    </div>
                </div>

                <div class="video-actions">
                    <p style="color: #666; font-size: 0.9rem; text-align: center; margin: 0;">
                        <i class="fas fa-info-circle"></i> Enjoy watching the recitation
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="community.php" class="nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="streams.php" class="nav-item active">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <script>
        // Video data for JavaScript
        const videoData = <?php echo json_encode($allRecordings); ?>;

        // Load video durations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadVideoDurations();
        });

        // Load video durations from actual video elements
        function loadVideoDurations() {
            const videos = document.querySelectorAll('.video-thumbnail video');
            videos.forEach((video, index) => {
                video.addEventListener('loadedmetadata', function() {
                    const duration = formatDuration(video.duration);
                    const durationBadge = document.getElementById(`duration-${index}`);
                    if (durationBadge) {
                        durationBadge.textContent = duration;
                    }
                });

                // Trigger load
                video.load();
            });
        }

        // Format duration in MM:SS format
        function formatDuration(seconds) {
            if (isNaN(seconds) || seconds === 0) return '--:--';

            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // Play video (opens modal directly)
        function playVideo(index) {
            openVideoModal(index);
        }

        // Open video modal
        function openVideoModal(index) {
            const recording = videoData[index];
            if (!recording) return;

            // Update modal content
            document.getElementById('modalTitle').textContent = recording.title || 'Recitation Recording';
            document.getElementById('modalAuthor').textContent = recording.full_name;
            document.getElementById('modalDate').textContent = recording.formatted_date;
            document.getElementById('modalVideoSource').src = recording.file_path;
            document.getElementById('modalVideoSourceMp4').src = recording.file_path;

            // Update avatar
            const modalAvatar = document.getElementById('modalAvatar');
            if (recording.profile_picture) {
                modalAvatar.innerHTML = `<img src="${recording.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
            } else {
                modalAvatar.textContent = recording.full_name.charAt(0).toUpperCase();
            }

            // Load and show modal
            const modalVideo = document.getElementById('modalVideo');
            modalVideo.load();

            // Ensure audio is enabled
            modalVideo.muted = false;
            modalVideo.volume = 1.0;

            const modal = document.getElementById('videoModal');
            modal.classList.add('active');

            // Auto-play the video
            modalVideo.play().catch(e => {
                console.log('Auto-play prevented by browser policy');
            });
        }

        // Close video modal
        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const modalVideo = document.getElementById('modalVideo');

            modalVideo.pause();
            modalVideo.currentTime = 0;
            modal.classList.remove('active');
        }

        // Close modal when clicking outside
        document.getElementById('videoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVideoModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('videoModal');
            if (modal.classList.contains('active')) {
                if (e.key === 'Escape') {
                    closeVideoModal();
                } else if (e.key === ' ') {
                    e.preventDefault();
                    const video = document.getElementById('modalVideo');
                    if (video.paused) {
                        video.play();
                    } else {
                        video.pause();
                    }
                }
            }
        });

        // Show notifications
        function showNotifications() {
            alert('Streams Notifications:\n• New recording uploaded\n• Someone liked your video');
        }

        // Toggle comment form
        function toggleComment(recordingId) {
            const commentForm = document.getElementById('comment-form-' + recordingId);
            if (commentForm.style.display === 'none' || commentForm.style.display === '') {
                commentForm.style.display = 'block';
                commentForm.querySelector('input[name="comment_text"]').focus();
            } else {
                commentForm.style.display = 'none';
            }
        }

        // Optimize video loading
        function optimizeVideoLoading() {
            const videos = document.querySelectorAll('.video-thumbnail video');

            // Use Intersection Observer for lazy loading
            if ('IntersectionObserver' in window) {
                const videoObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const video = entry.target;
                            if (!video.src && video.querySelector('source')) {
                                video.load();
                            }
                            videoObserver.unobserve(video);
                        }
                    });
                });

                videos.forEach(video => {
                    videoObserver.observe(video);
                });
            }
        }

        // Initialize optimizations
        document.addEventListener('DOMContentLoaded', function() {
            optimizeVideoLoading();
        });
    </script>
</body>
</html>
