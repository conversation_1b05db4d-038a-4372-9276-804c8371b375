<?php
/**
 * Streams Page for Recite! App
 * Clean, Mobile-First Streams with Database Recordings
 */

require_once 'config/database.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Streams';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Get all recordings from database
$conn = getConnection();

// Get screen recordings (dashboard recordings)
$screenRecords = [];
$screenRecordsQuery = "SELECT sr.*, u.full_name, u.profile_picture,
                       DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date
                       FROM screen_records sr
                       JOIN users u ON sr.user_id = u.id
                       ORDER BY sr.created_at DESC";
$screenRecordsResult = $conn->query($screenRecordsQuery);
if ($screenRecordsResult) {
    while ($row = $screenRecordsResult->fetch_assoc()) {
        $screenRecords[] = $row;
    }
}

// Get mirror recordings (selfie mirror recordings)
$mirrorRecords = [];
$mirrorRecordsQuery = "SELECT mr.*, u.full_name, u.profile_picture,
                       DATE_FORMAT(mr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date
                       FROM mirror_recordings mr
                       JOIN users u ON mr.user_id = u.id
                       ORDER BY mr.created_at DESC";
$mirrorRecordsResult = $conn->query($mirrorRecordsQuery);
if ($mirrorRecordsResult) {
    while ($row = $mirrorRecordsResult->fetch_assoc()) {
        $mirrorRecords[] = $row;
    }
}

// Combine all recordings
$allRecordings = array_merge($screenRecords, $mirrorRecords);

// Sort by created_at descending
usort($allRecordings, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design - Same as Dashboard */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }

        .header-center {
            flex: 1;
            max-width: 300px;
            margin: 0 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.75rem;
            text-align: center;
        }

        .welcome-card h2 {
            font-size: 1.1rem;
            margin: 0;
            font-weight: 600;
        }

        .welcome-card p {
            font-size: 0.8rem;
            margin: 0;
            opacity: 0.9;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-body {
            padding: 1rem;
        }

        /* Recording Item */
        .recording-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .recording-item:last-child {
            border-bottom: none;
        }

        .recording-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .recording-content {
            flex: 1;
        }

        .recording-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .recording-meta {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .recording-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 36px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--border);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 768px;
                margin: 0 auto;
            }
        }

        @media (max-width: 767px) {
            .header h1 {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='profile.php'">
                    <?php if (!empty($user['profile_picture'])): ?>
                        <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>"
                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <?php else: ?>
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="header-center">
                <input type="text" class="search-input" placeholder="Search recordings...">
            </div>

            <div class="header-right">
                <div class="notification-icon" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <div class="notification-badge">1</div>
                </div>
            </div>
        </div>

        <div class="welcome-card">
            <h2><i class="fas fa-stream"></i> Streams</h2>
            <p>Watch and share Qur'an recitations</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="card">
            <div class="card-body">
                <?php if (!empty($allRecordings)): ?>
                    <?php foreach ($allRecordings as $recording): ?>
                        <div class="recording-item">
                            <div class="recording-avatar">
                                <?php if (!empty($recording['profile_picture'])): ?>
                                    <img src="<?php echo htmlspecialchars($recording['profile_picture']); ?>" 
                                         alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                                <?php else: ?>
                                    <?php echo strtoupper(substr($recording['full_name'], 0, 1)); ?>
                                <?php endif; ?>
                            </div>
                            
                            <div class="recording-content">
                                <div class="recording-title">
                                    <?php echo htmlspecialchars($recording['title'] ?? 'Recitation Recording'); ?>
                                </div>
                                <div class="recording-meta">
                                    By <?php echo htmlspecialchars($recording['full_name']); ?> • 
                                    <?php echo $recording['formatted_date']; ?>
                                </div>
                                <div class="recording-actions">
                                    <a href="<?php echo htmlspecialchars($recording['file_path']); ?>" 
                                       class="btn btn-primary" target="_blank">
                                        <i class="fas fa-play"></i> Play
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-video"></i>
                        <h3>No Recordings Yet</h3>
                        <p>Start recording from the dashboard to see your streams here!</p>
                        <a href="dashboard.php" class="btn btn-primary" style="margin-top: 1rem;">
                            <i class="fas fa-home"></i> Go to Dashboard
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="community.php" class="nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="streams.php" class="nav-item active">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <script>
        // Show notifications
        function showNotifications() {
            alert('Streams Notifications:\n• New recording uploaded\n• Someone liked your video');
        }
    </script>
</body>
</html>
