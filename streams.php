<?php
/**
 * Streams Page for Recite! App
 * Public feed of all user recitation recordings
 */

require_once 'config/database.php';

// Set page title for navigation
$page_title = 'Streams';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Get all streams with user information
$streams = [];
try {
    $result = executeQuery(
        "SELECT s.*, u.full_name, u.profile_picture, u.ward, u.lga, u.state,
                DATE_FORMAT(s.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                (SELECT COUNT(*) FROM stream_interactions si WHERE si.stream_id = s.id AND si.interaction_type = 'like') as like_count,
                (SELECT COUNT(*) FROM stream_interactions si WHERE si.stream_id = s.id AND si.interaction_type = 'comment') as comment_count,
                (SELECT COUNT(*) FROM stream_interactions si WHERE si.stream_id = s.id AND si.interaction_type = 'share') as share_count,
                (SELECT COUNT(*) FROM stream_interactions si WHERE si.stream_id = s.id AND si.interaction_type = 'view') as view_count
         FROM streams s 
         JOIN users u ON s.user_id = u.id 
         WHERE u.is_active = 1 
         ORDER BY s.created_at DESC 
         LIMIT 50"
    );
    
    while ($row = $result->fetch_assoc()) {
        $streams[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching streams: " . $e->getMessage());
}

// Include the header with sidebar
require_once 'components/user_header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <h1 class="mb-4 text-dark-red">Community Streams</h1>

            <!-- Create Post -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form action="api/create_post.php" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <textarea name="text_content" class="form-control mb-2" placeholder="Share your thoughts..." rows="3"></textarea>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <input type="file" name="media_path" class="form-control d-none" id="mediaUpload">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('mediaUpload').click()">
                                    <i class="fas fa-camera"></i> Photo/Video
                                </button>
                            </div>
                            <button type="submit" class="btn btn-primary" style="background-color: var(--primary-red); border-color: var(--primary-red);">
                                <i class="fas fa-paper-plane"></i> Post
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Streams Feed -->
            <?php foreach ($streams as $stream): ?>
                <div class="card stream-card shadow-sm mb-4">
                    <div class="card-header bg-white d-flex align-items-center">
                        <img src="<?php echo $stream['profile_picture'] ? 'uploads/profiles/' . $stream['profile_picture'] : 'assets/images/default-avatar.png'; ?>" 
                             alt="Profile" class="rounded-circle me-3" style="width: 45px; height: 45px;">
                        <div>
                            <h6 class="mb-0 fw-bold text-dark-red"><?php echo htmlspecialchars($stream['full_name']); ?></h6>
                            <small class="text-muted"><?php echo $stream['formatted_date']; ?></small>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($stream['text_content'])): ?>
                            <p><?php echo nl2br(htmlspecialchars($stream['text_content'])); ?></p>
                        <?php endif; ?>

                        <?php if ($stream['content_type'] === 'recitation' && !empty($stream['media_path'])): ?>
                            <div class="video-thumbnail mb-2">
                                <video width="100%" controls poster="assets/images/recite-thumbnail.jpg">
                                    <source src="uploads/screen-recordings/<?php echo $stream['media_path']; ?>" type="video/webm">
                                    Your browser does not support the video tag.
                                </video>
                            </div>
                        <?php elseif (in_array($stream['content_type'], ['image', 'video']) && !empty($stream['media_path'])): ?>
                             <img src="uploads/posts/<?php echo $stream['media_path']; ?>" class="img-fluid rounded" alt="Post media">
                        <?php endif; ?>
                    </div>
                    <div class="card-footer bg-white d-flex justify-content-around">
                        <button class="btn btn-sm btn-outline-secondary interaction-btn" data-stream-id="<?php echo $stream['id']; ?>" data-type="like">
                            <i class="fas fa-heart"></i> <span class="like-count"><?php echo $stream['like_count']; ?></span>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary interaction-btn">
                            <i class="fas fa-comment"></i> <?php echo $stream['comment_count']; ?>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary interaction-btn">
                            <i class="fas fa-share"></i> <?php echo $stream['share_count']; ?>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
             <?php if (empty($streams)): ?>
                <div class="text-center py-5">
                    <p class="text-muted">No streams available yet. Be the first to post!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'components/user_footer.php'; ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const interactionButtons = document.querySelectorAll('.interaction-btn');
    
    interactionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const streamId = this.dataset.streamId;
            const type = this.dataset.type;
            
            if (type === 'like') {
                fetch('api/stream_interaction.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ stream_id: streamId, interaction_type: 'like' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const likeCountElement = this.querySelector('.like-count');
                        likeCountElement.textContent = data.new_like_count;
                        this.classList.toggle('liked', data.liked);
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    });
});
</script>
</body>
</html> 