<?php
/**
 * Streams Page for Recite! App
 * Public feed of all user recitation recordings
 */

require_once 'config/database.php';

// Set page title for navigation
$page_title = 'Streams';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Get all streams with user information
$streams = [];
try {
    // Get streams from streams table
    $result = executeQuery(
        "SELECT s.*, u.full_name, u.profile_picture, u.ward, u.lga, u.state,
                DATE_FORMAT(s.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                s.likes_count as like_count,
                s.comments_count as comment_count,
                s.shares_count as share_count,
                s.views_count as view_count
         FROM streams s
         JOIN users u ON s.user_id = u.id
         WHERE s.is_public = 1 AND u.is_blocked = 0
         ORDER BY s.created_at DESC
         LIMIT 25"
    );

    while ($row = $result->fetch_assoc()) {
        $streams[] = $row;
    }

    // Also get recent screen recordings
    $screenRecordsResult = executeQuery(
        "SELECT sr.*, u.full_name, u.profile_picture, u.ward, u.lga, u.state,
                DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                sr.likes_count as like_count,
                sr.comments_count as comment_count,
                sr.shares_count as share_count,
                sr.views_count as view_count,
                'video' as content_type,
                sr.file_path as media_path,
                sr.title as text_content
         FROM screen_records sr
         JOIN users u ON sr.user_id = u.id
         WHERE sr.is_public = 1 AND u.is_blocked = 0
         ORDER BY sr.created_at DESC
         LIMIT 15"
    );

    while ($row = $screenRecordsResult->fetch_assoc()) {
        $streams[] = $row;
    }

    // Also get recent mirror recordings (selfie videos)
    $mirrorRecordsResult = executeQuery(
        "SELECT mr.*, u.full_name, u.profile_picture, u.ward, u.lga, u.state,
                DATE_FORMAT(mr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                mr.likes_count as like_count,
                mr.comments_count as comment_count,
                mr.shares_count as share_count,
                mr.views_count as view_count,
                'video' as content_type,
                mr.file_path as media_path,
                mr.title as text_content
         FROM mirror_recordings mr
         JOIN users u ON mr.user_id = u.id
         WHERE mr.is_public = 1 AND u.is_blocked = 0
         ORDER BY mr.created_at DESC
         LIMIT 10"
    );

    while ($row = $mirrorRecordsResult->fetch_assoc()) {
        $streams[] = $row;
    }

    // Sort all streams by creation date
    usort($streams, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

} catch (Exception $e) {
    error_log("Error fetching streams: " . $e->getMessage());
}

// Include the header with sidebar
require_once 'components/user_header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <h1 class="mb-4 text-dark-red">Community Streams</h1>

            <!-- Create Post -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form action="api/create_post.php" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <textarea name="text_content" class="form-control mb-2" placeholder="Share your thoughts..." rows="3"></textarea>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <input type="file" name="media_path" class="form-control d-none" id="mediaUpload">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('mediaUpload').click()">
                                    <i class="fas fa-camera"></i> Photo/Video
                                </button>
                            </div>
                            <button type="submit" class="btn btn-primary" style="background-color: var(--primary-red); border-color: var(--primary-red);">
                                <i class="fas fa-paper-plane"></i> Post
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Streams Feed -->
            <?php foreach ($streams as $stream): ?>
                <div class="card stream-card shadow-sm mb-4">
                    <div class="card-header bg-white d-flex align-items-center">
                        <img src="<?php echo $stream['profile_picture'] ? 'uploads/profiles/' . $stream['profile_picture'] : 'assets/images/default-avatar.png'; ?>" 
                             alt="Profile" class="rounded-circle me-3" style="width: 45px; height: 45px;">
                        <div>
                            <h6 class="mb-0 fw-bold text-dark-red"><?php echo htmlspecialchars($stream['full_name']); ?></h6>
                            <small class="text-muted"><?php echo $stream['formatted_date']; ?></small>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($stream['text_content'])): ?>
                            <p><?php echo nl2br(htmlspecialchars($stream['text_content'])); ?></p>
                        <?php endif; ?>

                        <?php if ($stream['content_type'] === 'video' && !empty($stream['media_path'])): ?>
                            <div class="video-thumbnail mb-2">
                                <video width="100%" controls poster="assets/images/recite-thumbnail.jpg" style="border-radius: 8px;">
                                    <source src="<?php echo htmlspecialchars($stream['media_path']); ?>" type="video/webm">
                                    <source src="<?php echo htmlspecialchars($stream['media_path']); ?>" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            </div>
                        <?php elseif ($stream['content_type'] === 'image' && !empty($stream['media_path'])): ?>
                            <div class="image-container mb-2">
                                <img src="<?php echo htmlspecialchars($stream['media_path']); ?>" class="img-fluid rounded" alt="Post media" style="max-height: 400px; width: 100%; object-fit: cover;">
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer bg-white d-flex justify-content-around">
                        <button class="btn btn-sm btn-outline-secondary interaction-btn" data-stream-id="<?php echo $stream['id']; ?>" data-type="like">
                            <i class="fas fa-heart"></i> <span class="like-count"><?php echo $stream['like_count']; ?></span>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary interaction-btn">
                            <i class="fas fa-comment"></i> <?php echo $stream['comment_count']; ?>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary interaction-btn">
                            <i class="fas fa-share"></i> <?php echo $stream['share_count']; ?>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
             <?php if (empty($streams)): ?>
                <div class="text-center py-5">
                    <p class="text-muted">No streams available yet. Be the first to post!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'components/user_footer.php'; ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const interactionButtons = document.querySelectorAll('.interaction-btn');
    
    interactionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const streamId = this.dataset.streamId;
            const type = this.dataset.type;
            
            if (type === 'like') {
                fetch('api/stream_interaction.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ stream_id: streamId, interaction_type: 'like' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const likeCountElement = this.querySelector('.like-count');
                        likeCountElement.textContent = data.new_like_count;
                        this.classList.toggle('liked', data.liked);
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    });
});
</script>
</body>
</html> 