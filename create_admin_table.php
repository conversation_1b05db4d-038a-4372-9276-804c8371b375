<?php
/**
 * Create Admin Table Script
 * Run this once to create the admin table
 */

require_once 'config/database.php';

try {
    $conn = getConnection();
    
    // Create admins table if it doesn't exist
    $createAdminTable = "
        CREATE TABLE IF NOT EXISTS admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100),
            full_name VARCHAR(100),
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    
    $conn->query($createAdminTable);
    
    // Create default admin if doesn't exist
    $username = 'admin';
    $password = '1@3Usazladan';
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    $checkAdmin = $conn->prepare("SELECT id FROM admins WHERE username = ?");
    $checkAdmin->bind_param("s", $username);
    $checkAdmin->execute();
    $result = $checkAdmin->get_result();
    
    if ($result->num_rows === 0) {
        $insertAdmin = $conn->prepare("INSERT INTO admins (username, password, email, full_name) VALUES (?, ?, ?, ?)");
        $email = '<EMAIL>';
        $fullName = 'System Administrator';
        $insertAdmin->bind_param("ssss", $username, $hashedPassword, $email, $fullName);
        $insertAdmin->execute();
        
        echo "✅ Admin table created and default admin account set up successfully!<br>";
        echo "Username: admin<br>";
        echo "Password: 1@3Usazladan<br>";
    } else {
        echo "✅ Admin table exists and admin account is already set up!<br>";
    }
    
    // Also ensure users table has payment_verified column
    $addPaymentColumn = "ALTER TABLE users ADD COLUMN IF NOT EXISTS payment_verified TINYINT(1) DEFAULT 0";
    $conn->query($addPaymentColumn);
    
    // Update existing users without payment verification
    $updateExisting = "UPDATE users SET payment_verified = 1 WHERE is_active = 1 AND payment_verified = 0";
    $conn->query($updateExisting);
    
    echo "✅ Users table payment verification column checked/added!<br>";
    echo "<br><a href='admin/login.php'>Go to Admin Login</a><br>";
    echo "<a href='login.php'>Go to User Login</a>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?> 