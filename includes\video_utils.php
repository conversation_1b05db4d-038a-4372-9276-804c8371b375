<?php
/**
 * Video Utility Functions for RECITE App
 * Handles video thumbnails, duration, and metadata
 */

/**
 * Generate video thumbnail using FFmpeg (if available)
 * Falls back to first frame extraction
 */
function generateVideoThumbnail($videoPath, $thumbnailPath = null) {
    if (!file_exists($videoPath)) {
        return false;
    }
    
    // Create thumbnails directory if it doesn't exist
    $thumbnailDir = 'uploads/thumbnails/';
    if (!is_dir($thumbnailDir)) {
        mkdir($thumbnailDir, 0755, true);
    }
    
    // Generate thumbnail filename if not provided
    if (!$thumbnailPath) {
        $videoName = pathinfo($videoPath, PATHINFO_FILENAME);
        $thumbnailPath = $thumbnailDir . $videoName . '_thumb.jpg';
    }
    
    // Check if thumbnail already exists
    if (file_exists($thumbnailPath)) {
        return $thumbnailPath;
    }
    
    // Try to use FFmpeg if available
    if (isFFmpegAvailable()) {
        $command = "ffmpeg -i " . escapeshellarg($videoPath) . " -ss 00:00:01 -vframes 1 -y " . escapeshellarg($thumbnailPath) . " 2>&1";
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($thumbnailPath)) {
            return $thumbnailPath;
        }
    }
    
    // Fallback: Create a placeholder thumbnail
    return createPlaceholderThumbnail($thumbnailPath);
}

/**
 * Check if FFmpeg is available on the system
 */
function isFFmpegAvailable() {
    exec('ffmpeg -version 2>&1', $output, $returnCode);
    return $returnCode === 0;
}

/**
 * Create a placeholder thumbnail image
 */
function createPlaceholderThumbnail($thumbnailPath) {
    $width = 320;
    $height = 180;
    
    // Create image
    $image = imagecreate($width, $height);
    
    // Colors
    $backgroundColor = imagecolorallocate($image, 26, 95, 63); // Primary color
    $textColor = imagecolorallocate($image, 255, 255, 255);
    
    // Fill background
    imagefill($image, 0, 0, $backgroundColor);
    
    // Add play icon (simple triangle)
    $playIcon = array(
        $width/2 - 20, $height/2 - 15,  // Top point
        $width/2 - 20, $height/2 + 15,  // Bottom left
        $width/2 + 15, $height/2        // Right point
    );
    imagefilledpolygon($image, $playIcon, 3, $textColor);
    
    // Add text
    $text = 'VIDEO';
    $fontSize = 3;
    $textWidth = imagefontwidth($fontSize) * strlen($text);
    $textHeight = imagefontheight($fontSize);
    $x = ($width - $textWidth) / 2;
    $y = ($height + $textHeight) / 2 + 25;
    
    imagestring($image, $fontSize, $x, $y, $text, $textColor);
    
    // Save image
    $result = imagejpeg($image, $thumbnailPath, 80);
    imagedestroy($image);
    
    return $result ? $thumbnailPath : false;
}

/**
 * Get video duration using FFprobe or getID3
 */
function getVideoDuration($videoPath) {
    if (!file_exists($videoPath)) {
        return 0;
    }
    
    // Try FFprobe first
    if (isFFmpegAvailable()) {
        $command = "ffprobe -v quiet -show_entries format=duration -of csv=p=0 " . escapeshellarg($videoPath) . " 2>&1";
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && isset($output[0]) && is_numeric($output[0])) {
            return (float)$output[0];
        }
    }
    
    // Fallback: Try to get file size as rough estimate
    $fileSize = filesize($videoPath);
    // Very rough estimate: assume 1MB per minute for web video
    return $fileSize / (1024 * 1024) * 60;
}

/**
 * Format duration in human readable format
 */
function formatVideoDuration($seconds) {
    if ($seconds < 60) {
        return '0:' . str_pad((int)$seconds, 2, '0', STR_PAD_LEFT);
    } elseif ($seconds < 3600) {
        $minutes = floor($seconds / 60);
        $remainingSeconds = $seconds % 60;
        return $minutes . ':' . str_pad((int)$remainingSeconds, 2, '0', STR_PAD_LEFT);
    } else {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;
        return $hours . ':' . str_pad($minutes, 2, '0', STR_PAD_LEFT) . ':' . str_pad((int)$remainingSeconds, 2, '0', STR_PAD_LEFT);
    }
}

/**
 * Get video metadata
 */
function getVideoMetadata($videoPath) {
    $metadata = array(
        'duration' => 0,
        'size' => 0,
        'thumbnail' => null,
        'format' => 'unknown'
    );
    
    if (!file_exists($videoPath)) {
        return $metadata;
    }
    
    // Get file size
    $metadata['size'] = filesize($videoPath);
    
    // Get file format
    $extension = strtolower(pathinfo($videoPath, PATHINFO_EXTENSION));
    $metadata['format'] = $extension;
    
    // Get duration
    $metadata['duration'] = getVideoDuration($videoPath);
    
    // Generate thumbnail
    $metadata['thumbnail'] = generateVideoThumbnail($videoPath);
    
    return $metadata;
}

/**
 * Check if file is a valid video format
 */
function isValidVideoFormat($filename) {
    $allowedFormats = array('mp4', 'webm', 'avi', 'mov', 'mkv', 'flv', '3gp');
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedFormats);
}

/**
 * Optimize video for web streaming (if FFmpeg available)
 */
function optimizeVideoForWeb($inputPath, $outputPath = null) {
    if (!isFFmpegAvailable() || !file_exists($inputPath)) {
        return false;
    }
    
    if (!$outputPath) {
        $pathInfo = pathinfo($inputPath);
        $outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_optimized.mp4';
    }
    
    // FFmpeg command for web optimization
    $command = "ffmpeg -i " . escapeshellarg($inputPath) . 
               " -c:v libx264 -preset medium -crf 23 -c:a aac -b:a 128k" .
               " -movflags +faststart -y " . escapeshellarg($outputPath) . " 2>&1";
    
    exec($command, $output, $returnCode);
    
    return $returnCode === 0 && file_exists($outputPath) ? $outputPath : false;
}
?>
