/**
 * Dashboard JavaScript for Recite! App
 * Handles speech recognition, screen recording, content unlocking, and camera
 */

class RecitationDashboard {
    constructor() {
        this.currentContent = null;
        this.recognition = null;
        this.mediaRecorder = null;
        this.recordingStream = null;
        this.currentWordIndex = 0;
        this.arabicWords = [];
        this.score = 100;
        this.errorAttempts = {};
        this.isReciting = false;
        this.camera = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initSpeechRecognition();
        this.setupCamera();
    }
    
    setupEventListeners() {
        // Content unlocking
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('unlock-content')) {
                this.unlockContent(e.target);
            }
            
            if (e.target.classList.contains('start-recitation')) {
                this.startRecitation(e.target);
            }
        });
        
        // Recitation controls
        const startRecitingBtn = document.getElementById('start-reciting');
        const stopRecitingBtn = document.getElementById('stop-reciting');
        const resetRecitationBtn = document.getElementById('reset-recitation');
        const stopVideoBtn = document.getElementById('stop-video');
        const startCameraBtn = document.getElementById('start-camera');
        
        if (startRecitingBtn) {
            startRecitingBtn.addEventListener('click', () => this.beginReciting());
        }
        
        if (stopRecitingBtn) {
            stopRecitingBtn.addEventListener('click', () => this.stopReciting());
        }
        
        if (resetRecitationBtn) {
            resetRecitationBtn.addEventListener('click', () => this.resetRecitation());
        }
        
        if (stopVideoBtn) {
            stopVideoBtn.addEventListener('click', () => this.stopVideo());
        }
        
        if (startCameraBtn) {
            startCameraBtn.addEventListener('click', () => this.enableCamera());
        }
    }
    
    async unlockContent(button) {
        const contentId = button.dataset.contentId;
        const price = parseFloat(button.dataset.price);
        
        if (!confirm(`Unlock this content for ₦${price}?`)) {
            return;
        }
        
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Unlocking...';
        
        try {
            const response = await fetch('api/unlock_content.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content_id: contentId,
                    csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                button.className = 'btn btn-success btn-sm start-recitation';
                button.innerHTML = '<i class="fas fa-play"></i> Start Recitation';
                button.dataset.youtubeId = result.youtube_id;
                button.dataset.surahName = result.surah_name;
                button.dataset.arabicText = result.arabic_text;
                
                this.showAlert('success', 'Content unlocked successfully!');
                this.updateWalletDisplay(result.new_balance);
            } else {
                throw new Error(result.message || 'Failed to unlock content');
            }
        } catch (error) {
            console.error('Unlock error:', error);
            this.showAlert('danger', error.message);
            button.disabled = false;
            button.innerHTML = `<i class="fas fa-lock"></i> Unlock (₦${price})`;
        }
    }
    
    startRecitation(button) {
        const contentId = button.dataset.contentId;
        const youtubeId = button.dataset.youtubeId;
        const surahName = button.dataset.surahName;
        const arabicText = button.dataset.arabicText;
        
        this.currentContent = {
            id: contentId,
            youtubeId: youtubeId,
            name: surahName,
            text: arabicText
        };
        
        // Load video
        this.loadVideo(youtubeId, surahName);
        
        // Load Arabic text
        this.loadArabicText(arabicText);
        
        // Reset recitation state
        this.resetRecitation();
    }
    
    loadVideo(youtubeId, surahName) {
        const videoPlayer = document.getElementById('video-player');
        const contentList = document.getElementById('content-list');
        const youtubePlayer = document.getElementById('youtube-player');
        const currentSurahName = document.getElementById('current-surah-name');
        
        youtubePlayer.src = `https://www.youtube.com/embed/${youtubeId}?autoplay=1&enablejsapi=1`;
        currentSurahName.textContent = surahName;
        
        videoPlayer.classList.remove('d-none');
        contentList.style.height = 'calc(100% - 200px)';
    }
    
    loadArabicText(arabicText) {
        const noContent = document.getElementById('no-content');
        const recitationContent = document.getElementById('recitation-content');
        const arabicTextEl = document.getElementById('arabic-text');
        
        // Split Arabic text into words
        this.arabicWords = arabicText.split(' ').filter(word => word.trim());
        
        // Create spans for each word
        const wordSpans = this.arabicWords.map((word, index) => 
            `<span class="word" data-index="${index}">${word}</span>`
        ).join(' ');
        
        arabicTextEl.innerHTML = wordSpans;
        
        noContent.classList.add('d-none');
        recitationContent.classList.remove('d-none');
        
        this.updateWordProgress();
    }
    
    stopVideo() {
        const videoPlayer = document.getElementById('video-player');
        const contentList = document.getElementById('content-list');
        const youtubePlayer = document.getElementById('youtube-player');
        
        youtubePlayer.src = '';
        videoPlayer.classList.add('d-none');
        contentList.style.height = '';
        
        this.stopReciting();
    }
    
    initSpeechRecognition() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('Speech recognition not supported');
            return;
        }
        
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = 'ar-SA';
        
        this.recognition.onstart = () => {
            this.updateRecognitionStatus('Listening...', 'text-success');
        };
        
        this.recognition.onresult = (event) => {
            let finalTranscript = '';
            
            for (let i = event.resultIndex; i < event.results.length; i++) {
                if (event.results[i].isFinal) {
                    finalTranscript += event.results[i][0].transcript;
                }
            }
            
            if (finalTranscript) {
                this.processSpokenWord(finalTranscript.trim());
            }
        };
        
        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.updateRecognitionStatus('Recognition error: ' + event.error, 'text-danger');
        };
        
        this.recognition.onend = () => {
            if (this.isReciting) {
                this.recognition.start(); // Restart if still reciting
            } else {
                this.updateRecognitionStatus('Recognition stopped', 'text-muted');
            }
        };
    }
    
    async beginReciting() {
        if (!this.currentContent) {
            this.showAlert('warning', 'Please select a Surah first');
            return;
        }
        
        this.isReciting = true;
        
        // Start screen recording
        await this.startScreenRecording();
        
        // Start speech recognition
        if (this.recognition) {
            this.recognition.start();
        }
        
        // Update UI
        document.getElementById('start-reciting').classList.add('d-none');
        document.getElementById('stop-reciting').classList.remove('d-none');
        
        this.updateRecognitionStatus('Starting recitation...', 'text-primary');
        
        // Highlight first word
        this.highlightCurrentWord();
    }
    
    async startScreenRecording() {
        try {
            this.recordingStream = await navigator.mediaDevices.getDisplayMedia({
                video: { mediaSource: 'screen' },
                audio: true
            });
            
            this.mediaRecorder = new MediaRecorder(this.recordingStream);
            this.recordedChunks = [];
            
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };
            
            this.mediaRecorder.start();
            console.log('Screen recording started');
            
        } catch (error) {
            console.error('Error starting screen recording:', error);
            this.showAlert('warning', 'Screen recording not available, but you can continue reciting');
        }
    }
    
    stopReciting() {
        this.isReciting = false;
        
        // Stop speech recognition
        if (this.recognition) {
            this.recognition.stop();
        }
        
        // Stop screen recording
        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            this.mediaRecorder.stop();
        }
        
        if (this.recordingStream) {
            this.recordingStream.getTracks().forEach(track => track.stop());
        }
        
        // Update UI
        document.getElementById('start-reciting').classList.remove('d-none');
        document.getElementById('stop-reciting').classList.add('d-none');
        
        // Save recitation results
        this.saveRecitationResults();
    }
    
    processSpokenWord(spokenText) {
        if (!this.isReciting || this.currentWordIndex >= this.arabicWords.length) {
            return;
        }
        
        const currentWord = this.arabicWords[this.currentWordIndex];
        const words = spokenText.split(' ');
        
        // Simple word matching (can be improved with better Arabic NLP)
        let matched = false;
        for (const word of words) {
            if (this.isWordMatch(word, currentWord)) {
                matched = true;
                break;
            }
        }
        
        if (matched) {
            this.handleCorrectWord();
        } else {
            this.handleIncorrectWord();
        }
    }
    
    isWordMatch(spoken, expected) {
        // Simple matching - in a real app, you'd use more sophisticated Arabic text processing
        const normalizeArabic = (text) => {
            return text
                .replace(/[ًٌٍَُِْ]/g, '') // Remove diacritics
                .replace(/ة/g, 'ه') // Normalize taa marbuta
                .trim();
        };
        
        return normalizeArabic(spoken) === normalizeArabic(expected);
    }
    
    handleCorrectWord() {
        // Highlight word in blue
        const wordEl = document.querySelector(`[data-index="${this.currentWordIndex}"]`);
        if (wordEl) {
            wordEl.classList.add('correct');
            wordEl.classList.remove('current', 'error');
        }
        
        // Move to next word
        this.currentWordIndex++;
        this.updateWordProgress();
        
        // Reset error attempts for this word
        delete this.errorAttempts[this.currentWordIndex - 1];
        
        if (this.currentWordIndex >= this.arabicWords.length) {
            // Recitation completed
            this.completeRecitation();
        } else {
            this.highlightCurrentWord();
        }
    }
    
    handleIncorrectWord() {
        const wordIndex = this.currentWordIndex;
        
        if (!this.errorAttempts[wordIndex]) {
            this.errorAttempts[wordIndex] = 0;
        }
        
        this.errorAttempts[wordIndex]++;
        
        // Deduct marks based on attempt
        let deduction = this.errorAttempts[wordIndex];
        this.score = Math.max(0, this.score - deduction);
        
        this.updateScoreDisplay();
        
        if (this.errorAttempts[wordIndex] >= 3) {
            // Mark word as error and move on
            const wordEl = document.querySelector(`[data-index="${wordIndex}"]`);
            if (wordEl) {
                wordEl.classList.add('error');
                wordEl.classList.remove('current');
            }
            
            this.currentWordIndex++;
            this.updateWordProgress();
            
            if (this.currentWordIndex >= this.arabicWords.length) {
                this.completeRecitation();
            } else {
                this.highlightCurrentWord();
            }
        }
    }
    
    highlightCurrentWord() {
        // Remove previous highlighting
        document.querySelectorAll('.word.current').forEach(el => {
            el.classList.remove('current');
        });
        
        // Highlight current word
        const wordEl = document.querySelector(`[data-index="${this.currentWordIndex}"]`);
        if (wordEl) {
            wordEl.classList.add('current');
            wordEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
    
    updateWordProgress() {
        const progressEl = document.getElementById('word-progress');
        if (progressEl) {
            progressEl.textContent = `Word: ${this.currentWordIndex}/${this.arabicWords.length}`;
        }
    }
    
    updateScoreDisplay() {
        const scoreEl = document.getElementById('score-display');
        if (scoreEl) {
            scoreEl.textContent = `Score: ${this.score}`;
        }
    }
    
    updateRecognitionStatus(message, className) {
        const statusEl = document.getElementById('recognition-status');
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = className;
        }
    }
    
    resetRecitation() {
        this.currentWordIndex = 0;
        this.score = 100;
        this.errorAttempts = {};
        
        // Reset UI
        document.querySelectorAll('.word').forEach(word => {
            word.classList.remove('correct', 'error', 'current');
        });
        
        this.updateScoreDisplay();
        this.updateWordProgress();
        this.highlightCurrentWord();
    }
    
    async completeRecitation() {
        this.stopReciting();
        
        const accuracy = (this.score / 100) * 100;
        const pointsEarned = this.score >= 70 ? 1 : 0; // Award 1 point if score >= 70%
        
        this.showAlert('success', `Recitation completed! Score: ${this.score}% (${pointsEarned} point${pointsEarned !== 1 ? 's' : ''} earned)`);
        
        // Update user points if earned
        if (pointsEarned > 0) {
            this.updatePointsDisplay(pointsEarned);
        }
    }
    
    async saveRecording() {
        if (!this.recordedChunks || this.recordedChunks.length === 0) {
            return;
        }
        
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const formData = new FormData();
        
        formData.append('recording', blob, `recitation_${Date.now()}.webm`);
        formData.append('content_id', this.currentContent.id);
        formData.append('score', this.score);
        formData.append('csrf_token', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
        
        try {
            const response = await fetch('api/save_recording.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('Recording saved successfully');
            } else {
                console.error('Failed to save recording:', result.message);
            }
        } catch (error) {
            console.error('Error saving recording:', error);
        }
    }
    
    async saveRecitationResults() {
        try {
            const response = await fetch('api/save_recitation.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content_id: this.currentContent.id,
                    score: this.score,
                    words_correct: this.currentWordIndex - Object.keys(this.errorAttempts).length,
                    words_total: this.arabicWords.length,
                    accuracy_percentage: (this.score / 100) * 100,
                    csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Update rankings display
                this.updateRankingsDisplay(result.rankings);
            }
        } catch (error) {
            console.error('Error saving recitation results:', error);
        }
    }
    
    setupCamera() {
        // Camera will be activated on demand
    }
    
    async enableCamera() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
                video: true, 
                audio: false 
            });
            
            const video = document.getElementById('selfie-video');
            const placeholder = document.getElementById('camera-placeholder');
            
            video.srcObject = stream;
            video.classList.remove('d-none');
            placeholder.classList.add('d-none');
            
            this.camera = stream;
        } catch (error) {
            console.error('Error accessing camera:', error);
            this.showAlert('warning', 'Camera access denied or not available');
        }
    }
    
    updateWalletDisplay(newBalance) {
        const walletEl = document.querySelector('.fw-bold');
        if (walletEl && walletEl.textContent.includes('₦')) {
            walletEl.textContent = `₦${parseFloat(newBalance).toLocaleString('en-NG', { minimumFractionDigits: 2 })}`;
        }
    }
    
    updatePointsDisplay(pointsEarned) {
        const pointsEl = document.querySelectorAll('.fw-bold')[1];
        if (pointsEl) {
            const currentPoints = parseInt(pointsEl.textContent.replace(/,/g, ''));
            const newPoints = currentPoints + pointsEarned;
            pointsEl.textContent = newPoints.toLocaleString();
        }
    }
    
    updateRankingsDisplay(rankings) {
        if (rankings) {
            document.querySelector('.text-primary').textContent = rankings.ward_rank || 'Not ranked';
            document.querySelector('.text-success').textContent = rankings.lga_rank || 'Not ranked';
            document.querySelector('.text-warning').textContent = rankings.state_rank || 'Not ranked';
            document.querySelector('.text-danger').textContent = rankings.country_rank || 'Not ranked';
        }
    }
    
    showAlert(type, message) {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new RecitationDashboard();
});

// Add CSS for word highlighting
const style = document.createElement('style');
style.textContent = `
    .word {
        padding: 2px 4px;
        margin: 0 2px;
        border-radius: 4px;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .word.current {
        background-color: #fff3cd;
        border: 2px solid #ffc107;
        animation: pulse 1s infinite;
    }
    
    .word.correct {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #b6d7ff;
    }
    
    .word.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .arabic-text {
        font-family: 'Amiri', 'Traditional Arabic', serif;
        direction: rtl;
        text-align: right;
    }
`;
document.head.appendChild(style);
