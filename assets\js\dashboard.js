// Dashboard JavaScript Functions
let currentStream = null;
let mediaRecorder = null;
let recordedChunks = [];

// Start Camera
function startCamera() {
    navigator.mediaDevices.getUserMedia({ video: true, audio: true })
        .then(function(stream) {
            currentStream = stream;
            const video = document.getElementById('selfie-video');
            const placeholder = document.getElementById('camera-placeholder');
            
            video.srcObject = stream;
            video.play();
            video.style.display = 'block';
            placeholder.style.display = 'none';
            
            document.getElementById('start-camera-btn').style.display = 'none';
            document.getElementById('stop-camera-btn').style.display = 'block';
            document.getElementById('start-record-btn').style.display = 'block';
        })
        .catch(function(error) {
            alert('Camera access denied or not available');
            console.error('Camera error:', error);
        });
}

// Stop Camera
function stopCamera() {
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }
    
    const video = document.getElementById('selfie-video');
    const placeholder = document.getElementById('camera-placeholder');
    
    video.style.display = 'none';
    placeholder.style.display = 'block';
    
    document.getElementById('start-camera-btn').style.display = 'block';
    document.getElementById('stop-camera-btn').style.display = 'none';
    document.getElementById('start-record-btn').style.display = 'none';
    document.getElementById('stop-record-btn').style.display = 'none';
}

// Start Recording
function startRecording() {
    if (currentStream) {
        recordedChunks = [];
        mediaRecorder = new MediaRecorder(currentStream);
        
        mediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
                recordedChunks.push(event.data);
            }
        };
        
        mediaRecorder.onstop = function() {
            const blob = new Blob(recordedChunks, { type: 'video/mp4' });
            const filename = 'recitation-' + new Date().getTime() + '.mp4';

            // Save to database
            saveRecordingToDatabase(blob, filename);

            // Note: Removed auto-download as per requirements
        };
        
        mediaRecorder.start();
        
        document.getElementById('start-record-btn').style.display = 'none';
        document.getElementById('stop-record-btn').style.display = 'block';
    }
}

// Stop Recording
function stopRecording() {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
        
        document.getElementById('start-record-btn').style.display = 'block';
        document.getElementById('stop-record-btn').style.display = 'none';
    }
}

// Save recording to database
function saveRecordingToDatabase(blob, filename) {
    const formData = new FormData();
    formData.append('recording', blob, filename);
    formData.append('action', 'save_recording');
    
    fetch('save_recording.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.text();
    })
    .then(text => {
        try {
            const data = JSON.parse(text);
            if (data.success) {
                alert('Recording saved successfully!');
                window.location.reload();
            } else {
                alert('Failed to save recording: ' + data.message);
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            console.error('Response text:', text);
            alert('Error saving recording: Invalid server response');
        }
    })
    .catch(error => {
        console.error('Error saving recording:', error);
        alert('Error saving recording: ' + error.message);
    });
}

// Toggle balance visibility
let balanceVisible = true;
function toggleBalance() {
    const balanceDisplay = document.getElementById('balance-display');
    const eyeIcon = document.getElementById('balance-eye');
    
    if (balanceVisible) {
        balanceDisplay.textContent = '****';
        eyeIcon.className = 'fas fa-eye-slash';
        balanceVisible = false;
    } else {
        const balance = balanceDisplay.getAttribute('data-balance');
        balanceDisplay.textContent = balance;
        eyeIcon.className = 'fas fa-eye';
        balanceVisible = true;
    }
}

// Show notifications
function showNotifications() {
    alert('Notifications:\n• New Surah unlocked!\n• Recording saved successfully\n• Welcome to RECITE!');
}
