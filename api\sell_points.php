<?php
/**
 * Sell Points API for Recite! App
 * Allows users to sell points for wallet balance
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$points = intval($input['points'] ?? 0);
$expectedAmount = floatval($input['amount'] ?? 0);

if ($points < 50) {
    echo json_encode(['success' => false, 'message' => 'Minimum 50 points required to sell']);
    exit;
}

// Calculate actual earning (₦50 per point)
$actualEarning = $points * 50;

if (abs($expectedAmount - $actualEarning) > 0.01) {
    echo json_encode(['success' => false, 'message' => 'Amount mismatch']);
    exit;
}

try {
    $conn = getConnection();
    
    // Get user's current points balance
    $userResult = executeQuery(
        "SELECT points_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    
    $user = $userResult->fetch_assoc();
    $currentPoints = intval($user['points_balance']);
    
    if ($currentPoints < $points) {
        echo json_encode([
            'success' => false, 
            'message' => 'Insufficient points balance. You have ' . number_format($currentPoints) . ' points.'
        ]);
        exit;
    }
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Deduct points
        executeQuery(
            "UPDATE users SET points_balance = points_balance - ? WHERE id = ?",
            'ii',
            [$points, $userId]
        );
        
        // Add to wallet
        executeQuery(
            "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
            'di',
            [$actualEarning, $userId]
        );
        
        // Log points transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'sale', ?, ?, 'completed')",
            'iis',
            [$userId, -$points, "Sold {$points} points for ₦{$actualEarning}"]
        );
        
        // Log wallet transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'sale', ?, ?, 'completed')",
            'ids',
            [$userId, $actualEarning, "Sold {$points} points"]
        );
        
        // Commit transaction
        $conn->commit();
        $conn->autocommit(true);
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully sold {$points} points for ₦" . number_format($actualEarning, 2),
            'points_sold' => $points,
            'amount_earned' => $actualEarning
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Sell points error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to sell points']);
}
?> 