<?php
$page_title = 'My Profile';
require_once __DIR__ . '/../components/user_header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = $_POST['full_name'] ?? $user['full_name'] ?? '';
    $email = $_POST['email'] ?? $user['email'] ?? '';
    $phone = sanitize($_POST['phone'] ?? '');
    $country = sanitize($_POST['country'] ?? '');
    $state = sanitize($_POST['state'] ?? '');
    $ward = sanitize($_POST['ward'] ?? '');
    $lgea = sanitize($_POST['lgea'] ?? '');
    
    try {
        $conn = getConnection();
        
        // Handle profile picture upload
        $profile_pic = $user['profile_picture'] ?? '';
        if (isset($_FILES['profile_pic']) && $_FILES['profile_pic']['error'] == 0) {
            $upload_dir = __DIR__ . '/../uploads/profiles/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            $filename = uniqid() . '-' . basename($_FILES['profile_pic']['name']);
            if (move_uploaded_file($_FILES['profile_pic']['tmp_name'], $upload_dir . $filename)) {
                $profile_pic = $filename;
            }
        }
        
        // Update user information
        $stmt = $conn->prepare("
            UPDATE users 
            SET full_name = ?, email = ?, phone = ?, country = ?, state = ?, ward = ?, lgea = ?, profile_picture = ?
            WHERE id = ?
        ");
        $stmt->bind_param("ssssssssi", $full_name, $email, $phone, $country, $state, $ward, $lgea, $profile_pic, $userId);
        
        if ($stmt->execute()) {
            $message = 'Profile updated successfully!';
            
            // Update rankings if location changed
            if (function_exists('updateUserRanking')) {
                updateUserRanking($userId);
            }
        } else {
            $error = 'Failed to update profile.';
        }
        
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log("Profile update error: " . $e->getMessage());
    }
}

// Get user information
$user = getUserById($userId);
if (!$user) {
    header('Location: ../login.php');
    exit;
}

// Get user statistics
try {
    $conn = getConnection();
    
    // Get recitation stats
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_recitations,
            AVG(accuracy_percentage) as avg_accuracy,
            MAX(accuracy_percentage) as best_accuracy,
            SUM(points_earned) as total_points_earned
        FROM recitations 
        WHERE user_id = ? AND is_completed = 1
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $recitationStats = $stmt->get_result()->fetch_assoc() ?? [];
    $stmt->close();
    
    // Get ranking info
    $stmt = $conn->prepare("
        SELECT ward_rank, lgea_rank, state_rank, country_rank, total_score
        FROM rankings 
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $rankingResult = $stmt->get_result();
    $rankings = $rankingResult->fetch_assoc() ?? [];
    $stmt->close();
    
    // Get referral stats
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_referrals, SUM(points_earned) as referral_points
        FROM referrals 
        WHERE referrer_id = ? AND status = 'completed'
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $referralStats = $stmt->get_result()->fetch_assoc() ?? [];
    $stmt->close();
    
    $conn->close();
} catch (Exception $e) {
    error_log("Profile stats error: " . $e->getMessage());
    $recitationStats = ['total_recitations' => 0, 'avg_accuracy' => 0, 'best_accuracy' => 0, 'total_points_earned' => 0];
    $rankings = ['ward_rank' => 0, 'lgea_rank' => 0, 'state_rank' => 0, 'country_rank' => 0, 'total_score' => 0];
    $referralStats = ['total_referrals' => 0, 'referral_points' => 0];
}
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-edit me-2"></i>Edit Profile
                </h3>
            </div>
            <div class="card-body">
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <form action="profile.php" method="POST" enctype="multipart/form-data">
                    <div class="text-center mb-4">
                        <img src="<?php echo htmlspecialchars(($user['profile_picture'] ?? '') ? '/uploads/profiles/' . $user['profile_picture'] : '/assets/images/default-avatar.png'); ?>" 
                             alt="Profile Picture" class="rounded-circle" style="width: 128px; height: 128px; object-fit: cover; border: 4px solid var(--primary-color);">
                        <div class="mt-3">
                            <label for="profile_pic" class="btn btn-sm btn-outline-primary">Change Picture</label>
                            <input type="file" name="profile_pic" id="profile_pic" class="d-none">
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Username" value="<?php echo htmlspecialchars($user['username'] ?? $user['full_name'] ?? 'User'); ?>" disabled>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" placeholder="Full Name" value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>">
                    </div>

                    <div class="form-group mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Email Address" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                    </div>

                    <div class="form-group mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="Phone Number" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                    </div>

                    <div class="form-group mb-3">
                        <label for="country" class="form-label">Country</label>
                        <input type="text" class="form-control" id="country" name="country" placeholder="Country" value="<?php echo htmlspecialchars($user['country'] ?? ''); ?>">
                    </div>

                    <div class="form-group mb-3">
                        <label for="state" class="form-label">State</label>
                        <input type="text" class="form-control" id="state" name="state" placeholder="State" value="<?php echo htmlspecialchars($user['state'] ?? ''); ?>">
                    </div>

                    <div class="form-group mb-3">
                        <label for="ward" class="form-label">Ward</label>
                        <input type="text" class="form-control" id="ward" name="ward" placeholder="Ward" value="<?php echo htmlspecialchars($user['ward'] ?? ''); ?>">
                    </div>

                    <div class="form-group mb-3">
                        <label for="lgea" class="form-label">LGEA</label>
                        <input type="text" class="form-control" id="lgea" name="lgea" placeholder="LGEA" value="<?php echo htmlspecialchars($user['lgea'] ?? ''); ?>">
                    </div>

                    <button type="submit" class="btn btn-primary w-100 btn-lg">Save Changes</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>