<?php
/**
 * Points System Functions for RECITE App
 * Handles all point earning, tracking, and leaderboard functionality
 */

/**
 * Award points to a user
 */
function awardPoints($userId, $points, $transactionType, $description = '', $referenceId = null, $referenceType = null) {
    $conn = getConnection();
    
    try {
        $conn->begin_transaction();
        
        // Insert point transaction
        $transactionStmt = $conn->prepare("
            INSERT INTO point_transactions (user_id, points, transaction_type, description, reference_id, reference_type) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $transactionStmt->bind_param("iissss", $userId, $points, $transactionType, $description, $referenceId, $referenceType);
        $transactionStmt->execute();
        
        // Update user points
        $updateStmt = $conn->prepare("
            INSERT INTO user_points (user_id, total_points, updated_at) 
            VALUES (?, ?, NOW()) 
            ON DUPLICATE KEY UPDATE 
            total_points = total_points + ?, 
            updated_at = NOW()
        ");
        $updateStmt->bind_param("iii", $userId, $points, $points);
        $updateStmt->execute();
        
        // Also update users table for quick access
        $userUpdateStmt = $conn->prepare("UPDATE users SET total_points = COALESCE(total_points, 0) + ? WHERE id = ?");
        $userUpdateStmt->bind_param("ii", $points, $userId);
        $userUpdateStmt->execute();
        
        $conn->commit();
        return true;
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Points award error: " . $e->getMessage());
        return false;
    }
}

/**
 * Process recitation completion and award points
 */
function processRecitationCompletion($userId, $recordingId, $recordingType, $durationSeconds, $accuracyScore = 100) {
    $conn = getConnection();
    
    try {
        $conn->begin_transaction();
        
        // Calculate points based on duration and accuracy
        $basePoints = 1; // Base point for completion
        $durationBonus = min(5, floor($durationSeconds / 60)); // 1 point per minute, max 5
        $accuracyBonus = ($accuracyScore >= 90) ? 2 : (($accuracyScore >= 70) ? 1 : 0);
        
        $totalPoints = $basePoints + $durationBonus + $accuracyBonus;
        
        // Insert evaluation record
        $evalStmt = $conn->prepare("
            INSERT INTO recitation_evaluations 
            (user_id, recording_id, recording_type, accuracy_score, duration_seconds, points_earned, evaluation_status) 
            VALUES (?, ?, ?, ?, ?, ?, 'completed')
        ");
        $evalStmt->bind_param("iisiii", $userId, $recordingId, $recordingType, $accuracyScore, $durationSeconds, $totalPoints);
        $evalStmt->execute();
        
        // Award points
        $description = "Recitation completed - Duration: {$durationSeconds}s, Accuracy: {$accuracyScore}%";
        awardPoints($userId, $totalPoints, 'recitation_complete', $description, $recordingId, $recordingType);
        
        // Check for streak bonus
        checkAndAwardStreakBonus($userId);
        
        $conn->commit();
        return $totalPoints;
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Recitation completion error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Check and award streak bonus
 */
function checkAndAwardStreakBonus($userId) {
    $conn = getConnection();
    
    // Get user's recent recitations in last 72 hours
    $recentQuery = "
        SELECT COUNT(*) as count 
        FROM recitation_evaluations 
        WHERE user_id = ? 
        AND evaluation_status = 'completed' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
    ";
    $stmt = $conn->prepare($recentQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $recentCount = $stmt->get_result()->fetch_assoc()['count'];
    
    // Update streak
    $updateStreakStmt = $conn->prepare("
        UPDATE user_points 
        SET current_streak = ?, 
            best_streak = GREATEST(best_streak, ?),
            last_activity_date = CURDATE()
        WHERE user_id = ?
    ");
    $updateStreakStmt->bind_param("iii", $recentCount, $recentCount, $userId);
    $updateStreakStmt->execute();
    
    // Award streak bonus if reached 100 recitations
    if ($recentCount >= 100) {
        // Check if bonus already awarded in last 72 hours
        $bonusCheckQuery = "
            SELECT COUNT(*) as count 
            FROM point_transactions 
            WHERE user_id = ? 
            AND transaction_type = 'streak_bonus' 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
        ";
        $bonusStmt = $conn->prepare($bonusCheckQuery);
        $bonusStmt->bind_param("i", $userId);
        $bonusStmt->execute();
        $bonusExists = $bonusStmt->get_result()->fetch_assoc()['count'];
        
        if ($bonusExists == 0) {
            awardPoints($userId, 100, 'streak_bonus', "Streak bonus for 100 recitations in 72 hours");
            return true;
        }
    }
    
    return false;
}

/**
 * Process user interaction (like, comment, share)
 */
function processUserInteraction($userId, $targetUserId, $recordingId, $recordingType, $interactionType, $commentText = '') {
    $conn = getConnection();
    
    try {
        $conn->begin_transaction();
        
        // Points for different interactions
        $pointsMap = [
            'like' => 1,
            'comment' => 1,
            'share' => 1
        ];
        
        $points = $pointsMap[$interactionType] ?? 1;
        
        // Check if interaction already exists (except for comments which can be multiple)
        if ($interactionType !== 'comment') {
            $checkStmt = $conn->prepare("
                SELECT id FROM user_interactions
                WHERE user_id = ? AND recording_id = ? AND recording_type = ? AND interaction_type = ?
            ");
            $checkStmt->bind_param("iiss", $userId, $recordingId, $recordingType, $interactionType);
            $checkStmt->execute();
            $existing = $checkStmt->get_result()->fetch_assoc();

            if ($existing) {
                $conn->rollback();
                return false; // Already exists
            }
        }

        // Insert interaction record
        $interactionStmt = $conn->prepare("
            INSERT INTO user_interactions
            (user_id, target_user_id, recording_id, recording_type, interaction_type, comment_text, points_awarded)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $interactionStmt->bind_param("iiiissi", $userId, $targetUserId, $recordingId, $recordingType, $interactionType, $commentText, $points);

        if (!$interactionStmt->execute()) {
            error_log("Failed to insert interaction: " . $interactionStmt->error);
            $conn->rollback();
            return false;
        }

        // Award points to target user (video owner) - only if different user
        if ($targetUserId !== $userId) {
            $description = ucfirst($interactionType) . " received on recording";
            awardPoints($targetUserId, $points, $interactionType . '_received', $description, $recordingId, $recordingType);
        }
        
        $conn->commit();
        return true;
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log("User interaction error: " . $e->getMessage());
        return false;
    }
}

/**
 * Process referral signup
 */
function processReferralSignup($referrerId, $referredId, $referralCode) {
    $conn = getConnection();
    
    try {
        $conn->begin_transaction();
        
        // Insert referral record
        $referralStmt = $conn->prepare("
            INSERT INTO referrals (referrer_id, referred_id, referral_code, points_awarded) 
            VALUES (?, ?, ?, 1)
        ");
        $referralStmt->bind_param("iis", $referrerId, $referredId, $referralCode);
        $referralStmt->execute();
        
        // Award points to referrer
        awardPoints($referrerId, 1, 'referral', "Referral signup bonus", $referredId, 'user');
        
        $conn->commit();
        return true;
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Referral signup error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user's point summary
 */
function getUserPointsSummary($userId) {
    $conn = getConnection();
    
    $query = "
        SELECT 
            up.total_points,
            up.current_streak,
            up.best_streak,
            up.last_activity_date,
            (SELECT COUNT(*) FROM recitation_evaluations WHERE user_id = ? AND evaluation_status = 'completed') as total_recitations,
            (SELECT COUNT(*) FROM referrals WHERE referrer_id = ?) as total_referrals
        FROM user_points up 
        WHERE up.user_id = ?
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("iii", $userId, $userId, $userId);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    return $result ?: [
        'total_points' => 0,
        'current_streak' => 0,
        'best_streak' => 0,
        'last_activity_date' => null,
        'total_recitations' => 0,
        'total_referrals' => 0
    ];
}

/**
 * Get top performers for leaderboard
 */
function getTopPerformers($limit = 10, $period = 'all_time') {
    $conn = getConnection();
    
    $whereClause = '';
    if ($period === 'weekly') {
        $whereClause = "WHERE pt.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    } elseif ($period === 'monthly') {
        $whereClause = "WHERE pt.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
    }
    
    $query = "
        SELECT 
            u.id,
            u.full_name,
            u.profile_picture,
            COALESCE(SUM(pt.points), 0) as period_points,
            up.total_points,
            up.current_streak,
            (SELECT COUNT(*) FROM recitation_evaluations WHERE user_id = u.id AND evaluation_status = 'completed') as total_recitations
        FROM users u
        LEFT JOIN user_points up ON u.id = up.user_id
        LEFT JOIN point_transactions pt ON u.id = pt.user_id $whereClause
        GROUP BY u.id
        ORDER BY period_points DESC, up.total_points DESC
        LIMIT ?
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $performers = [];
    while ($row = $result->fetch_assoc()) {
        $performers[] = $row;
    }
    
    return $performers;
}

/**
 * Get user's ranking position
 */
function getUserRanking($userId) {
    $conn = getConnection();
    
    $query = "
        SELECT ranking.rank_position
        FROM (
            SELECT 
                u.id,
                ROW_NUMBER() OVER (ORDER BY up.total_points DESC) as rank_position
            FROM users u
            LEFT JOIN user_points up ON u.id = up.user_id
        ) ranking
        WHERE ranking.id = ?
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    return $result ? $result['rank_position'] : null;
}
?>
