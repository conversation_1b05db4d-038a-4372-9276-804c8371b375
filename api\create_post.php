<?php
/**
 * Create Post API for Recite! App
 * Handles creation of new posts with optional media upload
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];

// Validate CSRF token
if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$textContent = trim($_POST['text_content'] ?? '');
$hasMedia = isset($_FILES['media']) && $_FILES['media']['error'] === UPLOAD_ERR_OK;

// Validate input
if (empty($textContent) && !$hasMedia) {
    echo json_encode(['success' => false, 'message' => 'Please provide text content or upload media']);
    exit;
}

$uploadCost = $hasMedia ? 1000 : 0; // ₦1000 for media upload
$mediaPath = null;
$contentType = 'text';

try {
    $conn = getConnection();
    
    // Check wallet balance if media upload is required
    if ($uploadCost > 0) {
        $userResult = executeQuery(
            "SELECT wallet_balance FROM users WHERE id = ?",
            'i',
            [$userId]
        );
        
        $user = $userResult->fetch_assoc();
        $currentBalance = floatval($user['wallet_balance']);
        
        if ($currentBalance < $uploadCost) {
            echo json_encode([
                'success' => false, 
                'message' => 'Insufficient wallet balance. You need ₦1,000 to upload media.'
            ]);
            exit;
        }
    }
    
    // Handle media upload
    if ($hasMedia) {
        $uploadFile = $_FILES['media'];
        $uploadDir = '../uploads/posts/';
        
        // Create directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm', 'video/ogg'];
        $fileType = $uploadFile['type'];
        
        if (!in_array($fileType, $allowedTypes)) {
            echo json_encode(['success' => false, 'message' => 'Invalid file type. Only images and videos are allowed.']);
            exit;
        }
        
        // Validate file size (max 50MB)
        $maxSize = 50 * 1024 * 1024; // 50MB
        if ($uploadFile['size'] > $maxSize) {
            echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 50MB.']);
            exit;
        }
        
        // Generate unique filename
        $extension = pathinfo($uploadFile['name'], PATHINFO_EXTENSION);
        $fileName = 'post_' . $userId . '_' . time() . '_' . uniqid() . '.' . $extension;
        $filePath = $uploadDir . $fileName;
        
        // Move uploaded file
        if (!move_uploaded_file($uploadFile['tmp_name'], $filePath)) {
            throw new Exception('Failed to upload file');
        }
        
        $mediaPath = 'posts/' . $fileName;
        $contentType = strpos($fileType, 'video') === 0 ? 'video' : 'image';
    }
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Deduct upload cost from wallet
        if ($uploadCost > 0) {
            executeQuery(
                "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
                'di',
                [$uploadCost, $userId]
            );
            
            // Log transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'upload', ?, 'Media upload fee', 'completed')",
                'id',
                [$userId, -$uploadCost]
            );
        }
        
        // Create stream post
        executeQuery(
            "INSERT INTO streams (user_id, content_type, text_content, media_path) VALUES (?, ?, ?, ?)",
            'isss',
            [$userId, $contentType, $textContent, $mediaPath]
        );
        
        $postId = $conn->insert_id;
        
        // Commit transaction
        $conn->commit();
        $conn->autocommit(true);
        
        echo json_encode([
            'success' => true,
            'message' => 'Post created successfully!',
            'post_id' => $postId,
            'cost' => $uploadCost
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        
        // Delete uploaded file if database operation failed
        if ($mediaPath && file_exists('../uploads/' . $mediaPath)) {
            unlink('../uploads/' . $mediaPath);
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Create post error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to create post']);
}
?> 