<?php
session_start();
require_once __DIR__ . '/../config.php';

// Redirect to login if not authenticated as admin
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
    header('Location: login.php');
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];

// Create admin user array for display
$admin_user = [
    'id' => $admin_id,
    'username' => $admin_username
];

$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? 'Admin Dashboard'; ?> - Qur'an Recite App</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/modern-ui.css">
    <link rel="stylesheet" href="../assets/css/admin-panel.css">
</head>
<body>

<aside class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <div class="logo"><i class="fas fa-quran"></i></div>
        <h2>ReciteApp</h2>
    </div>
    <nav class="sidebar-nav">
        <a href="dashboard.php" class="nav-link <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>">
            <i class="fas fa-tachometer-alt"></i><span>Dashboard</span>
        </a>
        <a href="users.php" class="nav-link <?php echo ($current_page == 'users.php') ? 'active' : ''; ?>">
            <i class="fas fa-users"></i><span>Users</span>
        </a>
        <a href="videos.php" class="nav-link <?php echo ($current_page == 'videos.php') ? 'active' : ''; ?>">
            <i class="fas fa-video"></i><span>Videos</span>
        </a>
        <a href="manage-content.php" class="nav-link <?php echo ($current_page == 'manage-content.php') ? 'active' : ''; ?>">
            <i class="fas fa-edit"></i><span>Manage Content</span>
        </a>
        <a href="payouts.php" class="nav-link <?php echo ($current_page == 'payouts.php') ? 'active' : ''; ?>">
            <i class="fas fa-credit-card"></i><span>Payouts</span>
        </a>
        <a href="earnings.php" class="nav-link <?php echo ($current_page == 'earnings.php') ? 'active' : ''; ?>">
            <i class="fas fa-chart-line"></i><span>Earnings</span>
        </a>
        <a href="reports.php" class="nav-link <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>">
            <i class="fas fa-chart-pie"></i><span>Reports</span>
        </a>
    </nav>
    <div class="sidebar-footer">
        <a href="../logout.php" class="nav-link">
            <i class="fas fa-sign-out-alt"></i><span>Logout</span>
        </a>
    </div>
    <button class="sidebar-toggle" id="sidebarToggle"><i class="fas fa-chevron-left"></i></button>
</aside>

<div class="sidebar-overlay" id="sidebarOverlay"></div>

<main class="admin-main" id="adminMain">
    <header class="admin-header">
         <button class="mobile-menu-button" id="mobileMenuButton"><i class="fas fa-bars"></i></button>
        <h1><?php echo $page_title ?? 'Dashboard'; ?></h1>
        <div class="admin-user-menu">
             <div class="admin-user-avatar">
                <?php echo strtoupper(substr($admin_user['username'], 0, 1)); ?>
            </div>
            <span><?php echo htmlspecialchars($admin_user['username']); ?></span>
        </div>
    </header>
    
    <div class="dashboard-container"> 