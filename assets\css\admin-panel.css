/* Admin Panel Styles for ReciteApp */

:root {
    --admin-primary: #dc3545;
    --admin-secondary: #6c757d;
    --admin-success: #198754;
    --admin-info: #0dcaf0;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --admin-dark: #212529;
    --admin-light: #f8f9fa;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
}

/* Admin Body */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* Admin Sidebar */
.admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(135deg, var(--admin-primary), #b02a37);
    color: white;
    z-index: 1000;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.admin-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.admin-sidebar.collapsed .sidebar-header h2 {
    display: none;
}

.sidebar-header .logo {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
}

.sidebar-nav .nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.sidebar-nav .nav-link.active {
    background: rgba(255,255,255,0.2);
    color: white;
    border-right: 3px solid white;
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.admin-sidebar.collapsed .nav-link span {
    display: none;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* Sidebar Toggle */
.sidebar-toggle {
    position: absolute;
    top: 50%;
    right: -15px;
    width: 30px;
    height: 30px;
    background: var(--admin-primary);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transform: translateY(-50%);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #b02a37;
}

/* Main Content */
.admin-main {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: all 0.3s ease;
}

.admin-main.collapsed {
    margin-left: var(--sidebar-collapsed-width);
}

/* Admin Header */
.admin-header {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-header h1 {
    margin: 0;
    font-size: 1.75rem;
    color: var(--admin-dark);
    font-weight: 600;
}

.admin-user-menu {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.admin-user-avatar {
    width: 40px;
    height: 40px;
    background: var(--admin-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Dashboard Container */
.dashboard-container {
    padding: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
}

.stat-icon.primary { background: rgba(220, 53, 69, 0.1); color: var(--admin-primary); }
.stat-icon.success { background: rgba(25, 135, 84, 0.1); color: var(--admin-success); }
.stat-icon.warning { background: rgba(255, 193, 7, 0.1); color: var(--admin-warning); }
.stat-icon.danger { background: rgba(220, 53, 69, 0.1); color: var(--admin-danger); }

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--admin-dark);
}

.stat-label {
    margin: 0;
    color: var(--admin-secondary);
    font-size: 0.9rem;
}

/* Data Tables */
.data-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.table-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--admin-dark);
    border-bottom: 2px solid #dee2e6;
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.modern-table tr:hover {
    background: #f8f9fa;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--admin-primary);
    color: white;
}

.btn-primary:hover {
    background: #b02a37;
    color: white;
}

.btn-success {
    background: var(--admin-success);
    color: white;
}

.btn-danger {
    background: var(--admin-danger);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--admin-primary);
    border: 1px solid var(--admin-primary);
}

.btn-outline:hover {
    background: var(--admin-primary);
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

/* Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-success {
    background: var(--admin-success);
    color: white;
}

.badge-danger {
    background: var(--admin-danger);
    color: white;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.alert-success {
    background: rgba(25, 135, 84, 0.1);
    color: var(--admin-success);
    border: 1px solid rgba(25, 135, 84, 0.2);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--admin-danger);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-main.collapsed {
        margin-left: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
    
    .mobile-menu-button {
        display: block;
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--admin-dark);
        cursor: pointer;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 769px) {
    .mobile-menu-button {
        display: none;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-end { text-align: right; }
.mb-4 { margin-bottom: 1.5rem; }
.me-2 { margin-right: 0.5rem; }
.me-3 { margin-right: 1rem; }
.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.fw-semibold { font-weight: 600; }
.text-muted { color: var(--admin-secondary); }
.rounded-circle { border-radius: 50%; }
.img-thumbnail { border: 1px solid #dee2e6; border-radius: 5px; }

/* Row and Column Grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 0.75rem;
}

@media (max-width: 992px) {
    .col-lg-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
} 