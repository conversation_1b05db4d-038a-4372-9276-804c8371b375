<?php
require_once '../config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'User not authenticated']);
    exit;
}

try {
    $conn = getConnection();
    
    // Get request parameters
    $limit = intval($_GET['limit'] ?? 10);
    $offset = intval($_GET['offset'] ?? 0);
    $category = sanitize($_GET['category'] ?? '');
    $reciter = sanitize($_GET['reciter'] ?? '');
    $search = sanitize($_GET['search'] ?? '');
    
    // Build WHERE clause
    $whereConditions = ["v.is_active = 1"];
    $params = [];
    $types = "";
    
    if (!empty($category)) {
        $whereConditions[] = "v.category = ?";
        $params[] = $category;
        $types .= "s";
    }
    
    if (!empty($reciter)) {
        $whereConditions[] = "v.reciter = ?";
        $params[] = $reciter;
        $types .= "s";
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(v.title LIKE ? OR v.description LIKE ? OR v.reciter LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $types .= "sss";
    }
    
    $whereClause = implode(" AND ", $whereConditions);
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM videos v WHERE $whereClause";
    $stmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $totalResult = $stmt->get_result();
    $total = $totalResult->fetch_assoc()['total'];
    $stmt->close();
    
    // Get videos with pagination
    $sql = "
        SELECT v.id, v.title, v.youtube_url, v.youtube_id, v.category, v.reciter, 
               v.description, v.created_at,
               COUNT(r.id) as recitation_count,
               AVG(r.accuracy_percentage) as avg_accuracy
        FROM videos v
        LEFT JOIN recitations r ON v.id = r.video_id AND r.is_completed = 1
        WHERE $whereClause
        GROUP BY v.id
        ORDER BY v.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    // Add limit and offset to params
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $videos = [];
    while ($row = $result->fetch_assoc()) {
        // Extract YouTube video ID if not stored
        if (empty($row['youtube_id']) && !empty($row['youtube_url'])) {
            $row['youtube_id'] = extractYouTubeId($row['youtube_url']);
        }
        
        // Generate thumbnail URL
        $row['thumbnail_url'] = !empty($row['youtube_id']) 
            ? "https://img.youtube.com/vi/{$row['youtube_id']}/maxresdefault.jpg"
            : null;
            
        // Format numbers
        $row['recitation_count'] = intval($row['recitation_count']);
        $row['avg_accuracy'] = round(floatval($row['avg_accuracy']), 2);
        
        $videos[] = $row;
    }
    
    $stmt->close();
    
    // Get available categories and reciters for filtering
    $categoriesStmt = $conn->prepare("SELECT DISTINCT category FROM videos WHERE is_active = 1 AND category IS NOT NULL ORDER BY category");
    $categoriesStmt->execute();
    $categoriesResult = $categoriesStmt->get_result();
    $categories = [];
    while ($row = $categoriesResult->fetch_assoc()) {
        $categories[] = $row['category'];
    }
    $categoriesStmt->close();
    
    $recitersStmt = $conn->prepare("SELECT DISTINCT reciter FROM videos WHERE is_active = 1 AND reciter IS NOT NULL ORDER BY reciter");
    $recitersStmt->execute();
    $recitersResult = $recitersStmt->get_result();
    $reciters = [];
    while ($row = $recitersResult->fetch_assoc()) {
        $reciters[] = $row['reciter'];
    }
    $recitersStmt->close();
    
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'videos' => $videos,
        'pagination' => [
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total
        ],
        'filters' => [
            'categories' => $categories,
            'reciters' => $reciters
        ]
    ]);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->close();
    }
    
    logError("Fetch videos error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Function to extract YouTube video ID from URL
function extractYouTubeId($url) {
    $patterns = [
        '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/v\/([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/user\/[^\/]+#.*\/([a-zA-Z0-9_-]+)/'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?> 