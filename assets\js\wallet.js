 /**
 * Wallet JavaScript for Recite! App
 * Handles wallet funding, points buying/selling, and withdrawals
 */

class WalletManager {
    constructor() {
        this.paystackKey = 'pk_test_79ab46bc7ad4029705e3593e00dd2feb7c7cdb77';
        this.init();
    }
    
    init() {
        // Any initialization code if needed
    }
    
    showAlert(type, message) {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
    
    async makeApiCall(endpoint, data) {
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ...data,
                    csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                })
            });
            
            return await response.json();
        } catch (error) {
            console.error('API call error:', error);
            throw new Error('Network error occurred');
        }
    }
}

const walletManager = new WalletManager();

/**
 * Fund wallet using Paystack
 */
function fundWallet() {
    const amount = prompt('Enter amount to fund (₦):');
    if (!amount || isNaN(amount) || parseFloat(amount) < 100) {
        walletManager.showAlert('warning', 'Please enter a valid amount (minimum ₦100)');
        return;
    }
    
    const amountInKobo = parseFloat(amount) * 100;
    
    // Get user email for Paystack
    const userEmail = getUserEmail();
    
    const handler = PaystackPop.setup({
        key: walletManager.paystackKey,
        email: userEmail,
        amount: amountInKobo,
        currency: 'NGN',
        ref: 'fund_' + Math.floor((Math.random() * 1000000000) + 1),
        metadata: {
            action: 'fund_wallet',
            user_id: getUserId()
        },
        callback: function(response) {
            // Payment successful
            verifyPayment(response.reference, 'fund_wallet', parseFloat(amount));
        },
        onClose: function() {
            walletManager.showAlert('info', 'Payment cancelled');
        }
    });
    
    handler.openIframe();
}

/**
 * Buy points with wallet balance
 */
async function buyPoints() {
    const points = prompt('How many points would you like to buy? (₦70 per point)');
    if (!points || isNaN(points) || parseInt(points) < 1) {
        walletManager.showAlert('warning', 'Please enter a valid number of points');
        return;
    }
    
    const pointCount = parseInt(points);
    const totalCost = pointCount * 70;
    
    const confirmed = confirm(`Buy ${pointCount} points for ₦${totalCost.toLocaleString()}?`);
    if (!confirmed) return;
    
    try {
        const result = await walletManager.makeApiCall('api/buy_points.php', {
            points: pointCount,
            amount: totalCost
        });
        
        if (result.success) {
            walletManager.showAlert('success', result.message);
            
            // Update display
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            walletManager.showAlert('danger', result.message);
        }
    } catch (error) {
        walletManager.showAlert('danger', error.message);
    }
}

/**
 * Sell points for wallet balance
 */
async function sellPoints() {
    const points = prompt('How many points would you like to sell? (₦50 per point, minimum 50 points)');
    if (!points || isNaN(points) || parseInt(points) < 50) {
        walletManager.showAlert('warning', 'Please enter a valid number of points (minimum 50)');
        return;
    }
    
    const pointCount = parseInt(points);
    const totalEarning = pointCount * 50;
    
    const confirmed = confirm(`Sell ${pointCount} points for ₦${totalEarning.toLocaleString()}?`);
    if (!confirmed) return;
    
    try {
        const result = await walletManager.makeApiCall('api/sell_points.php', {
            points: pointCount,
            amount: totalEarning
        });
        
        if (result.success) {
            walletManager.showAlert('success', result.message);
            
            // Update display
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            walletManager.showAlert('danger', result.message);
        }
    } catch (error) {
        walletManager.showAlert('danger', error.message);
    }
}

/**
 * Withdraw mature points
 */
async function withdrawPoints() {
    const points = prompt('How many points would you like to withdraw? (₦100 per point, only points older than 30 days)');
    if (!points || isNaN(points) || parseInt(points) < 1) {
        walletManager.showAlert('warning', 'Please enter a valid number of points');
        return;
    }
    
    const pointCount = parseInt(points);
    const totalWithdrawal = pointCount * 100;
    
    const confirmed = confirm(`Withdraw ${pointCount} points for ₦${totalWithdrawal.toLocaleString()}? This will be processed manually.`);
    if (!confirmed) return;
    
    try {
        const result = await walletManager.makeApiCall('api/withdraw_points.php', {
            points: pointCount,
            amount: totalWithdrawal
        });
        
        if (result.success) {
            walletManager.showAlert('success', result.message);
            
            // Update display
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            walletManager.showAlert('danger', result.message);
        }
    } catch (error) {
        walletManager.showAlert('danger', error.message);
    }
}

/**
 * Verify payment with backend
 */
async function verifyPayment(reference, action, amount) {
    try {
        const result = await walletManager.makeApiCall('api/verify_wallet_payment.php', {
            reference: reference,
            action: action,
            amount: amount
        });
        
        if (result.success) {
            walletManager.showAlert('success', 'Payment verified successfully! Your wallet has been funded.');
            
            // Update display
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            walletManager.showAlert('danger', 'Payment verification failed: ' + result.message);
        }
    } catch (error) {
        walletManager.showAlert('danger', 'Payment verification error: ' + error.message);
    }
}

/**
 * Get user email from page context
 */
function getUserEmail() {
    // You can extract this from the page or make an API call
    // For now, we'll prompt the user
    return prompt('Enter your email address:') || '<EMAIL>';
}

/**
 * Get user ID from session
 */
function getUserId() {
    // This would typically be available from the page context
    // For now, we'll return a placeholder
    return 1;
}

// Initialize wallet manager when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Any initialization code
    console.log('Wallet manager initialized');
});