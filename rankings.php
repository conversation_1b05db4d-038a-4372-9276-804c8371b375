<?php
$page_title = 'Rankings';
require_once 'components/user_header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Get filter parameters
$filter_type = $_GET['filter'] ?? 'country';
$filter_value = $_GET['value'] ?? '';

// Sample data for now - in real app, this would come from database
$rankings = [
    ['id' => 1, 'user_id' => 1, 'name' => '<PERSON>', 'location' => 'Kano, Nigeria', 'score' => 2450, 'accuracy' => 98.5, 'recitations' => 125, 'is_current_user' => false],
    ['id' => 2, 'user_id' => 2, 'name' => 'Fatima Zahra', 'location' => 'Lagos, Nigeria', 'score' => 2380, 'accuracy' => 97.8, 'recitations' => 118, 'is_current_user' => false],
    ['id' => 3, 'user_id' => 3, 'name' => '<PERSON>', 'location' => 'Abuja, Nigeria', 'score' => 2320, 'accuracy' => 96.9, 'recitations' => 112, 'is_current_user' => false],
    ['id' => 4, 'user_id' => $userId, 'name' => $user['full_name'] ?? 'You', 'location' => ($user['state'] ?? 'State') . ', ' . ($user['country'] ?? 'Country'), 'score' => 2280, 'accuracy' => 96.2, 'recitations' => 108, 'is_current_user' => true],
    ['id' => 5, 'user_id' => 5, 'name' => 'Aisha Ibrahim', 'location' => 'Kaduna, Nigeria', 'score' => 2240, 'accuracy' => 95.7, 'recitations' => 105, 'is_current_user' => false],
    ['id' => 6, 'user_id' => 6, 'name' => 'Usman Aliyu', 'location' => 'Katsina, Nigeria', 'score' => 2180, 'accuracy' => 94.8, 'recitations' => 98, 'is_current_user' => false],
    ['id' => 7, 'user_id' => 7, 'name' => 'Zainab Ahmed', 'location' => 'Sokoto, Nigeria', 'score' => 2120, 'accuracy' => 93.9, 'recitations' => 92, 'is_current_user' => false],
    ['id' => 8, 'user_id' => 8, 'name' => 'Ibrahim Musa', 'location' => 'Bauchi, Nigeria', 'score' => 2080, 'accuracy' => 93.2, 'recitations' => 89, 'is_current_user' => false],
    ['id' => 9, 'user_id' => 9, 'name' => 'Halima Sani', 'location' => 'Gombe, Nigeria', 'score' => 2020, 'accuracy' => 92.5, 'recitations' => 85, 'is_current_user' => false],
    ['id' => 10, 'user_id' => 10, 'name' => 'Suleiman Garba', 'location' => 'Kebbi, Nigeria', 'score' => 1980, 'accuracy' => 91.8, 'recitations' => 82, 'is_current_user' => false],
];

// Get user's current rank
$currentUserRank = 0;
foreach ($rankings as $index => $ranking) {
    if ($ranking['is_current_user']) {
        $currentUserRank = $index + 1;
        break;
    }
}
?>

<style>
:root {
    --primary-color: #1a5f3f;
    --primary-light: #2d7a5a;
    --primary-hover: #164d34;
    --background: #FFFFFF;
    --surface: #F8F9FA;
    --text: #1E1E1E;
    --text-secondary: #666666;
    --border: #E0E0E0;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
}

body {
    background-color: var(--background);
    color: var(--text);
}

.rankings-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: center;
}

.rankings-header h2 {
    margin: 0 0 0.5rem 0;
    font-weight: 700;
}

.rankings-header p {
    margin: 0;
    opacity: 0.9;
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-tab {
    padding: 0.75rem 1.5rem;
    background: var(--surface);
    border: 2px solid var(--border);
    border-radius: 25px;
    text-decoration: none;
    color: var(--text);
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-tab:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.filter-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.rankings-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.rankings-table table {
    width: 100%;
    border-collapse: collapse;
}

.rankings-table th {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    font-weight: 600;
    text-align: left;
}

.rankings-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border);
}

.rankings-table tr:hover {
    background: var(--surface);
}

.rankings-table tr.current-user {
    background: rgba(177, 0, 32, 0.1);
    border-left: 4px solid var(--primary-color);
}

.rankings-table tr.current-user:hover {
    background: rgba(177, 0, 32, 0.15);
}

.rank-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    font-weight: 700;
    font-size: 0.9rem;
}

.rank-badge.top-3 {
    background: var(--primary-color);
    color: white;
}

.rank-badge.top-10 {
    background: var(--warning);
    color: white;
}

.rank-badge.other {
    background: var(--surface);
    color: var(--text);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info .name {
    font-weight: 600;
    color: var(--text);
}

.user-info .location {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.score-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.accuracy-badge {
    background: var(--success);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.recitations-count {
    color: var(--text-secondary);
    font-weight: 500;
}

.current-rank-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 2px solid var(--primary-color);
}

.current-rank-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.current-rank-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .rankings-table {
        font-size: 0.9rem;
    }
    
    .rankings-table th,
    .rankings-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .user-info {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .current-rank-number {
        font-size: 2rem;
    }
}
</style>

<div class="container-fluid">
    <div class="rankings-header">
        <h2><i class="fas fa-trophy me-2"></i>Global Rankings</h2>
        <p>See how you stack up against other reciters worldwide</p>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="filter-tabs">
                <a href="rankings.php?filter=country" class="filter-tab <?php echo $filter_type === 'country' ? 'active' : ''; ?>">
                    <i class="fas fa-globe me-1"></i>Country
                </a>
                <a href="rankings.php?filter=state" class="filter-tab <?php echo $filter_type === 'state' ? 'active' : ''; ?>">
                    <i class="fas fa-map-marked-alt me-1"></i>State
                </a>
                <a href="rankings.php?filter=ward" class="filter-tab <?php echo $filter_type === 'ward' ? 'active' : ''; ?>">
                    <i class="fas fa-map-marker-alt me-1"></i>Ward
                </a>
                <a href="rankings.php?filter=lgea" class="filter-tab <?php echo $filter_type === 'lgea' ? 'active' : ''; ?>">
                    <i class="fas fa-map-pin me-1"></i>LGEA
                </a>
            </div>
        </div>
        <div class="col-md-4">
            <div class="current-rank-card">
                <h3>Your Current Rank</h3>
                <div class="current-rank-number">#<?php echo $currentUserRank; ?></div>
                <p class="text-muted">in <?php echo ucfirst($filter_type); ?></p>
            </div>
        </div>
    </div>

    <div class="rankings-table">
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Reciter</th>
                    <th>Score</th>
                    <th>Accuracy</th>
                    <th>Recitations</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($rankings as $index => $ranking): ?>
                    <tr <?php echo $ranking['is_current_user'] ? 'class="current-user"' : ''; ?>>
                        <td>
                            <span class="rank-badge <?php echo $index < 3 ? 'top-3' : ($index < 10 ? 'top-10' : 'other'); ?>">
                                <?php echo $index + 1; ?>
                            </span>
                        </td>
                        <td>
                            <div class="user-info">
                                <img src="assets/images/default-avatar.png" alt="Avatar" class="user-avatar">
                                <div>
                                    <div class="name"><?php echo htmlspecialchars($ranking['name']); ?></div>
                                    <div class="location"><?php echo htmlspecialchars($ranking['location']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="score-badge"><?php echo number_format($ranking['score']); ?></span>
                        </td>
                        <td>
                            <span class="accuracy-badge"><?php echo $ranking['accuracy']; ?>%</span>
                        </td>
                        <td>
                            <span class="recitations-count"><?php echo $ranking['recitations']; ?></span>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<?php require_once 'components/user_footer.php'; ?> 