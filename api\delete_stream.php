<?php
/**
 * Delete Stream API for Recite! App
 * Handles stream deletion with password verification and earnings deduction
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$streamId = intval($input['stream_id'] ?? 0);
$password = trim($input['password'] ?? '');

if (!$streamId) {
    echo json_encode(['success' => false, 'message' => 'Invalid stream ID']);
    exit;
}

// Verify password
if ($password !== '1!3usazladan') {
    echo json_encode(['success' => false, 'message' => 'Incorrect password']);
    exit;
}

try {
    $conn = getConnection();
    
    // Check if stream exists and belongs to user
    $streamResult = executeQuery(
        "SELECT * FROM streams WHERE id = ? AND user_id = ?",
        'ii',
        [$streamId, $userId]
    );
    
    if ($streamResult->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Stream not found or you do not have permission to delete it']);
        exit;
    }
    
    $stream = $streamResult->fetch_assoc();
    
    // Calculate total earnings from this stream
    $earningsResult = executeQuery(
        "SELECT SUM(amount) as total_earnings FROM transactions 
         WHERE user_id = ? AND transaction_type = 'earning' 
         AND description LIKE '%stream%' 
         AND created_at >= ?",
        'is',
        [$userId, $stream['created_at']]
    );
    
    $earningsData = $earningsResult->fetch_assoc();
    $totalEarnings = floatval($earningsData['total_earnings'] ?? 0);
    
    // Get user's current balance
    $userResult = executeQuery(
        "SELECT wallet_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    
    $user = $userResult->fetch_assoc();
    $currentBalance = floatval($user['wallet_balance']);
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Delete stream interactions
        executeQuery(
            "DELETE FROM stream_interactions WHERE stream_id = ?",
            'i',
            [$streamId]
        );
        
        // Delete associated media file if exists
        if (!empty($stream['media_path'])) {
            $filePath = '../uploads/' . $stream['media_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        // Delete the stream
        executeQuery(
            "DELETE FROM streams WHERE id = ?",
            'i',
            [$streamId]
        );
        
        // Deduct earnings from user's wallet if any
        if ($totalEarnings > 0) {
            executeQuery(
                "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
                'di',
                [$totalEarnings, $userId]
            );
            
            // Log the deduction transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'deduction', ?, 'Stream deletion: earnings forfeited', 'completed')",
                'id',
                [$userId, -$totalEarnings]
            );
        }
        
        // Commit transaction
        $conn->commit();
        $conn->autocommit(true);
        
        $newBalance = $currentBalance - $totalEarnings;
        
        echo json_encode([
            'success' => true,
            'message' => 'Stream deleted successfully',
            'earnings_deducted' => $totalEarnings,
            'new_balance' => max(0, $newBalance)
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Delete stream error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to delete stream']);
}
?> 