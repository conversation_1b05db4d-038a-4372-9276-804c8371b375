<?php
/**
 * Wallet Transaction API for Recite! App
 * Handles wallet operations: buy points, sell points, withdrawals
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$action = $input['action'] ?? '';

try {
    $conn = getConnection();
    
    // Get current user balances
    $userResult = executeQuery(
        "SELECT wallet_balance, points_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    
    $user = $userResult->fetch_assoc();
    $walletBalance = floatval($user['wallet_balance']);
    $pointsBalance = intval($user['points_balance']);
    
    // Exchange rates
    $pointBuyRate = 70;  // ₦70 per point when buying
    $pointSellRate = 50; // ₦50 per point when selling
    
    switch ($action) {
        case 'buy_points':
            $points = intval($input['points'] ?? 0);
            $cost = $points * $pointBuyRate;
            
            if ($points <= 0) {
                echo json_encode(['success' => false, 'message' => 'Invalid number of points']);
                exit;
            }
            
            if ($cost > $walletBalance) {
                echo json_encode(['success' => false, 'message' => 'Insufficient wallet balance']);
                exit;
            }
            
            // Start transaction
            $conn->autocommit(false);
            
            try {
                // Deduct from wallet
                executeQuery(
                    "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
                    'di',
                    [$cost, $userId]
                );
                
                // Add points
                executeQuery(
                    "UPDATE users SET points_balance = points_balance + ? WHERE id = ?",
                    'ii',
                    [$points, $userId]
                );
                
                // Log transaction
                executeQuery(
                    "INSERT INTO transactions (user_id, transaction_type, amount, points_involved, description, status) VALUES (?, 'point_purchase', ?, ?, ?, 'completed')",
                    'idis',
                    [$userId, -$cost, $points, "Purchased $points points"]
                );
                
                $conn->commit();
                $conn->autocommit(true);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Points purchased successfully',
                    'new_wallet_balance' => $walletBalance - $cost,
                    'new_points_balance' => $pointsBalance + $points
                ]);
                
            } catch (Exception $e) {
                $conn->rollback();
                $conn->autocommit(true);
                throw $e;
            }
            break;
            
        case 'sell_points':
            $points = intval($input['points'] ?? 0);
            $amount = $points * $pointSellRate;
            
            if ($points <= 0) {
                echo json_encode(['success' => false, 'message' => 'Invalid number of points']);
                exit;
            }
            
            if ($points > $pointsBalance) {
                echo json_encode(['success' => false, 'message' => 'Insufficient points balance']);
                exit;
            }
            
            // Start transaction
            $conn->autocommit(false);
            
            try {
                // Deduct points
                executeQuery(
                    "UPDATE users SET points_balance = points_balance - ? WHERE id = ?",
                    'ii',
                    [$points, $userId]
                );
                
                // Add to wallet
                executeQuery(
                    "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
                    'di',
                    [$amount, $userId]
                );
                
                // Log transaction
                executeQuery(
                    "INSERT INTO transactions (user_id, transaction_type, amount, points_involved, description, status) VALUES (?, 'point_sale', ?, ?, ?, 'completed')",
                    'idis',
                    [$userId, $amount, $points, "Sold $points points"]
                );
                
                $conn->commit();
                $conn->autocommit(true);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Points sold successfully',
                    'new_wallet_balance' => $walletBalance + $amount,
                    'new_points_balance' => $pointsBalance - $points
                ]);
                
            } catch (Exception $e) {
                $conn->rollback();
                $conn->autocommit(true);
                throw $e;
            }
            break;
            
        case 'request_withdrawal':
            $amount = floatval($input['amount'] ?? 0);
            $bankName = sanitize($input['bank_name'] ?? '');
            $accountNumber = sanitize($input['account_number'] ?? '');
            $accountName = sanitize($input['account_name'] ?? '');
            
            if ($amount <= 0) {
                echo json_encode(['success' => false, 'message' => 'Invalid amount']);
                exit;
            }
            
            if ($amount > $walletBalance) {
                echo json_encode(['success' => false, 'message' => 'Insufficient wallet balance']);
                exit;
            }
            
            if (empty($bankName) || empty($accountNumber) || empty($accountName)) {
                echo json_encode(['success' => false, 'message' => 'All bank details are required']);
                exit;
            }
            
            // Create withdrawal request
            executeQuery(
                "INSERT INTO withdrawal_requests (user_id, amount, bank_name, account_number, account_holder_name, status) VALUES (?, ?, ?, ?, ?, 'pending')",
                'idsss',
                [$userId, $amount, $bankName, $accountNumber, $accountName]
            );
            
            // Log transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'withdrawal', ?, ?, 'pending')",
                'ids',
                [$userId, -$amount, "Withdrawal request to $bankName"]
            );
            
            echo json_encode([
                'success' => true,
                'message' => 'Withdrawal request submitted successfully. It will be processed within 24 hours.'
            ]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Wallet transaction error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Transaction failed. Please try again.']);
}
?>
