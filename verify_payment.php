<?php
/**
 * Payment Verification for Recite! App
 * Verifies Paystack payments and activates user accounts
 */

require_once 'config/database.php';

$error = '';
$success = '';

// Check if reference is provided
if (!isset($_GET['reference']) && !isset($_POST['reference'])) {
    header('Location: register.php');
    exit;
}

$reference = sanitize($_GET['reference'] ?? $_POST['reference']);

try {
    // DEVELOPMENT MODE: Mock payment verification for testing
    if (DEVELOPMENT_MODE && defined('DEVELOPMENT_MODE')) {
        error_log("DEVELOPMENT MODE: Using mock payment verification");
        
        // Extract user ID from reference (format: REG_USERID_RANDOMNUMBER)
        $userId = null;
        if (preg_match('/^REG_(\d+)_/', $reference, $matches)) {
            $userId = (int)$matches[1];
        }
        
        // If we can't extract user ID from reference, try session
        if (!$userId && isset($_SESSION['pending_user_id'])) {
            $userId = $_SESSION['pending_user_id'];
        }
        
        if (!$userId) {
            throw new Exception("Cannot determine user ID for payment verification");
        }
        
        // Mock successful payment data
        $result = [
            'status' => true,
            'data' => [
                'status' => 'success',
                'amount' => 100000, // 1000 NGN in kobo
                'metadata' => [
                    'user_id' => $userId
                ],
                'reference' => $reference
            ]
        ];
        
        error_log("Mock payment data: " . json_encode($result));
    } else {
        // Debug: Show what key we're using
        error_log("Using Paystack Secret Key: " . substr(PAYSTACK_SECRET_KEY, 0, 15) . "...");
        
        // Verify payment with Paystack using file_get_contents
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'Authorization: Bearer ' . PAYSTACK_SECRET_KEY,
                    'Cache-Control: no-cache',
                    'Content-Type: application/json'
                ],
                'timeout' => 30
            ]
        ]);
        
        $response = file_get_contents("https://api.paystack.co/transaction/verify/" . $reference, false, $context);
        
        // Debug: Log the raw response
        error_log("Paystack API Response: " . ($response ?: 'FALSE - No response'));
        
        if ($response === false) {
            // Get more detailed error info
            $error_details = error_get_last();
            error_log("Stream error: " . print_r($error_details, true));
            throw new Exception("Payment verification failed: Unable to connect to payment gateway");
        }
        
        $result = json_decode($response, true);
    }
    
    if ($result['status'] && $result['data']['status'] === 'success') {
        $paymentData = $result['data'];
        $userId = $paymentData['metadata']['user_id'] ?? null;
        $amount = $paymentData['amount'] / 100; // Convert from kobo to naira
        
        if (!$userId) {
            throw new Exception("Invalid payment metadata");
        }
        
        // Check if payment already processed
        $existingPayment = executeQuery(
            "SELECT id FROM transactions WHERE reference_id = ?",
            's',
            [$reference]
        );
        
        if ($existingPayment->num_rows > 0) {
            $success = 'Payment already processed. You can now login to your account.';
        } else {
            $conn = getConnection();
            $conn->autocommit(false);
            
            try {
                // Activate user account
                executeQuery(
                    "UPDATE users SET is_active = 1, payment_verified = 1 WHERE id = ?",
                    'i',
                    [$userId]
                );
                
                // Record payment transaction
                executeQuery(
                    "INSERT INTO transactions (user_id, transaction_type, amount, description, reference_id, status) VALUES (?, 'payment', ?, 'Registration fee', ?, 'completed')",
                    'ids',
                    [$userId, $amount, $reference]
                );
                
                // Award referral points if applicable
                $userResult = executeQuery(
                    "SELECT referred_by FROM users WHERE id = ?",
                    'i',
                    [$userId]
                );
                
                $user = $userResult->fetch_assoc();
                if ($user['referred_by']) {
                    // Give 1 point to referrer
                    executeQuery(
                        "UPDATE users SET points_balance = points_balance + 1 WHERE id = ?",
                        'i',
                        [$user['referred_by']]
                    );
                    
                    // Log referral transaction
                    executeQuery(
                        "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'referral', 1, 'Referral bonus', 'completed')",
                        'i',
                        [$user['referred_by']]
                    );
                    
                    // Add to referrals table
                    executeQuery(
                        "INSERT INTO referrals (referrer_id, referred_id) VALUES (?, ?)",
                        'ii',
                        [$user['referred_by'], $userId]
                    );
                }
                
                $conn->commit();
                $conn->autocommit(true);
                
                // Get user data for session
                $userResult = executeQuery(
                    "SELECT id, full_name, email FROM users WHERE id = ?",
                    'i',
                    [$userId]
                );
                $userData = $userResult->fetch_assoc();
                
                if ($userData) {
                    // Automatically log in the user
                    $_SESSION['user_id'] = $userData['id'];
                    $_SESSION['user_name'] = $userData['full_name'];
                    $_SESSION['user_email'] = $userData['email'];
                    $_SESSION['login_time'] = time();
                    
                    // Regenerate session ID for security
                    session_regenerate_id(true);
                }
                
                // Clear pending session data
                unset($_SESSION['pending_user_id']);
                
                $success = 'Payment verified successfully! Your account is now active. Redirecting to dashboard...';
                
            } catch (Exception $e) {
                $conn->rollback();
                $conn->autocommit(true);
                throw $e;
            }
        }
        
    } else {
        $error = 'Payment verification failed. Please contact support if you were charged.';
    }
    
} catch (Exception $e) {
    error_log("Payment verification error: " . $e->getMessage());
    $error = 'Payment verification failed. Please try again or contact support.';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Verification - <?php echo APP_NAME; ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/modern-ui.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="auth-card card">
                        <div class="auth-header text-center">
                            <h1 class="auth-title">Payment Verification</h1>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger text-center">
                                <i class="fas fa-times-circle fa-3x mb-3"></i>
                                <h4>Payment Failed</h4>
                                <p><?php echo $error; ?></p>
                                <a href="register.php" class="btn btn-primary">Try Again</a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle fa-3x mb-3"></i>
                                <h4>Payment Successful!</h4>
                                <p><?php echo $success; ?></p>
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <script>
                                        // Auto-redirect to dashboard after 3 seconds
                                        setTimeout(function() {
                                            window.location.href = 'dashboard.php';
                                        }, 3000);
                                    </script>
                                    <div class="mt-3">
                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                        <small>Redirecting to dashboard in 3 seconds...</small>
                                    </div>
                                    <a href="dashboard.php" class="btn btn-primary btn-lg mt-3">
                                        <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard Now
                                    </a>
                                <?php else: ?>
                                    <a href="login.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login to Your Account
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!$error && !$success): ?>
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-3">Verifying your payment...</p>
                            </div>
                        <?php endif; ?>
                        
                        <div class="text-center mt-4">
                            <a href="index.php" class="text-secondary">
                                <i class="fas fa-home me-1"></i>Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html> 