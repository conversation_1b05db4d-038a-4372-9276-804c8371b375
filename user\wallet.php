<?php
$page_title = 'My Wallet';
require_once __DIR__ . '/../components/user_header.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Fetch wallet stats
$balance = $user_stats['balance'] ?? 0;

$transactions = [];
$success_message = '';
$error_message = '';

try {
    $conn = getConnection();
    
    // Fetch transaction history
    $stmt = $conn->prepare("
        SELECT * FROM transactions
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 50
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $transactions = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();

    // Handle withdrawal request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_withdrawal'])) {
        $amount = filter_input(INPUT_POST, 'amount', FILTER_VALIDATE_FLOAT);
        
        if ($amount && $amount > 0 && $amount <= $balance) {
            // Start transaction
            $conn->autocommit(FALSE);
            
            try {
                // Deduct from balance
                $stmt1 = $conn->prepare("UPDATE user_stats SET balance = balance - ? WHERE user_id = ?");
                $stmt1->bind_param("di", $amount, $user_id);
                $stmt1->execute();
                $stmt1->close();

                // Create withdrawal record
                $stmt2 = $conn->prepare("INSERT INTO withdrawals (user_id, amount, status, created_at) VALUES (?, ?, 'pending', NOW())");
                $stmt2->bind_param("id", $user_id, $amount);
                $stmt2->execute();
                $stmt2->close();
                
                // Log transaction
                $negative_amount = -$amount;
                $stmt3 = $conn->prepare("INSERT INTO transactions (user_id, amount, type, description, created_at) VALUES (?, ?, 'withdrawal', 'Withdrawal request', NOW())");
                $stmt3->bind_param("ids", $user_id, $negative_amount, 'withdrawal');
                $stmt3->execute();
                $stmt3->close();

                // Commit transaction
                $conn->commit();
                $conn->autocommit(TRUE);
                
                $success_message = "Withdrawal request of ₦" . number_format($amount, 2) . " submitted successfully.";
                // Refresh data
                $balance -= $amount;
                
                // Re-fetch transactions
                $stmt = $conn->prepare("
                    SELECT * FROM transactions
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT 50
                ");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $transactions = $result->fetch_all(MYSQLI_ASSOC);
                $stmt->close();
                
            } catch (Exception $e) {
                $conn->rollback();
                $conn->autocommit(TRUE);
                $error_message = "Failed to process withdrawal: " . $e->getMessage();
            }
        } else {
            $error_message = "Invalid withdrawal amount.";
        }
    }
    
    $conn->close();
} catch (Exception $e) {
    $error_message = "Database error: " . $e->getMessage();
    error_log("Wallet error: " . $e->getMessage());
}
?>

<div class="row">
    <!-- Wallet Balance Card -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-wallet me-2"></i>My Balance
                </h3>
            </div>
            <div class="card-body text-center">
                <h1 class="display-4 fw-bold text-primary mb-3">₦<?php echo number_format($balance, 2); ?></h1>
                <p class="text-secondary">Available for withdrawal</p>
                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#withdrawalModal">
                    <i class="fas fa-paper-plane me-2"></i>Request Withdrawal
                </button>
            </div>
        </div>
    </div>

    <!-- Transaction History Card -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history me-2"></i>Transaction History
                </h3>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>
                <?php if ($error_message): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Description</th>
                                <th class="text-end">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($transactions)): ?>
                                <tr><td colspan="3" class="text-center text-secondary py-4">No transactions yet.</td></tr>
                            <?php else: ?>
                                <?php foreach ($transactions as $tx): ?>
                                <tr>
                                    <td><?php echo date('M d, Y H:i', strtotime($tx['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($tx['description'] ?? 'Transaction'); ?></td>
                                    <td class="text-end">
                                        <span class="fw-bold text-<?php echo floatval($tx['amount']) > 0 ? 'success' : 'danger'; ?>">
                                            <?php echo (floatval($tx['amount']) > 0 ? '+' : '') . '₦' . number_format(abs(floatval($tx['amount'])), 2); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Withdrawal Modal -->
<div class="modal fade" id="withdrawalModal" tabindex="-1" aria-labelledby="withdrawalModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="withdrawalModalLabel">Request Withdrawal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Your current balance is <strong>₦<?php echo number_format($balance, 2); ?></strong>.</p>
                    <div class="form-group">
                        <label for="amount" class="form-label">Amount to Withdraw</label>
                        <input type="number" class="form-control" id="amount" name="amount" placeholder="Amount" step="0.01" max="<?php echo $balance; ?>" required>
                    </div>
                    <p class="text-secondary small">Note: Withdrawals are processed within 3-5 business days. Make sure your bank details are up to date in your profile.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="request_withdrawal" class="btn btn-primary">Submit Request</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>