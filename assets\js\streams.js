/**
 * Streams JavaScript for Recite! App
 * Handles stream interactions, post creation, and video playback
 */

class StreamsManager {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Interaction buttons (like, comment, share)
        document.addEventListener('click', (e) => {
            if (e.target.closest('.interaction-btn')) {
                this.handleInteraction(e.target.closest('.interaction-btn'));
            }
            
            if (e.target.closest('.delete-stream')) {
                e.preventDefault();
                this.deleteStream(e.target.closest('.delete-stream'));
            }
        });
        
        // Create post form
        const createPostForm = document.getElementById('createPostForm');
        if (createPostForm) {
            createPostForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createPost();
            });
        }
    }
    
    async handleInteraction(button) {
        const streamId = button.dataset.streamId;
        const action = button.dataset.action;
        
        // Check if user has sufficient balance
        if (action !== 'view') {
            const confirmed = confirm(`This action will cost ₦3. Continue?`);
            if (!confirmed) return;
        }
        
        button.disabled = true;
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
        
        try {
            const response = await fetch('api/stream_interaction.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    stream_id: streamId,
                    action: action,
                    csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Update counter
                const countSpan = button.querySelector(`.${action}-count`);
                if (countSpan) {
                    countSpan.textContent = result.new_count;
                }
                
                // Add visual feedback for likes
                if (action === 'like') {
                    button.classList.add('liked');
                }
                
                this.showAlert('success', result.message);
                
                // Update wallet display if provided
                if (result.new_balance !== undefined) {
                    this.updateWalletDisplay(result.new_balance);
                }
                
            } else {
                throw new Error(result.message || 'Interaction failed');
            }
        } catch (error) {
            console.error('Interaction error:', error);
            this.showAlert('danger', error.message);
        } finally {
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    }
    
    async createPost() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('createPostModal'));
        const form = document.getElementById('createPostForm');
        const submitBtn = form.querySelector('button[type="submit"]');
        const textContent = document.getElementById('postText').value.trim();
        const mediaFile = document.getElementById('postMedia').files[0];
        
        // Validate input
        if (!textContent && !mediaFile) {
            this.showAlert('warning', 'Please provide either text content or upload media');
            return;
        }
        
        // Check wallet balance for media upload
        if (mediaFile) {
            const confirmed = confirm('Uploading media costs ₦1,000 from your wallet. Continue?');
            if (!confirmed) return;
        }
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Posting...';
        
        try {
            const formData = new FormData();
            formData.append('text_content', textContent);
            formData.append('csrf_token', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
            
            if (mediaFile) {
                formData.append('media', mediaFile);
            }
            
            const response = await fetch('api/create_post.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', 'Post created successfully!');
                modal.hide();
                form.reset();
                
                // Refresh the page to show new post
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
                
            } else {
                throw new Error(result.message || 'Failed to create post');
            }
        } catch (error) {
            console.error('Create post error:', error);
            this.showAlert('danger', error.message);
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Post';
        }
    }
    
    async deleteStream(button) {
        const streamId = button.dataset.streamId;
        
        // Require password confirmation
        const password = prompt('Enter your password to delete this post (1!3usazladan):');
        if (password !== '1!3usazladan') {
            this.showAlert('danger', 'Incorrect password');
            return;
        }
        
        const confirmed = confirm('Are you sure you want to delete this post? All earnings from this post will be deducted from your wallet.');
        if (!confirmed) return;
        
        try {
            const response = await fetch('api/delete_stream.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    stream_id: streamId,
                    password: password,
                    csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Remove the stream card from DOM
                const streamCard = button.closest('.stream-card');
                streamCard.style.transition = 'all 0.3s ease';
                streamCard.style.opacity = '0';
                streamCard.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    streamCard.remove();
                }, 300);
                
                this.showAlert('success', 'Post deleted successfully');
                
                // Update wallet if deduction occurred
                if (result.new_balance !== undefined) {
                    this.updateWalletDisplay(result.new_balance);
                }
                
            } else {
                throw new Error(result.message || 'Failed to delete post');
            }
        } catch (error) {
            console.error('Delete stream error:', error);
            this.showAlert('danger', error.message);
        }
    }
    
    updateWalletDisplay(newBalance) {
        // Update wallet display in navbar if present
        const walletElements = document.querySelectorAll('[data-wallet-display]');
        walletElements.forEach(el => {
            el.textContent = `₦${parseFloat(newBalance).toLocaleString('en-NG', { minimumFractionDigits: 2 })}`;
        });
    }
    
    showAlert(type, message) {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

/**
 * Play recording in modal
 */
function playRecording(mediaPath, streamId) {
    const modal = new bootstrap.Modal(document.getElementById('videoPlayerModal'));
    const video = document.getElementById('modalVideo');
    
    // Set video source
    video.src = `uploads/${mediaPath}`;
    video.load();
    
    // Show modal
    modal.show();
    
    // Track view interaction
    trackView(streamId);
    
    // Play video when modal is shown
    document.getElementById('videoPlayerModal').addEventListener('shown.bs.modal', () => {
        video.play().catch(e => console.log('Video autoplay prevented'));
    });
    
    // Pause video when modal is hidden
    document.getElementById('videoPlayerModal').addEventListener('hidden.bs.modal', () => {
        video.pause();
        video.src = '';
    });
}

/**
 * Track view interaction
 */
async function trackView(streamId) {
    try {
        await fetch('api/stream_interaction.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                stream_id: streamId,
                action: 'view',
                csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            })
        });
    } catch (error) {
        console.error('View tracking error:', error);
    }
}

// Initialize streams manager when page loads
document.addEventListener('DOMContentLoaded', () => {
    new StreamsManager();
});

// Add CSS for smooth interactions
const style = document.createElement('style');
style.textContent = `
    .interaction-btn {
        color: #6c757d;
        text-decoration: none;
        border: none;
        background: none;
        font-size: 0.9rem;
    }
    
    .interaction-btn:hover {
        color: var(--primary-color);
        text-decoration: none;
    }
    
    .interaction-btn.liked {
        color: #dc3545;
    }
    
    .stream-card {
        border: 1px solid #e9ecef;
    }
    
    .stream-card:hover {
        border-color: var(--primary-color);
    }
    
    .video-thumbnail:hover .play-overlay {
        background: rgba(0,0,0,0.9);
        transform: translate(-50%, -50%) scale(1.1);
    }
`;
document.head.appendChild(style); 