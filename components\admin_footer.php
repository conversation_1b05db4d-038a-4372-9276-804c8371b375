    </div><!-- /.dashboard-container -->
</main><!-- /.admin-main -->

<script>
document.addEventListener('DOMContentLoaded', function () {
    const sidebar = document.getElementById('adminSidebar');
    const mainContent = document.getElementById('adminMain');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenuButton = document.getElementById('mobileMenuButton');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    // Function to toggle sidebar
    const toggleSidebar = () => {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('collapsed');
        updateToggleButtonIcon();
        // Store preference in localStorage
        localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
    };

    // Function to toggle mobile sidebar
    const toggleMobileSidebar = () => {
        sidebar.classList.toggle('open');
        sidebarOverlay.classList.toggle('show');
    };
    
    // Update toggle button icon
    const updateToggleButtonIcon = () => {
        const icon = sidebarToggle.querySelector('i');
        if (sidebar.classList.contains('collapsed')) {
            icon.classList.remove('fa-chevron-left');
            icon.classList.add('fa-chevron-right');
        } else {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-left');
        }
    };

    // Desktop sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Mobile sidebar toggle
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', toggleMobileSidebar);
    }

    // Overlay click to close mobile sidebar
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', toggleMobileSidebar);
    }

    // Check localStorage for user's preference
    if (localStorage.getItem('sidebarCollapsed') === 'true') {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('collapsed');
    }
    
    updateToggleButtonIcon();
});
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html> 