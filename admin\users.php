<?php
$page_title = 'Manage Users';
require_once __DIR__ . '/../components/admin_header.php';

$conn = getConnection();
$message = '';
$error = '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $userId = intval($_POST['user_id'] ?? 0);

    if ((!isset($_POST['csrf_token']) || validateCSRFToken($_POST['csrf_token'])) && $userId > 0) {
        switch ($action) {
            case 'update_user':
                $fullName = sanitize($_POST['full_name']);
                $email = sanitize($_POST['email']);
                $walletBalance = floatval($_POST['wallet_balance']);
                $pointsBalance = intval($_POST['points_balance']);
                $isBlocked = isset($_POST['is_blocked']) ? 1 : 0;

                try {
                    $stmt = $conn->prepare("UPDATE users SET full_name = ?, email = ?, wallet_balance = ?, points_balance = ?, is_blocked = ? WHERE id = ?");
                    $stmt->bind_param("ssddii", $fullName, $email, $walletBalance, $pointsBalance, $isBlocked, $userId);

                    if ($stmt->execute()) {
                        $message = 'User updated successfully!';
                    } else {
                        $error = 'Failed to update user.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_user':
                try {
                    // First delete related records
                    $conn->query("DELETE FROM transactions WHERE user_id = $userId");
                    $conn->query("DELETE FROM unlocked_content WHERE user_id = $userId");
                    $conn->query("DELETE FROM screen_records WHERE user_id = $userId");
                    $conn->query("DELETE FROM streams WHERE user_id = $userId");

                    // Then delete the user
                    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->bind_param("i", $userId);

                    if ($stmt->execute()) {
                        $message = 'User deleted successfully!';
                    } else {
                        $error = 'Failed to delete user.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'toggle_block':
                try {
                    $stmt = $conn->prepare("UPDATE users SET is_blocked = NOT is_blocked WHERE id = ?");
                    $stmt->bind_param("i", $userId);

                    if ($stmt->execute()) {
                        $message = 'User status updated successfully!';
                    } else {
                        $error = 'Failed to update user status.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Fetch users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$conn = getConnection();

$where_clauses = [];
$search_param = '';
if (!empty($search)) {
    $where_clauses[] = "(full_name LIKE '%$search%' OR email LIKE '%$search%')";
    $search_param = "%$search%";
}

$where_sql = count($where_clauses) > 0 ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

// Get total count for pagination
$total_result = $conn->query("SELECT COUNT(*) as count FROM users $where_sql");
$total_users = $total_result->fetch_assoc()['count'];
$total_pages = ceil($total_users / $limit);

// Fetch users
$users_result = $conn->query("SELECT * FROM users $where_sql ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
$users = $users_result->fetch_all(MYSQLI_ASSOC);
?>

<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Search and Filter Bar -->
<div class="data-table-container mb-4">
    <div class="table-header">
        <h4 class="table-title">All Users</h4>
        <form action="users.php" method="GET" class="d-flex gap-2">
            <input type="text" name="search" class="form-control" placeholder="Search by username or email..." value="<?php echo htmlspecialchars($search); ?>">
            <button type="submit" class="btn btn-primary">Search</button>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="data-table-container">
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Email</th>
                    <th>Wallet</th>
                    <th>Points</th>
                    <th>Status</th>
                    <th>Joined</th>
                    <th class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($users)): ?>
                    <tr><td colspan="7" class="text-center py-5">No users found.</td></tr>
                <?php else: ?>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar">
                                    <?php echo strtoupper(substr($user['full_name'] ?? $user['email'], 0, 1)); ?>
                                </div>
                                <div class="ms-3">
                                    <div class="fw-semibold"><?php echo htmlspecialchars($user['full_name'] ?? 'N/A'); ?></div>
                                    <small class="text-muted">ID: <?php echo $user['id']; ?></small>
                                </div>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                        <td>
                            <span class="fw-semibold text-success">₦<?php echo number_format($user['wallet_balance'] ?? 0, 2); ?></span>
                        </td>
                        <td>
                            <span class="fw-semibold text-primary"><?php echo number_format($user['points_balance'] ?? 0); ?></span>
                        </td>
                        <td>
                            <?php if ($user['is_blocked']): ?>
                                <span class="badge bg-danger">Blocked</span>
                            <?php elseif ($user['payment_verified']): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-warning">Pending</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                        <td class="text-end">
                            <button class="btn btn-sm btn-primary" onclick="editUser(<?php echo $user['id']; ?>)">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-sm btn-<?php echo $user['is_blocked'] ? 'success' : 'warning'; ?>" onclick="toggleBlock(<?php echo $user['id']; ?>)">
                                <i class="fas fa-<?php echo $user['is_blocked'] ? 'unlock' : 'lock'; ?>"></i>
                                <?php echo $user['is_blocked'] ? 'Unblock' : 'Block'; ?>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser(<?php echo $user['id']; ?>)">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="table-footer p-3 d-flex justify-content-end">
        <nav>
            <ul class="pagination mb-0">
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_user">
                    <input type="hidden" name="user_id" id="editUserId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="mb-3">
                        <label for="editFullName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="editFullName" name="full_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editWalletBalance" class="form-label">Wallet Balance (₦)</label>
                                <input type="number" class="form-control" id="editWalletBalance" name="wallet_balance" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPointsBalance" class="form-label">Points Balance</label>
                                <input type="number" class="form-control" id="editPointsBalance" name="points_balance" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsBlocked" name="is_blocked">
                            <label class="form-check-label" for="editIsBlocked">
                                Block this user
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Action Forms -->
<form id="toggleBlockForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="toggle_block">
    <input type="hidden" name="user_id" id="toggleUserId">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
</form>

<form id="deleteUserForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete_user">
    <input type="hidden" name="user_id" id="deleteUserId">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
</form>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
<style>
/* Custom styles for pagination in dark mode if needed */
.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}
.page-link {
    color: var(--primary);
}
.page-link:hover {
    color: var(--primary-dark);
}

/* User management styles */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>

<script src="../assets/js/admin-users.js"></script>