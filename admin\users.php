<?php
$page_title = 'Manage Users';
require_once __DIR__ . '/../components/admin_header.php';

// Fetch users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$conn = getConnection();

$where_clauses = [];
$search_param = '';
if (!empty($search)) {
    $where_clauses[] = "(full_name LIKE '%$search%' OR email LIKE '%$search%')";
    $search_param = "%$search%";
}

$where_sql = count($where_clauses) > 0 ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

// Get total count for pagination
$total_result = $conn->query("SELECT COUNT(*) as count FROM users $where_sql");
$total_users = $total_result->fetch_assoc()['count'];
$total_pages = ceil($total_users / $limit);

// Fetch users
$users_result = $conn->query("SELECT * FROM users $where_sql ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
$users = $users_result->fetch_all(MYSQLI_ASSOC);
?>

<!-- Search and Filter Bar -->
<div class="data-table-container mb-4">
    <div class="table-header">
        <h4 class="table-title">All Users</h4>
        <form action="users.php" method="GET" class="d-flex gap-2">
            <input type="text" name="search" class="form-control" placeholder="Search by username or email..." value="<?php echo htmlspecialchars($search); ?>">
            <button type="submit" class="btn btn-primary">Search</button>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="data-table-container">
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Email</th>
                    <th>Joined Date</th>
                    <th>Status</th>
                    <th class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($users)): ?>
                    <tr><td colspan="5" class="text-center py-5">No users found.</td></tr>
                <?php else: ?>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <img src="<?php echo htmlspecialchars($user['profile_picture'] ? '../uploads/profiles/' . $user['profile_picture'] : '../assets/images/default-avatar.png'); ?>" 
                                     alt="<?php echo htmlspecialchars($user['full_name'] ?? $user['email']); ?>" class="rounded-circle me-3" width="40" height="40">
                                <div>
                                    <div class="fw-semibold"><?php echo htmlspecialchars($user['full_name'] ?? $user['email']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                </div>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                        <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                        <td>
                            <span class="badge badge-<?php echo $user['is_active'] ? 'success' : 'danger'; ?>">
                                <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td class="text-end">
                            <a href="#" class="btn btn-sm btn-outline">Edit</a>
                            <a href="#" class="btn btn-sm btn-danger">Delete</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="table-footer p-3 d-flex justify-content-end">
        <nav>
            <ul class="pagination mb-0">
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
<style>
/* Custom styles for pagination in dark mode if needed */
.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}
.page-link {
    color: var(--primary);
}
.page-link:hover {
    color: var(--primary-dark);
}
</style>