<?php
/**
 * Stream Interaction API for Recite! App
 * Handles likes, comments, shares, and views with transaction processing
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$streamId = intval($input['stream_id'] ?? 0);
$action = trim($input['action'] ?? '');

if (!$streamId || !in_array($action, ['like', 'comment', 'share', 'view'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid interaction data']);
    exit;
}

try {
    $conn = getConnection();
    
    // Check if stream exists
    $streamResult = executeQuery(
        "SELECT * FROM streams WHERE id = ?",
        'i',
        [$streamId]
    );
    
    if ($streamResult->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Stream not found']);
        exit;
    }
    
    $stream = $streamResult->fetch_assoc();
    $streamOwnerId = $stream['user_id'];
    
    // Check if user already performed this action (except views)
    if ($action !== 'view') {
        $existingResult = executeQuery(
            "SELECT id FROM stream_interactions WHERE user_id = ? AND stream_id = ? AND interaction_type = ?",
            'iis',
            [$userId, $streamId, $action]
        );
        
        if ($existingResult->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'You have already ' . $action . 'd this post']);
            exit;
        }
    }
    
    // Get user's current wallet balance for paid interactions
    $interactionCost = ($action === 'view') ? 0 : 3; // ₦3 for like, comment, share
    $newBalance = null;
    
    if ($interactionCost > 0) {
        $userResult = executeQuery(
            "SELECT wallet_balance FROM users WHERE id = ?",
            'i',
            [$userId]
        );
        
        $user = $userResult->fetch_assoc();
        $currentBalance = floatval($user['wallet_balance']);
        
        if ($currentBalance < $interactionCost) {
            echo json_encode([
                'success' => false, 
                'message' => 'Insufficient wallet balance. Please fund your wallet.'
            ]);
            exit;
        }
        
        $newBalance = $currentBalance - $interactionCost;
    }
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Record the interaction
        executeQuery(
            "INSERT INTO stream_interactions (user_id, stream_id, interaction_type) VALUES (?, ?, ?)",
            'iis',
            [$userId, $streamId, $action]
        );
        
        // Handle payment for paid interactions
        if ($interactionCost > 0) {
            // Deduct from user's wallet
            executeQuery(
                "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
                'di',
                [$interactionCost, $userId]
            );
            
            // Add ₦1 to stream owner's wallet (₦2 goes to app)
            $ownerEarning = 1;
            executeQuery(
                "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
                'di',
                [$ownerEarning, $streamOwnerId]
            );
            
            // Log user's transaction (deduction)
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'interaction', ?, ?, 'completed')",
                'ids',
                [$userId, -$interactionCost, ucfirst($action) . ' on stream']
            );
            
            // Log stream owner's transaction (earning)
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'earning', ?, ?, 'completed')",
                'ids',
                [$streamOwnerId, $ownerEarning, 'Earning from stream ' . $action]
            );
            
            // Log app's revenue (₦2)
            $appRevenue = $interactionCost - $ownerEarning;
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'app_revenue', ?, ?, 'completed')",
                'ids',
                [0, $appRevenue, 'App revenue from stream interaction']
            );
        }
        
        // Get updated interaction count
        $countResult = executeQuery(
            "SELECT COUNT(*) as count FROM stream_interactions WHERE stream_id = ? AND interaction_type = ?",
            'is',
            [$streamId, $action]
        );
        
        $countData = $countResult->fetch_assoc();
        $newCount = intval($countData['count']);
        
        // Commit transaction
        $conn->commit();
        $conn->autocommit(true);
        
        $response = [
            'success' => true,
            'message' => ucfirst($action) . ' recorded successfully',
            'new_count' => $newCount,
            'cost' => $interactionCost
        ];
        
        if ($newBalance !== null) {
            $response['new_balance'] = $newBalance;
        }
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Stream interaction error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to process interaction']);
}
?> 