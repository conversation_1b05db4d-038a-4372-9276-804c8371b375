Generate a full-stack responsive web app called “Qur’an Recite App”, with admin and user flows, dynamic dashboards, payment logic, voice feedback, screen recording, rankings, and multilingual support.

🧠 Overview & Purpose
The app is designed for reciting the Qur’an interactively using AI. It will help users listen, recite, get corrected via speech-to-text, and earn points or money via a reward system.

🧱 STRUCTURE OF THE APP
1️⃣ Landing Page
App name: Qur’an Recite App

Welcome text in English, Hausa, Arabic, Hindi, Chinese

Button: Register (₦1000 via Paystack)

Button: Login

Link to About Us, Contact Us, Privacy Policy

Display featured videos (streaming content from users)

Showcase ranking leaderboard (Ward, LGEA, State, Country)

2️⃣ User Authentication System
User registration with:

Full name

Email

Phone number

Password

Upload profile picture

Pay ₦1000 via Paystack

User login/logout with session

Password recovery

3️⃣ Admin Login
Login with master password: 1@3Usazladan

Can:

Upload YouTube video links (title, category, reciter)

Ban user, delete or block videos

View earnings, reports

Moderate flagged content

👤 USER DASHBOARD FLOW (After Login)
The user dashboard is split into 4 non-scrollable sections on large screen (or stacked on mobile):

🔳 Part 1: Video Section
Embedded YouTube video (added by admin)

User selects a video to watch before reciting

Only admin can post videos (secured by password)

🔳 Part 2: Mirror Selfie Camera
Live mirror view using webcam (no recording/saving)

Helps user watch posture during recitation

🔳 Part 3: Arabic Speech-to-Text Panel
After video ends, system plays voice from video

User must recite same verses

App listens and:

Highlights correct words (blue)

Highlights mistakes (red)

Allows 3 attempts per error:

1st error = −1 mark

2nd = −2 marks

3rd = −3 marks

AI stops at error until attempt used or passed

🔳 Part 4: Ranking System
Show user’s rank by:

Local Ward

LGEA

State

Country

Update in real-time using percentage of correct marks

1 point awarded for each 100% recitation

Bonus: 100 recitations in 72 hours = 100 points

📼 SCREEN RECORDING LOGIC
When user begins reciting, screen record starts automatically

At end, recording is:

Saved to server

Linked to user’s profile

Published on Streaming Home Page

Available to public to view, like, share, comment

📲 STREAMING HOME PAGE
Display all screen recordings (title, user icon)

Visitors can:

Watch (₦3 deducted from visitor)

Like (₦3 deducted)

Comment (₦3 deducted)

Share (₦3 deducted)

Of ₦3:

₦1 goes to video creator

₦2 goes to app wallet

Users can delete their own video

Reports are reviewed by admin

🪙 WALLET SYSTEM
Display wallet in ₦ and points

Earn:

1 point per recitation

1 point per referral

Bonus: 100 points for 100 recitations in 72hrs

Spend:

₦30 to access each screen recording session

₦1000 to upload new post (image/video)

Buy point: ₦70 per point via Paystack

Sell point: ₦50 per point (min 50 points)

Withdraw: ₦100/point (only after 30 days maturity)

🏦 BANK VERIFICATION
Users enter:

Bank name

Account number

App fetches and shows account holder name

If user sells or withdraws matured point:

App transfers the cash via manual record or integrated transfer

Transaction is stored in wallet history

👤 USER PROFILE PAGE
View:

Name

Email

Profile picture

Country, State, Ward, LGEA

Wallet & point balances

Video/post history

Referral link

Online status of people in same ward/state

Update personal info

📨 REFERRAL SYSTEM
Each user gets custom referral link

Every new user that registers via link = +1 point to referrer

Referral dashboard displays:

Who joined

When

From where

🧑‍💼 ADMIN DASHBOARD
Secure login with 1@3Usazladan

Manage:

User list, ban or unblock

Uploaded content

Stream recordings

Reports

App earnings

Upload YouTube videos for recitation

Flag abuse

📣 SOCIAL FEATURES
Live chat between users

Group video rooms

One-to-one video call

Online status by Ward / State / Country

📄 ABOUT US / CONTACT PAGE
Info about foundation, founders, scholars

Contact form (email)

WhatsApp group link

Donation support option

🔐 SECURITY & POLICIES
CSRF protection

Secure Paystack integration

User session tokens

Spam prevention on comments

📊 DATABASE TABLES (to be generated in MySQL)
Tables to include:

users

referrals

videos

screen_records

wallets

transactions

rankings

comments

likes

reports

posts

settings

withdrawal_requests

admin_logs

🌐 LANGUAGES SUPPORTED
English

Hausa

Arabic

Hindi

Chinese