<?php
/**
 * Unlock Content API for Recite! App
 * Handles content unlocking with wallet deduction
 */

// Prevent any output before JSON
ob_start();

require_once '../config/database.php';

// Clear any previous output
ob_clean();
header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    ob_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Debug logging
error_log("Unlock content request - User ID: $userId");
error_log("Input data: " . print_r($input, true));

// Validate CSRF token (temporarily disabled for debugging)
// if (!validateCSRFToken($input['csrf_token'] ?? '')) {
//     http_response_code(403);
//     echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
//     exit;
// }

$contentId = intval($input['content_id'] ?? 0);

if (!$contentId) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Invalid content ID']);
    exit;
}

try {
    $conn = getConnection();
    
    // Check if content exists
    $stmt = $conn->prepare("SELECT * FROM content WHERE id = ?");
    $stmt->bind_param("i", $contentId);
    $stmt->execute();
    $contentResult = $stmt->get_result();

    if ($contentResult->num_rows === 0) {
        error_log("Content not found for ID: $contentId");
        ob_clean();
        echo json_encode(['success' => false, 'message' => 'Content not found']);
        exit;
    }

    $content = $contentResult->fetch_assoc();
    error_log("Content found: " . print_r($content, true));
    
    // Check if already unlocked
    $stmt = $conn->prepare("SELECT id FROM unlocked_content WHERE user_id = ? AND content_id = ?");
    $stmt->bind_param("ii", $userId, $contentId);
    $stmt->execute();
    $unlockedResult = $stmt->get_result();

    if ($unlockedResult->num_rows > 0) {
        error_log("Content already unlocked for user $userId, content $contentId");
        ob_clean();
        echo json_encode([
            'success' => true,
            'message' => 'Content already unlocked',
            'youtube_id' => $content['youtube_id'],
            'surah_name' => $content['surah_name'],
            'arabic_text' => $content['arabic_text']
        ]);
        exit;
    }
    
    // Get user's current wallet balance
    $stmt = $conn->prepare("SELECT wallet_balance FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $userResult = $stmt->get_result();

    $user = $userResult->fetch_assoc();
    $currentBalance = floatval($user['wallet_balance']);
    $unlockPrice = floatval($content['unlock_price']);

    error_log("User balance: $currentBalance, Unlock price: $unlockPrice");
    
    if ($currentBalance < $unlockPrice) {
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'Insufficient wallet balance. Please fund your wallet.'
        ]);
        exit;
    }
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Deduct from wallet
        $stmt = $conn->prepare("UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?");
        $stmt->bind_param("di", $unlockPrice, $userId);
        $stmt->execute();

        // Add unlock record
        $stmt = $conn->prepare("INSERT INTO unlocked_content (user_id, content_id) VALUES (?, ?)");
        $stmt->bind_param("ii", $userId, $contentId);
        $stmt->execute();

        // Log transaction
        $description = "Unlocked: " . $content['surah_name'];
        $status = 'completed';
        $stmt = $conn->prepare("INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'content_unlock', ?, ?, ?)");
        $stmt->bind_param("idsss", $userId, -$unlockPrice, $description, $status);
        $stmt->execute();

        error_log("Transaction completed successfully for user $userId, content $contentId");
        
        // Commit transaction
        $conn->commit();
        $conn->autocommit(true);
        
        $newBalance = $currentBalance - $unlockPrice;

        ob_clean();
        echo json_encode([
            'success' => true,
            'message' => 'Content unlocked successfully',
            'new_balance' => $newBalance,
            'youtube_id' => $content['youtube_id'],
            'surah_name' => $content['surah_name'],
            'arabic_text' => $content['arabic_text']
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Unlock content error: " . $e->getMessage());
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Failed to unlock content: ' . $e->getMessage()]);
}
?> 