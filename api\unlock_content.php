<?php
/**
 * Unlock Content API for Recite! App
 * Handles content unlocking with wallet deduction
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$contentId = intval($input['content_id'] ?? 0);

if (!$contentId) {
    echo json_encode(['success' => false, 'message' => 'Invalid content ID']);
    exit;
}

try {
    $conn = getConnection();
    
    // Check if content exists
    $contentResult = executeQuery(
        "SELECT * FROM content WHERE id = ?",
        'i',
        [$contentId]
    );
    
    if ($contentResult->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Content not found']);
        exit;
    }
    
    $content = $contentResult->fetch_assoc();
    
    // Check if already unlocked
    $unlockedResult = executeQuery(
        "SELECT id FROM unlocked_content WHERE user_id = ? AND content_id = ?",
        'ii',
        [$userId, $contentId]
    );
    
    if ($unlockedResult->num_rows > 0) {
        echo json_encode([
            'success' => true, 
            'message' => 'Content already unlocked',
            'youtube_id' => $content['youtube_id'],
            'surah_name' => $content['surah_name'],
            'arabic_text' => $content['arabic_text']
        ]);
        exit;
    }
    
    // Get user's current wallet balance
    $userResult = executeQuery(
        "SELECT wallet_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    
    $user = $userResult->fetch_assoc();
    $currentBalance = floatval($user['wallet_balance']);
    $unlockPrice = floatval($content['unlock_price']);
    
    if ($currentBalance < $unlockPrice) {
        echo json_encode([
            'success' => false, 
            'message' => 'Insufficient wallet balance. Please fund your wallet.'
        ]);
        exit;
    }
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Deduct from wallet
        executeQuery(
            "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
            'di',
            [$unlockPrice, $userId]
        );
        
        // Add unlock record
        executeQuery(
            "INSERT INTO unlocked_content (user_id, content_id) VALUES (?, ?)",
            'ii',
            [$userId, $contentId]
        );
        
        // Log transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'content_unlock', ?, ?, 'completed')",
            'ids',
            [$userId, -$unlockPrice, "Unlocked: " . $content['surah_name']]
        );
        
        // Commit transaction
        $conn->commit();
        $conn->autocommit(true);
        
        $newBalance = $currentBalance - $unlockPrice;
        
        echo json_encode([
            'success' => true,
            'message' => 'Content unlocked successfully',
            'new_balance' => $newBalance,
            'youtube_id' => $content['youtube_id'],
            'surah_name' => $content['surah_name'],
            'arabic_text' => $content['arabic_text']
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Unlock content error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to unlock content']);
}
?> 