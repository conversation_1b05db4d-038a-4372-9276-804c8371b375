<?php
/**
 * Content Library Page - Full Surah Collection with Pagination
 */

require_once 'config/database.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Content Library';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Handle content unlocking
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unlock_content'])) {
    $contentId = intval($_POST['content_id']);
    $conn = getConnection();
    
    // Get content details
    $stmt = $conn->prepare("SELECT * FROM content WHERE id = ?");
    $stmt->bind_param("i", $contentId);
    $stmt->execute();
    $content = $stmt->get_result()->fetch_assoc();
    
    if ($content && $user['wallet_balance'] >= $content['unlock_price']) {
        // Deduct from wallet
        $newBalance = $user['wallet_balance'] - $content['unlock_price'];
        $updateStmt = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
        $updateStmt->bind_param("di", $newBalance, $userId);
        $updateStmt->execute();
        
        // Add to unlocked content
        $unlockStmt = $conn->prepare("INSERT INTO user_unlocked_content (user_id, content_id, unlocked_at) VALUES (?, ?, NOW())");
        $unlockStmt->bind_param("ii", $userId, $contentId);
        $unlockStmt->execute();
        
        $message = "Successfully unlocked " . $content['surah_name'] . "!";
        header('Location: content_library.php?success=' . urlencode($message));
        exit;
    } else {
        $error = "Insufficient balance to unlock this content.";
    }
}

// Pagination setup
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$perPage = 12;
$offset = ($page - 1) * $perPage;

// Get total count
$conn = getConnection();
$totalQuery = "SELECT COUNT(*) as total FROM content";
$totalResult = $conn->query($totalQuery);
$totalContent = $totalResult->fetch_assoc()['total'];
$totalPages = ceil($totalContent / $perPage);

// Get available content with pagination
$contentQuery = "SELECT * FROM content ORDER BY surah_number ASC LIMIT ? OFFSET ?";
$stmt = $conn->prepare($contentQuery);
$stmt->bind_param("ii", $perPage, $offset);
$stmt->execute();
$result = $stmt->get_result();
$availableContent = [];
while ($row = $result->fetch_assoc()) {
    $availableContent[] = $row;
}

// Get user's unlocked content
$unlockedQuery = "SELECT content_id FROM user_unlocked_content WHERE user_id = ?";
$stmt = $conn->prepare($unlockedQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$unlockedContent = [];
while ($row = $result->fetch_assoc()) {
    $unlockedContent[] = $row['content_id'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Same design system as dashboard */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .content-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .content-header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            text-align: center;
        }

        .surah-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .surah-name {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .content-body {
            padding: 1.5rem;
        }

        .content-info {
            margin-bottom: 1rem;
        }

        .price-tag {
            background: #f8f9fa;
            border: 1px solid var(--border);
            border-radius: 6px;
            padding: 0.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin: 2rem 0;
        }

        .pagination a, .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border);
            border-radius: 6px;
            text-decoration: none;
            color: var(--text);
        }

        .pagination a:hover {
            background: var(--primary);
            color: white;
        }

        .pagination .current {
            background: var(--primary);
            color: white;
        }

        /* Alert */
        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header h1 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-book"></i> Content Library</h1>
            <a href="dashboard.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_GET['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Stats -->
        <div style="text-align: center; margin: 2rem 0;">
            <h3>Qur'an Content Collection</h3>
            <p>You have unlocked <strong><?php echo count($unlockedContent); ?></strong> out of <strong><?php echo $totalContent; ?></strong> Surahs</p>
            <p>Current Balance: <strong>₦<?php echo number_format($user['wallet_balance'], 2); ?></strong></p>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <?php foreach ($availableContent as $content): ?>
                <div class="content-card">
                    <div class="content-header">
                        <div class="surah-number"><?php echo $content['surah_number']; ?></div>
                        <div class="surah-name"><?php echo htmlspecialchars($content['surah_name']); ?></div>
                    </div>
                    
                    <div class="content-body">
                        <div class="content-info">
                            <div class="price-tag">
                                <strong>₦<?php echo number_format($content['unlock_price'], 2); ?></strong>
                            </div>
                        </div>
                        
                        <?php if (in_array($content['id'], $unlockedContent)): ?>
                            <button class="btn btn-success" disabled>
                                <i class="fas fa-unlock"></i> Unlocked
                            </button>
                        <?php else: ?>
                            <form method="POST">
                                <input type="hidden" name="content_id" value="<?php echo $content['id']; ?>">
                                <input type="hidden" name="unlock_content" value="1">
                                <button type="submit" class="btn btn-primary"
                                        onclick="return confirm('Unlock <?php echo htmlspecialchars($content['surah_name']); ?> for ₦<?php echo number_format($content['unlock_price']); ?>?');">
                                    <i class="fas fa-lock"></i> Unlock for ₦<?php echo number_format($content['unlock_price']); ?>
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>

                <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?>">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
