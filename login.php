<?php
/**
 * Beautiful Login Page for Recite! App
 * Modern authentication with powerful design system
 */

// Configuration needs to be loaded first
require_once 'config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields';
    } else {
        try {
            $user = executeQuery(
                "SELECT * FROM users WHERE email = ?",
                's',
                [$email]
            )->fetch_assoc();
            
            if ($user && password_verify($password, $user['password_hash'])) {
                if (!$user['payment_verified']) {
                    $error = 'Please complete your payment to activate your account. <a href="verify_payment.php?email=' . urlencode($email) . '" class="text-decoration-none">Complete Payment</a>';
                } elseif ($user['is_blocked']) {
                    $error = 'Your account has been blocked. Please contact support.';
                } else {
                    // Successful login
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['is_admin'] = false;
                    
                    // Update last login
                    executeQuery(
                        "UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                        'i',
                        [$user['id']]
                    );
                    
                    header('Location: dashboard.php');
                    exit;
                }
            } else {
                $error = 'Invalid email or password';
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'Login failed. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>

    <!-- Fresh Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">
    
    <style>
        /* ===== AUTH PAGE STYLES - DARK GREEN THEME ===== */
        body {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-lg);
            margin: 0;
            position: relative;
        }

        /* Background Pattern */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .auth-container {
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr;
            position: relative;
            z-index: 1;
        }

        @media (min-width: 768px) {
            .auth-container {
                grid-template-columns: 1fr 1fr;
                min-height: 650px;
            }
        }

        .auth-left {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: var(--space-3xl);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .auth-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .auth-right {
            padding: var(--space-3xl);
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: var(--white);
        }

        .auth-logo {
            font-size: 4rem;
            margin-bottom: var(--space-xl);
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        .auth-brand-title {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            margin-bottom: var(--space-md);
            position: relative;
            z-index: 2;
        }

        .auth-brand-subtitle {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin-bottom: var(--space-2xl);
            position: relative;
            z-index: 2;
        }

        .auth-title {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            margin-bottom: var(--space-md);
            color: var(--gray-900);
            text-align: center;
        }

        .auth-subtitle {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            margin-bottom: var(--space-2xl);
            text-align: center;
            line-height: 1.6;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
            position: relative;
            z-index: 2;
        }

        .feature-item {
            background: rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            margin-bottom: var(--space-lg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: var(--space-lg);
            transition: var(--transition);
        }

        .feature-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(10px);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            flex-shrink: 0;
        }

        .feature-text {
            flex: 1;
        }

        .feature-text strong {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 600;
            margin-bottom: var(--space-xs);
        }

        .feature-text span {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            line-height: 1.4;
        }

        /* Form Enhancements */
        .form-group {
            margin-bottom: var(--space-xl);
        }

        .form-label {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: var(--space-md);
        }

        .form-input {
            height: 56px;
            font-size: var(--font-size-base);
            border: 2px solid var(--gray-200);
            transition: var(--transition);
        }

        .form-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(26, 95, 63, 0.1);
        }

        .remember-forgot {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-2xl);
        }

        .remember-label {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: var(--font-size-sm);
            color: var(--gray-600);
            cursor: pointer;
        }

        .forgot-link {
            font-size: var(--font-size-sm);
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        .auth-footer {
            text-align: center;
            margin-top: var(--space-2xl);
            padding-top: var(--space-xl);
            border-top: 1px solid var(--gray-200);
        }

        .auth-footer p {
            margin-bottom: var(--space-md);
            color: var(--gray-600);
        }

        .auth-footer a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .auth-footer a:hover {
            text-decoration: underline;
        }

        /* Mobile Responsive */
        @media (max-width: 767px) {
            body {
                padding: var(--space-md);
            }

            .auth-left {
                padding: var(--space-2xl) var(--space-xl);
                min-height: 300px;
            }

            .auth-right {
                padding: var(--space-2xl) var(--space-xl);
            }

            .feature-list {
                display: none;
            }

            .auth-logo {
                font-size: 3rem;
                margin-bottom: var(--space-lg);
            }

            .auth-brand-title {
                font-size: var(--font-size-2xl);
            }

            .auth-title {
                font-size: var(--font-size-2xl);
            }

            .remember-forgot {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--space-md);
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- Left Side - Branding -->
        <div class="auth-left">
            <div class="auth-logo">
                <i class="fas fa-book-quran"></i>
            </div>
            <h1 class="auth-brand-title">RECITE</h1>
            <p class="auth-brand-subtitle">Master the art of Qur'an recitation with AI-powered guidance</p>

            <ul class="feature-list">
                <li class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-microphone-alt"></i>
                    </div>
                    <div class="feature-text">
                        <strong>Voice Recognition</strong>
                        <span>AI-powered recitation analysis and feedback</span>
                    </div>
                </li>

                <li class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="feature-text">
                        <strong>Competitive Rankings</strong>
                        <span>Track your progress locally and nationally</span>
                    </div>
                </li>

                <li class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="feature-text">
                        <strong>Community Learning</strong>
                        <span>Learn together with other students</span>
                    </div>
                </li>
            </ul>
        </div>

        <!-- Right Side - Login Form -->
        <div class="auth-right">
            <div>
                <h2 class="auth-title">Welcome Back</h2>
                <p class="auth-subtitle">Sign in to continue your Qur'an recitation journey</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="alert-content">
                        <?php echo $error; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <div class="alert-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="alert-content">
                        <?php echo $success; ?>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" id="loginForm">
                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope"></i>
                        Email Address
                    </label>
                    <input type="email" class="form-input" id="email" name="email"
                           placeholder="Enter your email address" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <input type="password" class="form-input" id="password" name="password"
                           placeholder="Enter your password" required>
                </div>

                <div class="remember-forgot">
                    <label class="remember-label">
                        <input type="checkbox" class="form-checkbox" id="remember" name="remember">
                        Remember me
                    </label>
                    <a href="forgot-password.php" class="forgot-link">Forgot password?</a>
                </div>

                <button type="submit" class="btn btn-primary btn-lg btn-full">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In to Account
                </button>
            </form>

            <div class="auth-footer">
                <p>
                    Don't have an account?
                    <a href="register.php">Create account</a>
                </p>
                <p class="text-sm">
                    Admin access?
                    <a href="admin/login.php">Admin Login</a>
                </p>
            </div>
        </div>
    </div>
    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus first input
            document.getElementById('email').focus();

            // Form enhancement
            const loginForm = document.getElementById('loginForm');
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            loginForm.addEventListener('submit', function(e) {
                // Show loading state
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
                submitBtn.disabled = true;

                // Re-enable button if form submission fails (fallback)
                setTimeout(() => {
                    if (submitBtn.disabled) {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }, 15000);
            });

            // Form input enhancements
            const inputs = loginForm.querySelectorAll('.form-input');
            inputs.forEach(input => {
                // Add focus/blur effects
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                    if (this.value.trim() !== '') {
                        this.parentElement.classList.add('filled');
                    } else {
                        this.parentElement.classList.remove('filled');
                    }
                });

                // Check if already filled on page load
                if (input.value.trim() !== '') {
                    input.parentElement.classList.add('filled');
                }
            });

            // Add smooth animations
            const authContainer = document.querySelector('.auth-container');
            authContainer.style.opacity = '0';
            authContainer.style.transform = 'translateY(20px)';

            setTimeout(() => {
                authContainer.style.transition = 'all 0.6s ease';
                authContainer.style.opacity = '1';
                authContainer.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html> 