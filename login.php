<?php
/**
 * Beautiful Login Page for Recite! App
 * Modern authentication with powerful design system
 */

// Configuration needs to be loaded first
require_once 'config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields';
    } else {
        try {
            $user = executeQuery(
                "SELECT * FROM users WHERE email = ?",
                's',
                [$email]
            )->fetch_assoc();
            
            if ($user && password_verify($password, $user['password_hash'])) {
                if (!$user['payment_verified']) {
                    $error = 'Please complete your payment to activate your account. <a href="verify_payment.php?email=' . urlencode($email) . '" class="text-decoration-none">Complete Payment</a>';
                } elseif ($user['is_blocked']) {
                    $error = 'Your account has been blocked. Please contact support.';
                } else {
                    // Successful login
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['is_admin'] = false;
                    
                    // Update last login
                    executeQuery(
                        "UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                        'i',
                        [$user['id']]
                    );
                    
                    header('Location: dashboard.php');
                    exit;
                }
            } else {
                $error = 'Invalid email or password';
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'Login failed. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>

    <!-- Fresh Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">
    
    <style>
        /* Auth Page Specific Styles */
        body {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-md);
            margin: 0;
        }

        .auth-container {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            padding: var(--space-2xl);
        }

        .auth-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }

        .auth-logo {
            font-size: var(--font-size-3xl);
            color: var(--primary);
            margin-bottom: var(--space-lg);
        }

        .auth-title {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            margin-bottom: var(--space-md);
            color: var(--gray-900);
        }

        .auth-subtitle {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            margin-bottom: var(--space-xl);
        }





    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-book-quran"></i>
            </div>
            <h1 class="auth-title">Welcome Back</h1>
            <p class="auth-subtitle">
                Sign in to continue your recitation journey
            </p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <form method="POST" id="loginForm">
            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input type="email" class="form-input" id="email" name="email"
                       placeholder="Enter your email address" required
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i> Password
                </label>
                <input type="password" class="form-input" id="password" name="password"
                       placeholder="Enter your password" required>
            </div>

            <div class="form-group">
                <label class="form-checkbox-label">
                    <input type="checkbox" name="remember" id="remember">
                    <span class="checkmark"></span>
                    Remember me
                </label>
            </div>

            <button type="submit" class="btn btn-primary btn-lg w-full">
                <i class="fas fa-sign-in-alt"></i>
                Sign In to Account
            </button>
        </form>

        <div class="text-center mt-8">
            <p class="text-gray-600">
                Don't have an account?
                <a href="register.php" class="text-primary font-medium">Create account</a>
            </p>
            <p class="text-gray-600 mt-4">
                <a href="forgot-password.php" class="text-primary font-medium">Forgot your password?</a>
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Form enhancement
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner loading"></i> Signing In...';
            submitBtn.disabled = true;

            // Re-enable button if form submission fails
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 10000);
        });

        // Auto-focus email input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });
    </script>
</body>
</html> 