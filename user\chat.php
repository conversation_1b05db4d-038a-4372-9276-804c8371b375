<?php
$page_title = 'Community Chat';
require_once __DIR__ . '/../components/user_header.php';

// Fetch chat messages
$stmt = $pdo->query("
    SELECT m.id, m.message, m.created_at, u.username, u.profile_pic
    FROM messages m
    JOIN users u ON m.user_id = u.id
    ORDER BY m.created_at ASC
    LIMIT 100
");
$messages = $stmt->fetchAll();
?>

<div class="chat-container">
    <div class="chat-header">
        <h4 class="mb-0">Community Chat</h4>
        <small class="text-white-50">Talk with fellow reciters</small>
    </div>
    <div class="chat-messages" id="chat-messages">
        <?php foreach ($messages as $message): ?>
            <div class="chat-message <?php echo ($message['username'] == $user['username']) ? 'sent' : 'received'; ?>">
                <img src="<?php echo htmlspecialchars($message['profile_pic'] ?? '/assets/images/default-avatar.png'); ?>" class="chat-avatar">
                <div class="message-content">
                    <div class="message-info">
                        <span class="message-user"><?php echo htmlspecialchars($message['username']); ?></span>
                        <span class="message-time"><?php echo date('h:i A', strtotime($message['created_at'])); ?></span>
                    </div>
                    <div class="message-text"><?php echo htmlspecialchars($message['message']); ?></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <div class="chat-input-area">
        <form id="chat-form">
            <input type="hidden" name="user_id" value="<?php echo $user_id; ?>">
            <input type="text" id="message-input" name="message" class="form-control" placeholder="Type your message...">
            <button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane"></i></button>
        </form>
    </div>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>
<script>
// Basic chat functionality (would need AJAX for real-time)
const chatForm = document.getElementById('chat-form');
const messageInput = document.getElementById('message-input');
const chatMessages = document.getElementById('chat-messages');

chatForm.addEventListener('submit', function(e) {
    e.preventDefault();
    const messageText = messageInput.value.trim();
    if (messageText) {
        // This is a placeholder for sending message via AJAX
        // For now, we just add it to the UI
        const sentMessage = `
            <div class="chat-message sent">
                <img src="<?php echo htmlspecialchars($user['profile_pic'] ?? '/assets/images/default-avatar.png'); ?>" class="chat-avatar">
                <div class="message-content">
                    <div class="message-info">
                        <span class="message-user">You</span>
                        <span class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                    </div>
                    <div class="message-text">${messageText}</div>
                </div>
            </div>`;
        chatMessages.innerHTML += sentMessage;
        messageInput.value = '';
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
});
chatMessages.scrollTop = chatMessages.scrollHeight;
</script>
<style>
.chat-container {
    max-width: 900px;
    margin: 0 auto;
    background: var(--dark-surface);
    border-radius: 1rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
}
.chat-header {
    padding: 1.25rem;
    background: rgba(0,0,0,0.2);
    border-bottom: 1px solid var(--dark-border);
}
.chat-messages {
    flex-grow: 1;
    padding: 1.25rem;
    overflow-y: auto;
}
.chat-message {
    display: flex;
    margin-bottom: 1rem;
    align-items: flex-end;
}
.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 1rem;
}
.message-content {
    max-width: 75%;
}
.message-info {
    margin-bottom: 0.25rem;
}
.message-user {
    font-weight: bold;
    color: var(--primary-light);
}
.message-time {
    font-size: 0.75rem;
    color: var(--gray-400);
    margin-left: 0.5rem;
}
.message-text {
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    background: var(--dark-bg);
    color: var(--dark-text);
}

/* Sent messages */
.chat-message.sent {
    flex-direction: row-reverse;
}
.chat-message.sent .chat-avatar {
    margin-right: 0;
    margin-left: 1rem;
}
.chat-message.sent .message-text {
    background: var(--primary);
    color: white;
    border-bottom-right-radius: 0.25rem;
}
/* Received messages */
.chat-message.received .message-text {
    border-bottom-left-radius: 0.25rem;
}
.chat-input-area {
    padding: 1rem;
    background: rgba(0,0,0,0.2);
    border-top: 1px solid var(--dark-border);
}
.chat-input-area form {
    display: flex;
}
.chat-input-area .form-control {
    background: var(--dark-bg);
    border-color: var(--dark-border);
    color: white;
}
</style>

</rewritten_file>