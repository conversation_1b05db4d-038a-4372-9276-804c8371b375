<?php
/**
 * YouTube Video Player Component
 * Used in the dashboard for displaying admin-selected recitation videos
 */

// Default video if none provided
$defaultVideoId = 'dQw4w9WgXcQ'; // Default YouTube video
$videoTitle = $videoData['title'] ?? 'Qur\'an Recitation';
$videoId = $videoData['youtube_id'] ?? $videoData['video_id'] ?? $defaultVideoId;
$videoDescription = $videoData['description'] ?? '';
$reciter = $videoData['reciter'] ?? '';
?>

<div class="video-player-container">
    <div class="card h-100">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0">
                <i class="fas fa-play-circle me-2"></i>
                <?php echo htmlspecialchars($videoTitle); ?>
            </h6>
            <?php if ($reciter): ?>
                <small class="text-light">
                    <i class="fas fa-microphone me-1"></i>
                    Reciter: <?php echo htmlspecialchars($reciter); ?>
                </small>
            <?php endif; ?>
        </div>
        
        <div class="card-body p-0">
            <div class="video-wrapper">
                <iframe 
                    id="youtube-player"
                    width="100%" 
                    height="300"
                    src="https://www.youtube.com/embed/<?php echo htmlspecialchars($videoId); ?>?enablejsapi=1&rel=0&modestbranding=1"
                    frameborder="0" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                </iframe>
            </div>
            
            <?php if ($videoDescription): ?>
                <div class="p-3">
                    <h6>Description:</h6>
                    <p class="text-muted small mb-0"><?php echo htmlspecialchars($videoDescription); ?></p>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="video-controls">
                    <button id="playBtn" class="btn btn-primary btn-sm">
                        <i class="fas fa-play"></i> Play
                    </button>
                    <button id="pauseBtn" class="btn btn-warning btn-sm" style="display:none;">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                </div>
                <div class="video-status">
                    <span id="videoStatus" class="badge bg-secondary">Ready</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.video-player-container {
    height: 100%;
}

.video-wrapper {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

@media (max-width: 768px) {
    .video-wrapper {
        padding-bottom: 75%; /* More square on mobile */
    }
    
    .video-wrapper iframe {
        height: 250px;
        position: static;
        padding-bottom: 0;
    }
}
</style>

<script>
// YouTube Player API
let player;
let isPlayerReady = false;

// Load YouTube API
function loadYouTubeAPI() {
    if (!window.YT) {
        const tag = document.createElement('script');
        tag.src = 'https://www.youtube.com/iframe_api';
        const firstScriptTag = document.getElementsByTagName('script')[0];
        firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    } else {
        onYouTubeIframeAPIReady();
    }
}

// Called when YouTube API is ready
function onYouTubeIframeAPIReady() {
    player = new YT.Player('youtube-player', {
        events: {
            'onReady': onPlayerReady,
            'onStateChange': onPlayerStateChange
        }
    });
}

function onPlayerReady(event) {
    isPlayerReady = true;
    updateVideoStatus('Ready to play');
    
    // Enable controls
    document.getElementById('playBtn').disabled = false;
}

function onPlayerStateChange(event) {
    const playBtn = document.getElementById('playBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    
    switch (event.data) {
        case YT.PlayerState.UNSTARTED:
            updateVideoStatus('Not started');
            showPlayButton();
            break;
        case YT.PlayerState.ENDED:
            updateVideoStatus('Video ended');
            showPlayButton();
            // Trigger video end event for speech analysis
            if (window.speechAnalyzer && window.speechAnalyzer.setTargetText) {
                setTimeout(() => {
                    const targetText = getVideoTranscript();
                    if (targetText) {
                        window.speechAnalyzer.setTargetText(targetText);
                        updateVideoStatus('Ready for recitation');
                    }
                }, 1000);
            }
            break;
        case YT.PlayerState.PLAYING:
            updateVideoStatus('Playing');
            showPauseButton();
            break;
        case YT.PlayerState.PAUSED:
            updateVideoStatus('Paused');
            showPlayButton();
            break;
        case YT.PlayerState.BUFFERING:
            updateVideoStatus('Loading...');
            break;
        case YT.PlayerState.CUED:
            updateVideoStatus('Ready');
            showPlayButton();
            break;
    }
}

function showPlayButton() {
    document.getElementById('playBtn').style.display = 'inline-block';
    document.getElementById('pauseBtn').style.display = 'none';
}

function showPauseButton() {
    document.getElementById('playBtn').style.display = 'none';
    document.getElementById('pauseBtn').style.display = 'inline-block';
}

function updateVideoStatus(status) {
    const statusElement = document.getElementById('videoStatus');
    if (statusElement) {
        statusElement.textContent = status;
        statusElement.className = 'badge ' + getStatusClass(status);
    }
}

function getStatusClass(status) {
    switch (status.toLowerCase()) {
        case 'playing':
            return 'bg-success';
        case 'paused':
            return 'bg-warning';
        case 'loading...':
        case 'buffering':
            return 'bg-info';
        case 'video ended':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

function getVideoTranscript() {
    // This would typically come from the database or API
    // For now, return a sample Arabic text
    return "بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ";
}

// Button event handlers
document.addEventListener('DOMContentLoaded', function() {
    // Play button
    document.getElementById('playBtn').addEventListener('click', function() {
        if (isPlayerReady && player) {
            player.playVideo();
        }
    });
    
    // Pause button
    document.getElementById('pauseBtn').addEventListener('click', function() {
        if (isPlayerReady && player) {
            player.pauseVideo();
        }
    });
    
    // Load YouTube API
    loadYouTubeAPI();
});

// Public methods for external access
window.videoPlayer = {
    play: function() {
        if (isPlayerReady && player) {
            player.playVideo();
        }
    },
    pause: function() {
        if (isPlayerReady && player) {
            player.pauseVideo();
        }
    },
    stop: function() {
        if (isPlayerReady && player) {
            player.stopVideo();
        }
    },
    getCurrentTime: function() {
        if (isPlayerReady && player) {
            return player.getCurrentTime();
        }
        return 0;
    },
    getDuration: function() {
        if (isPlayerReady && player) {
            return player.getDuration();
        }
        return 0;
    },
    isReady: function() {
        return isPlayerReady;
    }
};
</script> 