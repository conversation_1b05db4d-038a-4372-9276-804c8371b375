<?php
/**
 * Simple Paystack API Test
 */

require_once 'config/database.php';

echo "<h2>Paystack API Test</h2>";
echo "<p><strong>Public Key:</strong> " . PAYSTACK_PUBLIC_KEY . "</p>";
echo "<p><strong>Secret Key:</strong> " . substr(PAYSTACK_SECRET_KEY, 0, 10) . "..." . substr(PAYSTACK_SECRET_KEY, -10) . "</p>";

// Test API connection
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'Authorization: Bearer ' . PAYSTACK_SECRET_KEY,
            'Cache-Control: no-cache',
            'Content-Type: application/json'
        ],
        'timeout' => 30
    ]
]);

echo "<h3>Testing API Connection...</h3>";

// Test with a known invalid reference to see if we get proper API response
$test_reference = "test_12345_invalid";
$response = file_get_contents("https://api.paystack.co/transaction/verify/" . $test_reference, false, $context);

if ($response === false) {
    echo "<div style='color: red;'>❌ API Connection Failed</div>";
    $headers = get_headers("https://api.paystack.co/transaction/verify/" . $test_reference);
    echo "<p><strong>Response Headers:</strong></p>";
    echo "<pre>" . print_r($headers, true) . "</pre>";
} else {
    $result = json_decode($response, true);
    echo "<div style='color: green;'>✅ API Connection Successful</div>";
    echo "<p><strong>API Response:</strong></p>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";
    
    if (isset($result['message']) && $result['message'] === 'Transaction not found') {
        echo "<div style='color: blue;'>✅ This is expected - the test reference doesn't exist, but API is working!</div>";
    }
}

// Test your actual payment reference
if (isset($_GET['ref'])) {
    $your_reference = $_GET['ref'];
    echo "<h3>Testing Your Payment Reference: " . htmlspecialchars($your_reference) . "</h3>";
    
    $response = file_get_contents("https://api.paystack.co/transaction/verify/" . $your_reference, false, $context);
    
    if ($response === false) {
        echo "<div style='color: red;'>❌ Failed to verify your payment</div>";
    } else {
        $result = json_decode($response, true);
        echo "<div style='color: green;'>✅ Got response for your payment</div>";
        echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";
    }
}

echo "<p><a href='?ref=REG_1_319544558'>Test with your payment reference</a></p>";
?> 