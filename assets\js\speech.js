class SpeechAnalyzer {
    constructor(options = {}) {
        this.targetText = '';
        this.currentIndex = 0;
        this.words = [];
        this.attempts = {};
        this.maxAttempts = 3;
        this.isListening = false;
        this.isCompleted = false;
        this.currentRecitationId = null;
        
        // Configuration
        this.language = options.language || 'ar-SA';
        this.continuous = options.continuous || true;
        this.interimResults = options.interimResults || true;
        
        // UI elements
        this.textContainer = document.getElementById('analysisText');
        this.micButton = document.getElementById('micButton');
        this.statusDisplay = document.getElementById('statusDisplay');
        this.scoreDisplay = document.getElementById('scoreDisplay');
        this.progressBar = document.getElementById('progressBar');
        
        // Initialize speech recognition
        this.initializeSpeechRecognition();
        
        // Bind events
        this.bindEvents();
        
        console.log('SpeechAnalyzer initialized');
    }
    
    initializeSpeechRecognition() {
        // Check for speech recognition support
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            this.showError('Speech recognition not supported in this browser. Please use Chrome or Edge.');
            return;
        }
        
        // Initialize speech recognition
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        // Configure recognition
        this.recognition.lang = this.language;
        this.recognition.continuous = this.continuous;
        this.recognition.interimResults = this.interimResults;
        this.recognition.maxAlternatives = 3;
        
        // Set up event handlers
        this.recognition.onstart = () => {
            this.isListening = true;
            this.updateStatus('🎤 Listening... Speak clearly in Arabic', 'success');
            this.micButton.classList.add('recording');
            this.micButton.innerHTML = '<i class="fas fa-stop"></i> Stop';
            console.log('Speech recognition started');
        };
        
        this.recognition.onend = () => {
            this.isListening = false;
            this.updateStatus('Stopped listening. Click the microphone to continue.', 'info');
            this.micButton.classList.remove('recording');
            this.micButton.innerHTML = '<i class="fas fa-microphone"></i> Start';
            console.log('Speech recognition ended');
        };
        
        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.isListening = false;
            this.micButton.classList.remove('recording');
            this.micButton.innerHTML = '<i class="fas fa-microphone"></i> Start';
            
            switch (event.error) {
                case 'no-speech':
                    this.updateStatus('No speech detected. Please speak clearly and try again.', 'warning');
                    break;
                case 'network':
                    this.updateStatus('Network error. Please check your internet connection.', 'danger');
                    break;
                case 'not-allowed':
                    this.updateStatus('Microphone access denied. Please allow microphone access and refresh the page.', 'danger');
                    break;
                case 'audio-capture':
                    this.updateStatus('No microphone found. Please connect a microphone.', 'danger');
                    break;
                default:
                    this.updateStatus(`Speech recognition error: ${event.error}`, 'danger');
            }
        };
        
        this.recognition.onresult = (event) => {
            this.processResults(event);
        };
    }
    
    bindEvents() {
        if (this.micButton) {
            this.micButton.addEventListener('click', () => {
                this.toggleListening();
            });
        }
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (event.code === 'Space' && event.ctrlKey) {
                event.preventDefault();
                this.toggleListening();
            }
        });
    }
    
    setTargetText(text, recitationId = null) {
        this.targetText = text.trim();
        this.words = this.tokenizeArabicText(this.targetText);
        this.currentIndex = 0;
        this.attempts = {};
        this.isCompleted = false;
        this.currentRecitationId = recitationId;
        
        this.renderText();
        this.updateProgress();
        this.updateStatus('Ready to start speech analysis. Click the microphone to begin.', 'info');
        
        console.log('Target text set:', this.targetText);
        console.log('Words to analyze:', this.words);
    }
    
    tokenizeArabicText(text) {
        // Remove diacritics for comparison but keep original for display
        const cleanText = text.replace(/[\u064B-\u0652\u0670\u06D6-\u06ED]/g, '');
        
        // Split into words, removing empty strings and extra whitespace
        const words = cleanText.split(/\s+/).filter(word => word.length > 0);
        
        // Store both clean and original versions
        return words.map((word, index) => {
            const originalWords = text.split(/\s+/);
            return {
                clean: word,
                original: originalWords[index] || word,
                index: index
            };
        });
    }
    
    toggleListening() {
        if (this.isCompleted) {
            this.updateStatus('Analysis already completed! Start a new session to continue.', 'info');
            return;
        }
        
        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }
    
    startListening() {
        if (!this.recognition) {
            this.showError('Speech recognition not available. Please refresh the page.');
            return;
        }
        
        if (this.words.length === 0) {
            this.updateStatus('No text loaded for analysis. Please select a video first.', 'warning');
            return;
        }
        
        try {
            this.recognition.start();
        } catch (error) {
            console.error('Error starting recognition:', error);
            this.updateStatus('Failed to start speech recognition. Please try again.', 'danger');
        }
    }
    
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }
    
    processResults(event) {
        let interimTranscript = '';
        let finalTranscript = '';
        
        // Process all results
        for (let i = event.resultIndex; i < event.results.length; i++) {
            const result = event.results[i];
            const transcript = result[0].transcript.trim();
            
            if (result.isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }
        
        // Update UI with interim results
        if (interimTranscript) {
            this.updateStatus(`🎤 Hearing: "${interimTranscript}"`, 'info');
        }
        
        // Process final results
        if (finalTranscript) {
            this.analyzeSpokenText(finalTranscript);
        }
    }
    
    analyzeSpokenText(spokenText) {
        console.log('Analyzing spoken text:', spokenText);
        
        const spokenWords = this.tokenizeArabicText(spokenText);
        let matchedWords = 0;
        let currentWordIndex = this.currentIndex;
        
        // Compare spoken words with target words starting from current position
        for (const spokenWord of spokenWords) {
            if (currentWordIndex >= this.words.length) break;
            
            const targetWord = this.words[currentWordIndex];
            const similarity = this.calculateSimilarity(spokenWord.clean, targetWord.clean);
            
            console.log(`Comparing "${spokenWord.clean}" with "${targetWord.clean}" - Similarity: ${similarity}`);
            
            if (similarity > 0.7) { // 70% similarity threshold
                this.markWordCorrect(currentWordIndex);
                matchedWords++;
                currentWordIndex++;
                this.updateStatus(`✅ Correct: "${targetWord.original}"`, 'success');
            } else {
                this.handleWordError(currentWordIndex, spokenWord.clean, targetWord.clean);
                break; // Stop on first error to allow correction
            }
        }
        
        this.currentIndex = currentWordIndex;
        this.updateProgress();
        this.renderText();
        
        // Check if completed
        if (this.currentIndex >= this.words.length) {
            this.completeAnalysis();
        } else if (matchedWords > 0) {
            // Continue listening for next words
            setTimeout(() => {
                if (!this.isCompleted && this.isListening) {
                    this.updateStatus(`Continue with next word: "${this.words[this.currentIndex].original}"`, 'info');
                }
            }, 1000);
        }
    }
    
    calculateSimilarity(word1, word2) {
        // Remove all diacritics and normalize
        const clean1 = this.normalizeArabicText(word1);
        const clean2 = this.normalizeArabicText(word2);
        
        // Use Levenshtein distance for similarity
        const distance = this.levenshteinDistance(clean1, clean2);
        const maxLength = Math.max(clean1.length, clean2.length);
        
        return maxLength === 0 ? 1 : 1 - (distance / maxLength);
    }
    
    normalizeArabicText(text) {
        return text
            .replace(/[\u064B-\u0652\u0670\u06D6-\u06ED]/g, '') // Remove diacritics
            .replace(/[أإآ]/g, 'ا') // Normalize Alif
            .replace(/[ؤ]/g, 'و') // Normalize Waw
            .replace(/[ئ]/g, 'ي') // Normalize Ya
            .replace(/[ة]/g, 'ه') // Normalize Ta Marbuta
            .trim();
    }
    
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    markWordCorrect(index) {
        if (!this.attempts[index]) {
            this.attempts[index] = { attempts: 0, correct: true, deduction: 0 };
        }
        this.attempts[index].correct = true;
        
        console.log(`Word ${index} marked correct:`, this.words[index].original);
    }
    
    handleWordError(index, spokenWord, targetWord) {
        if (!this.attempts[index]) {
            this.attempts[index] = { attempts: 0, correct: false, deduction: 0 };
        }
        
        this.attempts[index].attempts++;
        
        // Calculate deduction based on attempt number
        const deduction = this.attempts[index].attempts;
        this.attempts[index].deduction = deduction;
        
        console.log(`Word ${index} error (attempt ${this.attempts[index].attempts}):`, {
            spoken: spokenWord,
            target: targetWord,
            deduction: deduction
        });
        
        // Show error feedback with specific guidance
        const targetOriginal = this.words[index].original;
        const attemptsRemaining = this.maxAttempts - this.attempts[index].attempts;
        
        if (attemptsRemaining > 0) {
            this.updateStatus(
                `❌ Try again: "${targetOriginal}" (Attempt ${this.attempts[index].attempts}/${this.maxAttempts}) - ${attemptsRemaining} attempts remaining`,
                'warning'
            );
        } else {
            this.updateStatus(`❌ Max attempts reached for "${targetOriginal}". Moving to next word. (-${deduction} points)`, 'danger');
            this.currentIndex++; // Move to next word
            this.attempts[index].correct = false;
        }
    }
    
    renderText() {
        if (!this.textContainer) return;
        
        let html = '<div class="arabic-text" dir="rtl">';
        
        this.words.forEach((wordObj, index) => {
            let className = 'word';
            let statusIcon = '';
            let deductionText = '';
            
            if (index < this.currentIndex) {
                // Past words
                if (this.attempts[index] && this.attempts[index].correct) {
                    className += ' word-correct';
                    statusIcon = '<span class="status-icon correct">✓</span>';
                } else {
                    className += ' word-error';
                    const deduction = this.attempts[index]?.deduction || 0;
                    statusIcon = `<span class="status-icon error">✗</span>`;
                    deductionText = deduction > 0 ? `<span class="deduction">-${deduction}</span>` : '';
                }
            } else if (index === this.currentIndex) {
                // Current word
                className += ' word-current';
                statusIcon = '<span class="status-icon current">→</span>';
            } else {
                // Future words
                className += ' word-pending';
            }
            
            html += `<span class="${className}" data-index="${index}">
                        ${wordObj.original}
                        ${statusIcon}
                        ${deductionText}
                     </span> `;
        });
        
        html += '</div>';
        this.textContainer.innerHTML = html;
    }
    
    updateProgress() {
        if (!this.progressBar) return;
        
        const progress = this.words.length > 0 ? (this.currentIndex / this.words.length) * 100 : 0;
        this.progressBar.style.width = `${progress}%`;
        this.progressBar.setAttribute('aria-valuenow', progress);
        
        const progressText = document.querySelector('.progress-text');
        if (progressText) {
            progressText.textContent = `${this.currentIndex}/${this.words.length} words (${Math.round(progress)}%)`;
        }
    }
    
    updateStatus(message, type = 'info') {
        if (!this.statusDisplay) return;
        
        this.statusDisplay.className = `alert alert-${type} mb-3`;
        this.statusDisplay.innerHTML = `<i class="fas fa-${this.getStatusIcon(type)} me-2"></i>${message}`;
        
        console.log(`Status (${type}):`, message);
    }
    
    getStatusIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'danger': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            case 'info': default: return 'info-circle';
        }
    }
    
    completeAnalysis() {
        this.isCompleted = true;
        this.stopListening();
        
        // Calculate final score
        const results = this.calculateFinalScore();
        
        // Update UI
        this.updateStatus(`🎉 Analysis completed! Accuracy: ${results.accuracy}% - Score: ${results.score}/${results.totalWords}`, 'success');
        this.displayScore(results);
        
        // Save results
        this.saveResults(results);
        
        // Trigger completion event
        this.onComplete(results);
        
        console.log('Analysis completed:', results);
    }
    
    calculateFinalScore() {
        let correctWords = 0;
        let totalDeductions = 0;
        
        this.words.forEach((word, index) => {
            if (this.attempts[index] && this.attempts[index].correct) {
                correctWords++;
            }
            if (this.attempts[index]) {
                totalDeductions += this.attempts[index].deduction || 0;
            }
        });
        
        const accuracy = this.words.length > 0 ? (correctWords / this.words.length) * 100 : 0;
        const totalWords = this.words.length;
        const score = Math.max(0, totalWords - totalDeductions);
        const pointsEarned = accuracy >= 100 ? 1 : 0; // 1 point for perfect recitation
        
        return {
            accuracy: Math.round(accuracy * 100) / 100,
            correctWords,
            totalWords,
            totalDeductions,
            score,
            pointsEarned,
            attempts: this.attempts,
            completedAt: new Date().toISOString()
        };
    }
    
    displayScore(results) {
        if (!this.scoreDisplay) return;
        
        this.scoreDisplay.innerHTML = `
            <div class="score-summary">
                <h5><i class="fas fa-chart-line me-2"></i>Recitation Results</h5>
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="score-item">
                            <div class="score-value text-primary">${results.accuracy}%</div>
                            <div class="score-label">Accuracy</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="score-item">
                            <div class="score-value text-success">${results.correctWords}/${results.totalWords}</div>
                            <div class="score-label">Correct Words</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="score-item">
                            <div class="score-value text-warning">${results.totalDeductions}</div>
                            <div class="score-label">Deductions</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="score-item">
                            <div class="score-value text-info">${results.pointsEarned}</div>
                            <div class="score-label">Points Earned</div>
                        </div>
                    </div>
                </div>
                ${results.accuracy >= 100 ? 
                    '<div class="alert alert-success mt-3"><i class="fas fa-trophy me-2"></i>Perfect recitation! You earned 1 point!</div>' : 
                    '<div class="alert alert-info mt-3"><i class="fas fa-info-circle me-2"></i>Practice more to achieve 100% accuracy and earn points!</div>'
                }
            </div>
        `;
    }
    
    async saveResults(results) {
        try {
            const response = await fetch('/api/save-transcript.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recitation_id: this.currentRecitationId,
                    target_text: this.targetText,
                    results: results,
                    analysis_data: {
                        words: this.words,
                        attempts: this.attempts
                    }
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                console.log('Results saved successfully:', data);
                
                // Update user stats in UI if provided
                if (data.user_stats) {
                    this.updateUserStats(data.user_stats);
                }
                
                // Update rankings if provided
                if (data.rankings) {
                    this.updateRankings(data.rankings);
                }
                
                return data;
            } else {
                console.error('Failed to save results:', data.error);
                this.updateStatus('Results saved locally but failed to sync with server.', 'warning');
                return null;
            }
        } catch (error) {
            console.error('Error saving results:', error);
            this.updateStatus('Results saved locally but failed to sync with server.', 'warning');
            return null;
        }
    }
    
    updateUserStats(stats) {
        // Update wallet balance
        const walletElements = document.querySelectorAll('.wallet-balance');
        walletElements.forEach(el => {
            el.textContent = `₦${parseFloat(stats.wallet_balance).toFixed(2)}`;
        });
        
        // Update points balance
        const pointsElements = document.querySelectorAll('.points-balance');
        pointsElements.forEach(el => {
            el.textContent = stats.points_balance;
        });
        
        // Update recitation count
        const recitationElements = document.querySelectorAll('.recitation-count');
        recitationElements.forEach(el => {
            el.textContent = stats.total_recitations;
        });
    }
    
    updateRankings(rankings) {
        const rankingElements = {
            ward: document.querySelector('.ward-rank'),
            lgea: document.querySelector('.lgea-rank'),
            state: document.querySelector('.state-rank'),
            country: document.querySelector('.country-rank')
        };
        
        Object.entries(rankings).forEach(([type, rank]) => {
            if (rankingElements[type]) {
                rankingElements[type].textContent = `#${rank}`;
            }
        });
    }
    
    onComplete(results) {
        // Trigger screen recording completion if available
        if (window.screenRecorder && window.screenRecorder.isRecording) {
            window.screenRecorder.completeRecording(results);
        }
        
        // Dispatch custom event for other components
        const event = new CustomEvent('speechAnalysisComplete', {
            detail: { results, analyzer: this }
        });
        document.dispatchEvent(event);
    }
    
    showError(message) {
        this.updateStatus(message, 'danger');
        console.error('SpeechAnalyzer error:', message);
    }
    
    reset() {
        this.currentIndex = 0;
        this.attempts = {};
        this.isCompleted = false;
        this.stopListening();
        this.renderText();
        this.updateProgress();
        this.updateStatus('Reset complete. Ready for new analysis.', 'info');
        
        // Clear score display
        if (this.scoreDisplay) {
            this.scoreDisplay.innerHTML = '';
        }
    }
    
    destroy() {
        if (this.recognition) {
            this.recognition.stop();
            this.recognition = null;
        }
        
        // Remove event listeners
        if (this.micButton) {
            this.micButton.removeEventListener('click', this.toggleListening);
        }
        
        document.removeEventListener('keydown', this.keyboardHandler);
        
        console.log('SpeechAnalyzer destroyed');
    }
}

// Export for use
window.SpeechAnalyzer = SpeechAnalyzer;