-- Recite! Database Structure
-- Complete MySQL database for the Qur'an Recitation App

CREATE DATABASE IF NOT EXISTS recite_app;
USE recite_app;

-- Users table for authentication and user data
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    ward VARCHAR(100),
    lga VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Nigeria',
    profile_picture VARCHAR(255),
    wallet_balance DECIMAL(10,2) DEFAULT 0.00,
    points_balance INT DEFAULT 0,
    referral_code VARCHAR(20) UNIQUE,
    referred_by INT,
    is_active BOOLEAN DEFAULT FALSE,
    is_blocked BOOLEAN DEFAULT FALSE,
    payment_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (referred_by) REFERENCES users(id)
);

-- User stats table for performance metrics
CREATE TABLE user_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    total_recitations INT DEFAULT 0,
    total_score INT DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT 0.00,
    best_score INT DEFAULT 0,
    current_streak INT DEFAULT 0,
    longest_streak INT DEFAULT 0,
    total_study_time INT DEFAULT 0,
    total_videos_watched INT DEFAULT 0,
    total_content_unlocked INT DEFAULT 0,
    last_activity_date DATE,
    level_reached INT DEFAULT 1,
    badges_earned INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_stats (user_id)
);

-- Content table for Qur'an Surahs
CREATE TABLE content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    surah_number INT NOT NULL,
    surah_name VARCHAR(255) NOT NULL,
    youtube_id VARCHAR(50) NOT NULL,
    arabic_text TEXT NOT NULL,
    unlock_price DECIMAL(10,2) DEFAULT 30.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Unlocked content tracking
CREATE TABLE unlocked_content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_unlock (user_id, content_id)
);

-- Recitations table for user practice sessions
CREATE TABLE recitations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    score INT DEFAULT 100,
    final_score INT,
    words_correct INT DEFAULT 0,
    words_total INT DEFAULT 0,
    accuracy_percentage DECIMAL(5,2),
    duration_seconds INT,
    screen_recording_path VARCHAR(255),
    is_completed BOOLEAN DEFAULT FALSE,
    points_awarded INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE
);

-- Streams table for user posts
CREATE TABLE streams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_type ENUM('text', 'image', 'video', 'recitation') DEFAULT 'text',
    text_content TEXT,
    media_path VARCHAR(255),
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    like_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,
    share_count INT DEFAULT 0,
    is_reported BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Stream interactions (likes, comments, shares)
CREATE TABLE stream_interactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stream_id INT NOT NULL,
    user_id INT NOT NULL,
    interaction_type ENUM('like', 'comment', 'share') NOT NULL,
    comment_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_like (stream_id, user_id, interaction_type)
);

-- Transactions table for financial records
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    transaction_type ENUM('payment', 'unlock', 'interaction', 'withdrawal', 'point_buy', 'point_sell', 'referral', 'bonus') NOT NULL,
    amount DECIMAL(10,2),
    points INT DEFAULT 0,
    description TEXT,
    reference_id VARCHAR(100),
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Rankings table for leaderboard
CREATE TABLE rankings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    ward_rank INT,
    lga_rank INT,
    state_rank INT,
    country_rank INT,
    total_score INT DEFAULT 0,
    total_recitations INT DEFAULT 0,
    streak_count INT DEFAULT 0,
    last_recitation_date DATE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_rank (user_id)
);

-- Referrals table
CREATE TABLE referrals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    points_awarded INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Withdrawals table
CREATE TABLE withdrawals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    points_converted INT NOT NULL,
    bank_name VARCHAR(100),
    account_number VARCHAR(20),
    account_name VARCHAR(255),
    status ENUM('pending', 'approved', 'completed', 'rejected') DEFAULT 'pending',
    maturity_date DATE,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Reports table for content moderation
CREATE TABLE reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reporter_id INT NOT NULL,
    stream_id INT NOT NULL,
    reason TEXT,
    status ENUM('pending', 'reviewed', 'resolved') DEFAULT 'pending',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE
);

-- Admin logs table
CREATE TABLE admin_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_username VARCHAR(100),
    action VARCHAR(255),
    target_user_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WebRTC Signaling Table
CREATE TABLE call_signals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    caller_id INT NOT NULL,
    callee_id INT NOT NULL,
    signal_type ENUM('offer', 'answer', 'candidate') NOT NULL,
    payload TEXT NOT NULL,
    is_processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (caller_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (callee_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Sample content data
INSERT INTO content (surah_number, surah_name, youtube_id, arabic_text) VALUES
(1, 'Al-Fatiha', 'F1qYCkOy6E8', 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ الرَّحْمَٰنِ الرَّحِيمِ مَالِكِ يَوْمِ الدِّينِ إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ'),
(2, 'Al-Baqarah (Excerpt)', 'pZ12_E5R3qc', 'الم ذَٰلِكَ الْكِتَابُ لَا رَيْبَ فِيهِ هُدًى لِّلْمُتَّقِينَ الَّذِينَ يُؤْمِنُونَ بِالْغَيْبِ وَيُقِيمُونَ الصَّلَاةَ وَمِمَّا رَزَقْنَاهُمْ يُنفِقُونَ'),
(112, 'Al-Ikhlas', 'kppJBF4dPQY', 'قُلْ هُوَ اللَّهُ أَحَدٌ اللَّهُ الصَّمَدُ لَمْ يَلِدْ وَلَمْ يُولَدْ وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ');

-- Indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_referral_code ON users(referral_code);
CREATE INDEX idx_unlocked_content_user ON unlocked_content(user_id);
CREATE INDEX idx_recitations_user ON recitations(user_id);
CREATE INDEX idx_streams_user ON streams(user_id);
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_rankings_user ON rankings(user_id); 