<?php
require_once 'config.php';

echo "<h1>Initializing Sample Data</h1>";

// Create admin user
$admin_exists = $conn->query("SELECT id FROM users WHERE email = '<EMAIL>'")->num_rows > 0;

if (!$admin_exists) {
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, phone, country, state, lgea, ward, is_admin, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 'active', NOW())");
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt->bind_param("ssssssss", 
        $admin_name = "Admin User",
        $admin_email = "<EMAIL>", 
        $admin_password,
        $admin_phone = "08012345678",
        $admin_country = "Nigeria",
        $admin_state = "FCT",
        $admin_lgea = "Abuja",
        $admin_ward = "Ward 1"
    );
    $stmt->execute();
    echo "✓ Admin created: <EMAIL> / admin123<br>";
}

// Create sample video
$video_exists = $conn->query("SELECT id FROM videos WHERE title = 'Sample Recitation'")->num_rows > 0;

if (!$video_exists) {
    $stmt = $conn->prepare("INSERT INTO videos (title, url, description, reciter, category, status, uploaded_at) VALUES (?, ?, ?, ?, ?, 'active', NOW())");
    $stmt->bind_param("sssss", 
        $title = "Sample Recitation",
        $url = "https://www.youtube.com/watch?v=DkS1pkIJ7u8",
        $description = "Sample Qur'an recitation for testing",
        $reciter = "Sheikh Abdul Rahman",
        $category = "Test"
    );
    $stmt->execute();
    echo "✓ Sample video created<br>";
}

echo "<h2>✅ Initialization Complete!</h2>";
echo "<p>Admin: <EMAIL> / admin123</p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Data Initialization</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="container mt-4">
</body>
</html> 