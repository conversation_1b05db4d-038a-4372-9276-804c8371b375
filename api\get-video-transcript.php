<?php
require_once '../config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'User not authenticated']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$videoId = intval($_GET['video_id'] ?? 1);

try {
    $conn = getConnection();
    
    // Get video details and transcript
    $stmt = $conn->prepare("SELECT * FROM videos WHERE id = ? AND is_active = 1");
    $stmt->bind_param("i", $videoId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Video not found']);
        exit;
    }
    
    $video = $result->fetch_assoc();
    $stmt->close();
    $conn->close();
    
    // Sample Arabic transcript for recitation practice
    // In a real application, this would be stored in the database
    $sampleTranscripts = [
        1 => "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
        2 => "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ الرَّحْمَٰنِ الرَّحِيمِ مَالِكِ يَوْمِ الدِّينِ",
        3 => "إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ",
        4 => "صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ"
    ];
    
    $transcript = $sampleTranscripts[$videoId] ?? $sampleTranscripts[1];
    
    // Success response
    echo json_encode([
        'success' => true,
        'video' => [
            'id' => $video['id'],
            'title' => $video['title'],
            'reciter' => $video['reciter'],
            'youtube_id' => $video['youtube_id'],
            'transcript' => $transcript,
            'category' => $video['category']
        ]
    ]);
    
} catch (Exception $e) {
    logError("Get video transcript error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch video data'
    ]);
}
?> 