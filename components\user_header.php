<?php
/**
 * User Header Component for Recite! App
 * Manages session, database connection, and user data for protected pages
 */

// Configuration needs to be loaded first to set any session ini settings
require_once __DIR__ . '/../config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Global variables for user data
$user = null;
$user_stats = null;

// Check if user is logged in and fetch their data
if (isLoggedIn()) {
    $conn = getConnection(); // Use the singleton connection
    
    $userId = $_SESSION['user_id'];
    
    // Fetch user details
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
    }
    
    // Fetch user stats
    $user_stats = null;
    $stats_stmt = $conn->prepare("SELECT * FROM user_stats WHERE user_id = ?");
    if($stats_stmt) {
        $stats_stmt->bind_param("i", $_SESSION['user_id']);
        $stats_stmt->execute();
        $stats_result = $stats_stmt->get_result();
        $user_stats = $stats_result->fetch_assoc();
        $stats_stmt->close();
        
        // If no stats exist, create default stats
        if(!$user_stats) {
            // First check if the user exists in the users table
            $user_check_stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
            if($user_check_stmt) {
                $user_check_stmt->bind_param("i", $_SESSION['user_id']);
                $user_check_stmt->execute();
                $user_check_result = $user_check_stmt->get_result();
                
                if($user_check_result->num_rows > 0) {
                    // User exists, try to create stats
                    $create_stats_stmt = $conn->prepare("INSERT INTO user_stats (user_id, total_recitations, total_score, average_score, best_score, current_streak, longest_streak, total_study_time, total_videos_watched, total_content_unlocked, last_activity_date, level_reached, badges_earned) VALUES (?, 0, 0, 0.00, 0, 0, 0, 0, 0, 0, CURDATE(), 1, 0)");
                    if($create_stats_stmt) {
                        $create_stats_stmt->bind_param("i", $_SESSION['user_id']);
                        try {
                            $create_stats_stmt->execute();
                            $create_stats_stmt->close();
                            
                            // Fetch the newly created stats
                            $stats_stmt = $conn->prepare("SELECT * FROM user_stats WHERE user_id = ?");
                            if($stats_stmt) {
                                $stats_stmt->bind_param("i", $_SESSION['user_id']);
                                $stats_stmt->execute();
                                $stats_result = $stats_stmt->get_result();
                                $user_stats = $stats_result->fetch_assoc();
                                $stats_stmt->close();
                            }
                        } catch (mysqli_sql_exception $e) {
                            // If stats creation fails, we'll use default values
                            $create_stats_stmt->close();
                        }
                    }
                } else {
                    // User doesn't exist, redirect to login
                    header('Location: ' . BASE_URL . '/login.php');
                    exit();
                }
                $user_check_stmt->close();
            }
        }
    }
    
    // Ensure user_stats has default values even if creation fails
    if(!$user_stats) {
        $user_stats = [
            'total_recitations' => 0,
            'total_score' => 0,
            'average_score' => 0.00,
            'best_score' => 0,
            'current_streak' => 0,
            'longest_streak' => 0,
            'total_study_time' => 0,
            'total_videos_watched' => 0,
            'total_content_unlocked' => 0,
            'level_reached' => 1,
            'badges_earned' => 0
        ];
    }
    // Don't close the connection here as it's managed by getConnection() static variable

} else {
    // If not logged in, redirect to login
    $script_name = basename($_SERVER['SCRIPT_NAME']);
    $public_pages = ['login.php', 'register.php', 'index.php'];
    if (!in_array($script_name, $public_pages)) {
        if (strpos($_SERVER['REQUEST_URI'], '/user/') !== false) {
            header('Location: ../login.php');
            exit();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? 'User Dashboard'; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo BASE_URL; ?>/assets/css/design-system.css" rel="stylesheet">
    <style>
        /* Dark Red Theme Variables */
        :root {
            --primary-red: #B10020;
            --primary-red-light: #D32F2F;
            --primary-red-dark: #8B0000;
            --white: #FFFFFF;
            --dark-text: #1E1E1E;
            --subtle-gray: #F3F3F3;
            --light-gray: #E8E8E8;
            --sidebar-width: 280px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--white);
            color: var(--dark-text);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }
        
        /* Sidebar Overlay for Mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .sidebar-overlay.active {
            display: block;
            opacity: 1;
        }
        
        /* Sidebar */
        .user-sidebar {
            width: var(--sidebar-width);
            background: var(--white);
            border-right: 1px solid var(--light-gray);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 999;
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: 2px 0 20px rgba(177, 0, 32, 0.1);
        }
        
        /* Main Content Area - Fixed positioning */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            background: var(--white);
            transition: margin-left 0.3s ease;
            position: relative;
            width: calc(100% - var(--sidebar-width));
            overflow-x: hidden;
        }
        
        /* Header - No sticky positioning */
        .main-header {
            background: var(--white);
            border-bottom: 1px solid var(--light-gray);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: relative;
            z-index: 10;
        }

        /* Content Wrapper */
        .content-wrapper {
            padding: 0;
            min-height: calc(100vh - 80px);
            background: var(--white);
        }
        
        /* Sidebar Header */
        .sidebar-header {
            padding: 2rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
            color: white;
            text-align: center;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            font-size: 1.4rem;
            font-weight: 700;
            color: white;
        }
        
        .logo i {
            font-size: 1.8rem;
        }
        
        /* Navigation */
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: var(--dark-text);
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 0.25rem 0.75rem;
            border-radius: 12px;
            font-weight: 500;
            position: relative;
        }
        
        .nav-link i {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }
        
        .nav-link:hover {
            background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
            color: white;
            transform: translateX(6px);
            box-shadow: 0 4px 15px rgba(177, 0, 32, 0.2);
        }
        
        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
            color: white;
            transform: translateX(4px);
            box-shadow: 0 4px 15px rgba(177, 0, 32, 0.2);
        }
        
        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--white);
            border-radius: 0 4px 4px 0;
        }
        
        /* Sidebar Footer */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem;
            border-top: 1px solid var(--light-gray);
            background: var(--white);
        }
        
        /* Content Wrapper - No extra padding at top */
        .content-wrapper {
            padding: 0 2rem 2rem 2rem;
            max-width: 100%;
            width: 100%;
            position: relative;
        }
        
        /* Header Elements */
        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .header-left h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-text);
            margin: 0;
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--light-gray);
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .user-name {
            font-weight: 600;
            color: var(--dark-text);
        }
        
        /* Mobile Menu Button */
        .mobile-menu-button {
            display: none;
            background: var(--primary-red);
            color: white;
            border: none;
            font-size: 1.25rem;
            padding: 0.75rem;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(177, 0, 32, 0.2);
        }
        
        .mobile-menu-button:hover {
            background: var(--primary-red-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(177, 0, 32, 0.3);
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .user-sidebar {
                transform: translateX(-100%);
                width: 280px;
                z-index: 1001;
            }

            .user-sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
                width: 100% !important;
                position: relative;
            }

            .mobile-menu-button {
                display: block;
            }

            .main-header {
                padding: 1rem 1.5rem;
                position: relative;
                z-index: 10;
            }

            .content-wrapper {
                padding: 0;
                min-height: calc(100vh - 70px);
            }

            .header-left h1 {
                font-size: 1.25rem;
            }

            .user-menu .user-name {
                display: none;
            }
        }
        
        /* Tablet Responsive */
        @media (max-width: 1024px) and (min-width: 769px) {
            .main-content {
                margin-left: var(--sidebar-width);
                width: calc(100% - var(--sidebar-width));
            }
        }

        @media (max-width: 480px) {
            .user-sidebar {
                width: 100vw;
                z-index: 1001;
            }

            .main-header {
                padding: 0.75rem 1rem;
            }

            .content-wrapper {
                padding: 0;
                min-height: calc(100vh - 65px);
            }
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>
        
        <!-- User Sidebar -->
        <div class="user-sidebar" id="userSidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-book-quran"></i>
                    <span>ReciteApp</span>
                </div>
            </div>
            <nav class="sidebar-nav">
                <a href="<?php echo BASE_URL; ?>/dashboard.php" class="nav-link <?= (isset($page_title) && $page_title == 'Dashboard') ? 'active' : '' ?>">
                    <i class="fas fa-tachometer-alt"></i><span>Dashboard</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/streams.php" class="nav-link <?= (isset($page_title) && $page_title == 'Streams') ? 'active' : '' ?>">
                    <i class="fas fa-video"></i><span>Streams</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/rankings.php" class="nav-link <?= (isset($page_title) && $page_title == 'Rankings') ? 'active' : '' ?>">
                    <i class="fas fa-trophy"></i><span>Rankings</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/community.php" class="nav-link <?= (isset($page_title) && $page_title == 'Community') ? 'active' : '' ?>">
                    <i class="fas fa-users"></i><span>Community</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/wallet.php" class="nav-link <?= (isset($page_title) && $page_title == 'Wallet') ? 'active' : '' ?>">
                    <i class="fas fa-wallet"></i><span>Wallet</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/profile.php" class="nav-link <?= (isset($page_title) && $page_title == 'Profile') ? 'active' : '' ?>">
                    <i class="fas fa-user-circle"></i><span>Profile</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/chat.php" class="nav-link <?= (isset($page_title) && $page_title == 'Chat') ? 'active' : '' ?>">
                    <i class="fas fa-comments"></i><span>Chat</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="<?php echo BASE_URL; ?>/logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i><span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Main Header -->
            <div class="main-header">
                <div class="header-left">
                    <button class="mobile-menu-button" id="mobileMenuBtn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1><?php echo $page_title ?? 'Dashboard'; ?></h1>
                </div>
                <div class="header-right">
                     <?php if ($user): ?>
                        <div class="user-menu">
                            <img src="<?php echo BASE_URL . htmlspecialchars($user['profile_picture'] ?? '/assets/images/default-avatar.png'); ?>" alt="User Avatar" class="user-avatar">
                            <span class="user-name"><?php echo htmlspecialchars($user['full_name'] ?? 'Guest'); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Content Wrapper -->
            <div class="content-wrapper">

            <div class="content-wrapper">
                <script>
                    // Mobile Menu Toggle - Inline for immediate availability
                    document.addEventListener('DOMContentLoaded', function() {
                        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
                        const userSidebar = document.getElementById('userSidebar');
                        const sidebarOverlay = document.getElementById('sidebarOverlay');
                        
                        if (mobileMenuBtn && userSidebar && sidebarOverlay) {
                            mobileMenuBtn.addEventListener('click', function() {
                                userSidebar.classList.toggle('active');
                                sidebarOverlay.classList.toggle('active');
                            });
                            
                            sidebarOverlay.addEventListener('click', function() {
                                userSidebar.classList.remove('active');
                                sidebarOverlay.classList.remove('active');
                            });
                            
                            // Close sidebar when clicking nav links on mobile
                            const navLinks = userSidebar.querySelectorAll('.nav-link');
                            navLinks.forEach(link => {
                                link.addEventListener('click', function() {
                                    if (window.innerWidth <= 768) {
                                        userSidebar.classList.remove('active');
                                        sidebarOverlay.classList.remove('active');
                                    }
                                });
                            });
                        }
                    });
                </script>
            </div> <!-- End content-wrapper -->
        </div>
    </div>
</body>
</html>