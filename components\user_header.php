<?php
/**
 * User Header Component for Recite! App
 * Manages session, database connection, and user data for protected pages
 */

// Configuration needs to be loaded first to set any session ini settings
require_once __DIR__ . '/../config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Global variables for user data
$user = null;
$user_stats = null;

// Check if user is logged in and fetch their data
if (isLoggedIn()) {
    $conn = getConnection(); // Use the singleton connection
    
    $userId = $_SESSION['user_id'];
    
    // Fetch user details
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
    }
    
    // Fetch user stats
    $user_stats = null;
    $stats_stmt = $conn->prepare("SELECT * FROM user_stats WHERE user_id = ?");
    if($stats_stmt) {
        $stats_stmt->bind_param("i", $_SESSION['user_id']);
        $stats_stmt->execute();
        $stats_result = $stats_stmt->get_result();
        $user_stats = $stats_result->fetch_assoc();
        $stats_stmt->close();
        
        // If no stats exist, create default stats
        if(!$user_stats) {
            // First check if the user exists in the users table
            $user_check_stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
            if($user_check_stmt) {
                $user_check_stmt->bind_param("i", $_SESSION['user_id']);
                $user_check_stmt->execute();
                $user_check_result = $user_check_stmt->get_result();
                
                if($user_check_result->num_rows > 0) {
                    // User exists, try to create stats
                    $create_stats_stmt = $conn->prepare("INSERT INTO user_stats (user_id, total_recitations, total_score, average_score, best_score, current_streak, longest_streak, total_study_time, total_videos_watched, total_content_unlocked, last_activity_date, level_reached, badges_earned) VALUES (?, 0, 0, 0.00, 0, 0, 0, 0, 0, 0, CURDATE(), 1, 0)");
                    if($create_stats_stmt) {
                        $create_stats_stmt->bind_param("i", $_SESSION['user_id']);
                        try {
                            $create_stats_stmt->execute();
                            $create_stats_stmt->close();
                            
                            // Fetch the newly created stats
                            $stats_stmt = $conn->prepare("SELECT * FROM user_stats WHERE user_id = ?");
                            if($stats_stmt) {
                                $stats_stmt->bind_param("i", $_SESSION['user_id']);
                                $stats_stmt->execute();
                                $stats_result = $stats_stmt->get_result();
                                $user_stats = $stats_result->fetch_assoc();
                                $stats_stmt->close();
                            }
                        } catch (mysqli_sql_exception $e) {
                            // If stats creation fails, we'll use default values
                            $create_stats_stmt->close();
                        }
                    }
                } else {
                    // User doesn't exist, redirect to login
                    header('Location: ' . BASE_URL . '/login.php');
                    exit();
                }
                $user_check_stmt->close();
            }
        }
    }
    
    // Ensure user_stats has default values even if creation fails
    if(!$user_stats) {
        $user_stats = [
            'total_recitations' => 0,
            'total_score' => 0,
            'average_score' => 0.00,
            'best_score' => 0,
            'current_streak' => 0,
            'longest_streak' => 0,
            'total_study_time' => 0,
            'total_videos_watched' => 0,
            'total_content_unlocked' => 0,
            'level_reached' => 1,
            'badges_earned' => 0
        ];
    }
    // Don't close the connection here as it's managed by getConnection() static variable

} else {
    // If not logged in, redirect to login
    $script_name = basename($_SERVER['SCRIPT_NAME']);
    $public_pages = ['login.php', 'register.php', 'index.php'];
    if (!in_array($script_name, $public_pages)) {
        if (strpos($_SERVER['REQUEST_URI'], '/user/') !== false) {
            header('Location: ../login.php');
            exit();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? 'User Dashboard'; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo BASE_URL; ?>/assets/css/design-system.css" rel="stylesheet">
    <style>
        /* Dark Red Theme Variables */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --primary-dark: #0f3d28;
            --primary-hover: #164d34;
            --white: #FFFFFF;
            --dark-text: #1E1E1E;
            --subtle-gray: #F3F3F3;
            --light-gray: #E8E8E8;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --sidebar-width: 280px;
            --bottom-nav-height: 80px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--white);
            color: var(--dark-text);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }
        
        /* Hide Sidebar - Replace with Bottom Navigation */
        .sidebar-overlay,
        .user-sidebar {
            display: none !important;
        }

        /* Bottom Navigation Bar */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--bottom-nav-height);
            background: var(--white);
            border-top: 1px solid var(--gray-200);
            box-shadow: 0 -2px 20px rgba(26, 95, 63, 0.1);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 8px;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--gray-600);
            transition: all 0.2s ease;
            padding: 8px 4px;
            border-radius: 12px;
            min-width: 60px;
            position: relative;
        }

        .bottom-nav-item:hover {
            color: var(--primary);
            background: rgba(26, 95, 63, 0.05);
        }

        .bottom-nav-item.active {
            color: var(--primary);
            background: rgba(26, 95, 63, 0.1);
        }

        .bottom-nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .bottom-nav-item span {
            font-size: 11px;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
        }
        
        /* Main Content Area - Full width with bottom padding */
        .main-content {
            margin-left: 0;
            min-height: 100vh;
            background: var(--white);
            position: relative;
            width: 100%;
            overflow-x: hidden;
            padding-bottom: calc(var(--bottom-nav-height) + 20px);
        }
        
        /* Header - No sticky positioning */
        .main-header {
            background: var(--white);
            border-bottom: 1px solid var(--light-gray);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: relative;
            z-index: 10;
        }


        
        /* Bottom Navigation Responsive Design */
        @media (max-width: 480px) {
            .bottom-nav-item span {
                font-size: 10px;
            }

            .bottom-nav-item i {
                font-size: 18px;
            }

            .bottom-nav {
                padding: 0 4px;
            }
        }
        
        /* Header Elements */
        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-left h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-text);
            margin: 0;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--light-gray);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-name {
            font-weight: 600;
            color: var(--dark-text);
        }
        
        /* Mobile Responsive for Header */
        @media (max-width: 768px) {
            .main-header {
                padding: 1rem 1.5rem;
            }

            .header-left h1 {
                font-size: 1.25rem;
            }

            .user-menu .user-name {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Main Content -->
        <div class="main-content">
            <!-- Main Header -->
            <div class="main-header">
                <div class="header-left">
                    <h1><?php echo $page_title ?? 'Dashboard'; ?></h1>
                </div>
                <div class="header-right">
                     <?php if ($user): ?>
                        <div class="user-menu">
                            <img src="<?php echo BASE_URL . htmlspecialchars($user['profile_picture'] ?? '/assets/images/default-avatar.png'); ?>" alt="User Avatar" class="user-avatar">
                            <span class="user-name"><?php echo htmlspecialchars($user['full_name'] ?? 'Guest'); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>


        </div>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="<?php echo BASE_URL; ?>/dashboard.php" class="bottom-nav-item <?= (isset($page_title) && $page_title == 'Dashboard') ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/streams.php" class="bottom-nav-item <?= (isset($page_title) && $page_title == 'Streams') ? 'active' : '' ?>">
                <i class="fas fa-video"></i>
                <span>Streams</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/rankings.php" class="bottom-nav-item <?= (isset($page_title) && $page_title == 'Rankings') ? 'active' : '' ?>">
                <i class="fas fa-trophy"></i>
                <span>Rankings</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/community.php" class="bottom-nav-item <?= (isset($page_title) && $page_title == 'Community') ? 'active' : '' ?>">
                <i class="fas fa-users"></i>
                <span>Community</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/wallet.php" class="bottom-nav-item <?= (isset($page_title) && $page_title == 'Wallet') ? 'active' : '' ?>">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/profile.php" class="bottom-nav-item <?= (isset($page_title) && $page_title == 'Profile') ? 'active' : '' ?>">
                <i class="fas fa-user-circle"></i>
                <span>Profile</span>
            </a>
        </nav>
    </div>
</body>
</html>