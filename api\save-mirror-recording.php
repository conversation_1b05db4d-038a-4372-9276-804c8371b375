<?php
require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $conn = getConnection();
    
    // Validate required fields
    if (!isset($_POST['title']) || empty(trim($_POST['title']))) {
        throw new Exception('Title is required');
    }
    
    if (!isset($_FILES['mirror_video']) || $_FILES['mirror_video']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Video file is required');
    }
    
    $userId = $_SESSION['user_id'];
    $title = trim($_POST['title']);
    $mirrorType = $_POST['mirror_type'] ?? 'selfie';
    $isPublic = isset($_POST['is_public']) && $_POST['is_public'] == '1';
    $recitationId = $_POST['recitation_id'] ?? null;
    
    // Validate file
    $allowedTypes = ['video/webm', 'video/mp4', 'video/avi'];
    $fileType = $_FILES['mirror_video']['type'];
    
    if (!in_array($fileType, $allowedTypes)) {
        throw new Exception('Invalid file type. Only video files are allowed.');
    }
    
    // Check file size (max 100MB)
    $maxSize = 100 * 1024 * 1024; // 100MB in bytes
    if ($_FILES['mirror_video']['size'] > $maxSize) {
        throw new Exception('File size too large. Maximum size is 100MB.');
    }
    
    // Create upload directory if not exists
    $uploadDir = '../uploads/mirrors/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            throw new Exception('Failed to create upload directory');
        }
    }
    
    // Generate unique filename
    $fileExtension = pathinfo($_FILES['mirror_video']['name'], PATHINFO_EXTENSION);
    if (empty($fileExtension)) {
        $fileExtension = 'webm'; // Default extension
    }
    $fileName = 'mirror_' . $userId . '_' . time() . '_' . uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    
    // Move uploaded file
    if (!move_uploaded_file($_FILES['mirror_video']['tmp_name'], $filePath)) {
        throw new Exception('Failed to save video file');
    }
    
    // Get file size and duration (basic estimation)
    $fileSize = filesize($filePath);
    $duration = 0; // We'll estimate or use JavaScript to get actual duration
    
    // Generate thumbnail (optional - requires FFmpeg)
    $thumbnailPath = null;
    $thumbnailDir = '../uploads/thumbnails/';
    if (!is_dir($thumbnailDir)) {
        mkdir($thumbnailDir, 0755, true);
    }
    
    // Try to generate thumbnail using FFmpeg if available
    $thumbnailName = 'thumb_' . pathinfo($fileName, PATHINFO_FILENAME) . '.jpg';
    $thumbnailFullPath = $thumbnailDir . $thumbnailName;
    
    // Basic thumbnail generation (you might want to implement this properly)
    if (function_exists('exec')) {
        $ffmpegCmd = "ffmpeg -i \"$filePath\" -ss 00:00:01 -vframes 1 \"$thumbnailFullPath\" 2>/dev/null";
        @exec($ffmpegCmd);
        if (file_exists($thumbnailFullPath)) {
            $thumbnailPath = 'uploads/thumbnails/' . $thumbnailName;
        }
    }
    
    // Insert into database
    $stmt = $conn->prepare("
        INSERT INTO mirror_recordings 
        (user_id, recitation_id, title, file_path, thumbnail_path, file_size, duration, is_public, mirror_type) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $dbFilePath = 'uploads/mirrors/' . $fileName;
    $stmt->bind_param(
        "iisssiiss", 
        $userId, 
        $recitationId, 
        $title, 
        $dbFilePath, 
        $thumbnailPath, 
        $fileSize, 
        $duration, 
        $isPublic, 
        $mirrorType
    );
    
    if (!$stmt->execute()) {
        // Delete uploaded file if database insertion fails
        unlink($filePath);
        if ($thumbnailPath && file_exists($thumbnailFullPath)) {
            unlink($thumbnailFullPath);
        }
        throw new Exception('Failed to save recording to database');
    }
    
    $recordingId = $conn->insert_id;
    
    // Update user stats
    $updateStmt = $conn->prepare("
        UPDATE users 
        SET total_recitations = total_recitations + 1 
        WHERE id = ?
    ");
    $updateStmt->bind_param("i", $userId);
    $updateStmt->execute();
    $updateStmt->close();
    
    // Award points for mirror recording (1 point)
    $pointsStmt = $conn->prepare("
        UPDATE users 
        SET points_balance = points_balance + 1 
        WHERE id = ?
    ");
    $pointsStmt->bind_param("i", $userId);
    $pointsStmt->execute();
    $pointsStmt->close();
    
    // Log transaction
    $transactionStmt = $conn->prepare("
        INSERT INTO transactions 
        (user_id, transaction_type, amount, points, description, status) 
        VALUES (?, 'earning', 0.00, 1, 'Mirror recording upload reward', 'completed')
    ");
    $transactionStmt->bind_param("i", $userId);
    $transactionStmt->execute();
    $transactionStmt->close();
    
    $stmt->close();
    $conn->close();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Mirror recording saved successfully',
        'recording_id' => $recordingId,
        'points_earned' => 1,
        'file_path' => $dbFilePath,
        'thumbnail_path' => $thumbnailPath
    ]);
    
} catch (Exception $e) {
    error_log("Mirror recording save error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 