﻿<?php
$page_title = 'Screen & Mirror Recording';
require_once __DIR__ . '/../components/user_header.php';
?>

<div class="recording-container">
    <div class="row g-4">
        <!-- Left Column: Video Player and Instructions -->
        <div class="col-lg-7">
            <div class="dashboard-card" style="position: sticky; top: 100px;">
                <div class="card-header">
                    <div class="card-icon"><i class="fas fa-video"></i></div>
                    <h4 class="card-title">Recitation Video</h4>
                </div>
                <div class="card-content">
                    <div class="video-container mb-3">
                         <!-- Placeholder for the main recitation video -->
                        <div class="placeholder-video">
                            <i class="fas fa-play-circle fa-5x text-white-50"></i>
                        </div>
                    </div>
                    <h5 class="text-white">Instructions</h5>
                    <ul class="text-white-50">
                        <li>Ensure you have good lighting and a quiet environment.</li>
                        <li>Click "Start Recording" for either Screen or Mirror.</li>
                        <li>Follow the recitation from the video.</li>
                        <li>Click "Stop Recording" when finished. Your video will be processed for scoring.</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Right Column: Recording Panels -->
        <div class="col-lg-5">
            <!-- Screen Recording Panel -->
            <div class="dashboard-card mb-4">
                <div class="card-header">
                    <div class="card-icon"><i class="fas fa-desktop"></i></div>
                    <h4 class="card-title">Screen Recording</h4>
                </div>
                <div class="card-content text-center">
                    <p class="text-white-50">Record your screen as you recite. Your webcam will be captured in a small corner overlay.</p>
                    <video id="screen-preview" class="preview-video mb-3" autoplay muted></video>
                    <div class="recording-controls">
                        <button class="btn btn-lg btn-success" id="start-screen-record">
                            <i class="fas fa-play-circle me-2"></i>Start Screen Recording
                        </button>
                         <button class="btn btn-lg btn-danger d-none" id="stop-screen-record">
                            <i class="fas fa-stop-circle me-2"></i>Stop & Submit
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mirror Recording Panel -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon"><i class="fas fa-camera-retro"></i></div>
                    <h4 class="card-title">Mirror Recording</h4>
                </div>
                <div class="card-content text-center">
                    <p class="text-white-50">Record yourself reciting using your webcam. This is great for focusing on your Tajweed and expression.</p>
                    <video id="mirror-preview" class="preview-video mb-3" autoplay muted></video>
                    <div class="recording-controls">
                         <button class="btn btn-lg btn-success" id="start-mirror-record">
                            <i class="fas fa-play-circle me-2"></i>Start Mirror Recording
                        </button>
                        <button class="btn btn-lg btn-danger d-none" id="stop-mirror-record">
                            <i class="fas fa-stop-circle me-2"></i>Stop & Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>
<script>
// Placeholder script for recording functionality
document.addEventListener('DOMContentLoaded', () => {
    // UI elements
    const startScreenBtn = document.getElementById('start-screen-record');
    const stopScreenBtn = document.getElementById('stop-screen-record');
    const screenPreview = document.getElementById('screen-preview');

    const startMirrorBtn = document.getElementById('start-mirror-record');
    const stopMirrorBtn = document.getElementById('stop-mirror-record');
    const mirrorPreview = document.getElementById('mirror-preview');

    let screenStream, mirrorStream, screenRecorder, mirrorRecorder;

    // Start Screen Recording
    startScreenBtn.addEventListener('click', async () => {
        try {
            // Get screen and camera streams
            screenStream = await navigator.mediaDevices.getDisplayMedia({ video: true, audio: true });
            const cameraStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });

            // Show preview
            screenPreview.srcObject = cameraStream; // Show user their face
            startScreenBtn.classList.add('d-none');
            stopScreenBtn.classList.remove('d-none');
            console.log('Screen recording started');
        } catch (err) {
            console.error('Error starting screen recording:', err);
            alert('Could not start screen recording. Please ensure you grant permissions.');
        }
    });

    // Stop Screen Recording
    stopScreenBtn.addEventListener('click', () => {
        screenStream.getTracks().forEach(track => track.stop());
        screenPreview.srcObject = null;
        startScreenBtn.classList.remove('d-none');
        stopScreenBtn.classList.add('d-none');
        alert('Screen recording stopped. Submitting for analysis (placeholder).');
    });

    // Start Mirror Recording
    startMirrorBtn.addEventListener('click', async () => {
        try {
            mirrorStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
            mirrorPreview.srcObject = mirrorStream;
            startMirrorBtn.classList.add('d-none');
            stopMirrorBtn.classList.remove('d-none');
            console.log('Mirror recording started');
        } catch (err) {
            console.error('Error starting mirror recording:', err);
            alert('Could not start webcam recording. Please ensure you grant permissions.');
        }
    });

    // Stop Mirror Recording
    stopMirrorBtn.addEventListener('click', () => {
        mirrorStream.getTracks().forEach(track => track.stop());
        mirrorPreview.srcObject = null;
        startMirrorBtn.classList.remove('d-none');
        stopMirrorBtn.classList.add('d-none');
        alert('Mirror recording stopped. Submitting for analysis (placeholder).');
    });
});
</script>
<style>
.video-container, .placeholder-video, .preview-video {
    position: relative;
    width: 100%;
    background-color: #000;
    border-radius: 1rem;
    overflow: hidden;
}
.video-container {
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
}
.placeholder-video {
    aspect-ratio: 16/9;
    display: flex;
    align-items: center;
    justify-content: center;
}
.preview-video {
    aspect-ratio: 16/9;
    display: block;
    margin: 0 auto;
}
</style>

</rewritten_file>