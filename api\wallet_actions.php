<?php
/**
 * Wallet Actions API for Recite! App
 * Handles all wallet operations: buy points, sell points, withdraw
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Require login
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

try {
    $conn = getConnection();
    
    switch ($action) {
        case 'buy_points':
            $points = intval($input['points'] ?? 0);
            $costPerPoint = 70; // ₦70 per point
            $totalCost = $points * $costPerPoint;
            
            if ($points <= 0) {
                throw new Exception('Invalid number of points');
            }
            
            // Check user wallet balance
            $userBalance = getUserWalletBalance($userId);
            if ($userBalance < $totalCost) {
                throw new Exception('Insufficient wallet balance. Please fund your wallet first.');
            }
            
            $conn->autocommit(false);
            
            // Deduct from wallet
            executeQuery(
                "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
                'di',
                [$totalCost, $userId]
            );
            
            // Add points
            executeQuery(
                "UPDATE users SET points_balance = points_balance + ? WHERE id = ?",
                'ii',
                [$points, $userId]
            );
            
            // Record wallet transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status, created_at) VALUES (?, 'withdrawal', ?, 'Points purchase: {$points} points', 'completed', NOW())",
                'id',
                [$userId, $totalCost]
            );
            
            // Record points transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, points, description, status, created_at) VALUES (?, 'earning', ?, 'Points purchase: {$points} points for ₦{$totalCost}', 'completed', NOW())",
                'ii',
                [$userId, $points]
            );
            
            $conn->commit();
            $conn->autocommit(true);
            
            echo json_encode([
                'success' => true,
                'message' => "Successfully purchased {$points} points for ₦" . number_format($totalCost),
                'new_wallet_balance' => getUserWalletBalance($userId),
                'new_points_balance' => getUserPointsBalance($userId)
            ]);
            break;
            
        case 'sell_points':
            $points = intval($input['points'] ?? 0);
            $pricePerPoint = 50; // ₦50 per point when selling
            $totalEarning = $points * $pricePerPoint;
            $minimumPoints = 50;
            
            if ($points < $minimumPoints) {
                throw new Exception("Minimum {$minimumPoints} points required for selling");
            }
            
            // Check user points balance
            $userPoints = getUserPointsBalance($userId);
            if ($userPoints < $points) {
                throw new Exception('Insufficient points balance');
            }
            
            $conn->autocommit(false);
            
            // Deduct points
            executeQuery(
                "UPDATE users SET points_balance = points_balance - ? WHERE id = ?",
                'ii',
                [$points, $userId]
            );
            
            // Add to wallet
            executeQuery(
                "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
                'di',
                [$totalEarning, $userId]
            );
            
            // Record points transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, points, description, status, created_at) VALUES (?, 'withdrawal', ?, 'Points sale: {$points} points for ₦{$totalEarning}', 'completed', NOW())",
                'ii',
                [$userId, $points]
            );
            
            // Record wallet transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status, created_at) VALUES (?, 'deposit', ?, 'Points sale: {$points} points', 'completed', NOW())",
                'id',
                [$userId, $totalEarning]
            );
            
            $conn->commit();
            $conn->autocommit(true);
            
            echo json_encode([
                'success' => true,
                'message' => "Successfully sold {$points} points for ₦" . number_format($totalEarning),
                'new_wallet_balance' => getUserWalletBalance($userId),
                'new_points_balance' => getUserPointsBalance($userId)
            ]);
            break;
            
        case 'withdraw_money':
            $amount = floatval($input['amount'] ?? 0);
            $bankDetails = sanitize($input['bank_details'] ?? '');
            $minimumWithdrawal = 1000; // ₦1000 minimum
            
            if ($amount < $minimumWithdrawal) {
                throw new Exception("Minimum withdrawal amount is ₦" . number_format($minimumWithdrawal));
            }
            
            if (empty($bankDetails)) {
                throw new Exception('Bank details are required');
            }
            
            // Check user wallet balance
            $userBalance = getUserWalletBalance($userId);
            if ($userBalance < $amount) {
                throw new Exception('Insufficient wallet balance');
            }
            
            $conn->autocommit(false);
            
            // Deduct from wallet
            executeQuery(
                "UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?",
                'di',
                [$amount, $userId]
            );
            
            // Create withdrawal request
            executeQuery(
                "INSERT INTO withdrawal_requests (user_id, amount, bank_details, status, created_at) VALUES (?, ?, ?, 'pending', NOW())",
                'ids',
                [$userId, $amount, $bankDetails]
            );
            
            // Record transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, amount, description, status, created_at) VALUES (?, 'withdrawal', ?, 'Bank withdrawal request: ₦{$amount}', 'pending', NOW())",
                'id',
                [$userId, $amount]
            );
            
            $conn->commit();
            $conn->autocommit(true);
            
            echo json_encode([
                'success' => true,
                'message' => "Withdrawal request of ₦" . number_format($amount) . " submitted successfully. Processing time: 3-5 business days.",
                'new_wallet_balance' => getUserWalletBalance($userId)
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    error_log("Wallet action error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Helper functions
function getUserWalletBalance($userId) {
    $result = executeQuery(
        "SELECT wallet_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    $user = $result->fetch_assoc();
    return floatval($user['wallet_balance'] ?? 0);
}

function getUserPointsBalance($userId) {
    $result = executeQuery(
        "SELECT points_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    $user = $result->fetch_assoc();
    return intval($user['points_balance'] ?? 0);
}
?> 