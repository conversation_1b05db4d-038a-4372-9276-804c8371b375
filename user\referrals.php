<?php
require_once '../config.php';
requireLogin();

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    header('Location: ../login.php');
    exit;
}

$conn = getConnection();

// Get user's referral statistics
$referralStats = [];

// Total referrals
$stmt = $conn->prepare("SELECT COUNT(*) as total_referrals FROM referrals WHERE referrer_id = ?");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$referralStats['total_referrals'] = $result->fetch_assoc()['total_referrals'];

// Successful referrals (completed registration with payment)
$stmt = $conn->prepare("SELECT COUNT(*) as successful_referrals FROM referrals WHERE referrer_id = ? AND status = 'completed'");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$referralStats['successful_referrals'] = $result->fetch_assoc()['successful_referrals'];

// Total points earned from referrals
$stmt = $conn->prepare("SELECT SUM(points_earned) as total_points FROM referrals WHERE referrer_id = ? AND status = 'completed'");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$referralStats['total_points'] = $result->fetch_assoc()['total_points'] ?? 0;

// Get detailed referral history
$stmt = $conn->prepare("
    SELECT r.*, u.full_name, u.email, u.profile_picture, u.registration_paid, u.created_at as joined_date
    FROM referrals r
    JOIN users u ON r.referred_id = u.id
    WHERE r.referrer_id = ?
    ORDER BY r.created_at DESC
    LIMIT 50
");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$referralHistory = [];
while ($row = $result->fetch_assoc()) {
    $referralHistory[] = $row;
}

// Get monthly referral statistics
$monthlyStats = [];
$stmt = $conn->prepare("
    SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as count,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful
    FROM referrals 
    WHERE referrer_id = ? 
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month DESC
    LIMIT 12
");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $monthlyStats[] = $row;
}

$stmt->close();
$conn->close();

// Generate user's referral link
$referralCode = $user['referral_code'];
$referralLink = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]" . dirname($_SERVER['REQUEST_URI']) . "/../register.php?ref=$referralCode";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referrals - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/main.css" rel="stylesheet">
    <style>
        .referral-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
        }
        .referral-link-box {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 15px;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-completed {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .chart-container {
            height: 300px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="fas fa-book-quran text-success me-2"></i>
                Qur'an Recite App
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.php">
                            <i class="fas fa-users me-1"></i>Community
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="streaming.php">
                            <i class="fas fa-video me-1"></i>Streaming
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="referrals.php">
                            <i class="fas fa-users me-1"></i>Referrals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-1"></i>Profile
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="<?php echo $user['profile_picture'] ? '../uploads/profiles/' . $user['profile_picture'] : '../assets/images/default-avatar.png'; ?>" 
                                 alt="Profile" class="rounded-circle me-2" style="width: 32px; height: 32px;">
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="wallet.php"><i class="fas fa-wallet me-2"></i>Wallet</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid" style="margin-top: 80px; padding: 20px;">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-primary">
                    <i class="fas fa-users me-2"></i>
                    Referral Dashboard
                </h2>
                <p class="text-muted">Invite friends and earn points for every successful registration</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h3 class="mb-0"><?php echo $referralStats['total_referrals']; ?></h3>
                            <small>Total Referrals</small>
                        </div>
                        <div class="col-md-3">
                            <h3 class="mb-0"><?php echo $referralStats['successful_referrals']; ?></h3>
                            <small>Successful Referrals</small>
                        </div>
                        <div class="col-md-3">
                            <h3 class="mb-0"><?php echo $referralStats['total_points']; ?></h3>
                            <small>Points Earned</small>
                        </div>
                        <div class="col-md-3">
                            <h3 class="mb-0">₦<?php echo number_format($referralStats['total_points'] * 50, 2); ?></h3>
                            <small>Est. Value (₦50/point)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Referral Link Section -->
            <div class="col-lg-8">
                <div class="card referral-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-link me-2"></i>
                            Your Referral Link
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="referral-link-box">
                            <div class="d-flex align-items-center">
                                <input type="text" class="form-control me-2" id="referralLink" 
                                       value="<?php echo $referralLink; ?>" readonly>
                                <button class="btn btn-primary" onclick="copyReferralLink()">
                                    <i class="fas fa-copy me-1"></i>Copy
                                </button>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6>How it Works:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Share your unique referral link</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Friend registers and pays ₦1000</li>
                                    <li><i class="fas fa-check text-success me-2"></i>You earn 1 point automatically</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Convert points to money anytime</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Share Options:</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success btn-sm" onclick="shareWhatsApp()">
                                        <i class="fab fa-whatsapp me-1"></i>Share on WhatsApp
                                    </button>
                                    <button class="btn btn-info btn-sm" onclick="shareTwitter()">
                                        <i class="fab fa-twitter me-1"></i>Share on Twitter
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="shareFacebook()">
                                        <i class="fab fa-facebook me-1"></i>Share on Facebook
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Referral History -->
                <div class="card referral-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Referral History
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($referralHistory)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No referrals yet</h6>
                                <p class="text-muted">Start sharing your referral link to earn points!</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Status</th>
                                            <th>Points Earned</th>
                                            <th>Date Joined</th>
                                            <th>Date Referred</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($referralHistory as $referral): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo $referral['profile_picture'] ? '../uploads/profiles/' . $referral['profile_picture'] : '../assets/images/default-avatar.png'; ?>" 
                                                             alt="Profile" class="rounded-circle me-2" style="width: 30px; height: 30px;">
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($referral['full_name']); ?></strong>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($referral['email']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge status-<?php echo $referral['status']; ?>">
                                                        <?php echo ucfirst($referral['status']); ?>
                                                    </span>
                                                    <?php if ($referral['registration_paid']): ?>
                                                        <br><small class="text-success">
                                                            <i class="fas fa-check-circle me-1"></i>Paid
                                                        </small>
                                                    <?php else: ?>
                                                        <br><small class="text-warning">
                                                            <i class="fas fa-clock me-1"></i>Pending Payment
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">+<?php echo $referral['points_earned']; ?> points</span>
                                                </td>
                                                <td>
                                                    <small><?php echo date('M j, Y', strtotime($referral['joined_date'])); ?></small>
                                                </td>
                                                <td>
                                                    <small><?php echo timeAgo($referral['created_at']); ?></small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Monthly Statistics -->
            <div class="col-lg-4">
                <div class="card referral-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Monthly Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($monthlyStats)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No monthly data available</p>
                            </div>
                        <?php else: ?>
                            <div class="chart-container">
                                <?php foreach ($monthlyStats as $stat): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <strong><?php echo date('M Y', strtotime($stat['month'] . '-01')); ?></strong>
                                            <br><small class="text-muted"><?php echo $stat['successful']; ?> successful</small>
                                        </div>
                                        <div class="text-end">
                                            <div class="progress" style="width: 100px; height: 8px;">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: <?php echo min(100, ($stat['successful'] / max(1, $stat['count'])) * 100); ?>%"></div>
                                            </div>
                                            <small class="text-muted"><?php echo $stat['count']; ?> total</small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card referral-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="copyReferralLink()">
                                <i class="fas fa-copy me-1"></i>
                                Copy Referral Link
                            </button>
                            <a href="wallet.php" class="btn btn-success">
                                <i class="fas fa-coins me-1"></i>
                                Convert Points to Money
                            </a>
                            <button class="btn btn-info" onclick="shareWhatsApp()">
                                <i class="fab fa-whatsapp me-1"></i>
                                Share on WhatsApp
                            </button>
                            <a href="profile.php" class="btn btn-secondary">
                                <i class="fas fa-user me-1"></i>
                                Update Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyReferralLink() {
            const linkInput = document.getElementById('referralLink');
            linkInput.select();
            linkInput.setSelectionRange(0, 99999);
            navigator.clipboard.writeText(linkInput.value).then(function() {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'toast show position-fixed top-0 end-0 m-3';
                toast.innerHTML = `
                    <div class="toast-header">
                        <strong class="me-auto">Success</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        Referral link copied to clipboard!
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 3000);
            });
        }

        function shareWhatsApp() {
            const message = encodeURIComponent(`Join me on Qur'an Recite App - Learn and earn while improving your Qur'anic recitation! Register now: ${document.getElementById('referralLink').value}`);
            window.open(`https://wa.me/?text=${message}`, '_blank');
        }

        function shareTwitter() {
            const message = encodeURIComponent(`Join me on Qur'an Recite App - Learn and earn while improving your Qur'anic recitation! ${document.getElementById('referralLink').value}`);
            window.open(`https://twitter.com/intent/tweet?text=${message}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(document.getElementById('referralLink').value);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }
    </script>
</body>
</html> 