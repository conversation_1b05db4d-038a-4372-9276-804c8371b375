<?php
/**
 * Save Recitation API for Recite! App
 * Handles saving recitation results and updating rankings
 */

require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!validateCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$contentId = intval($input['content_id'] ?? 0);
$score = intval($input['score'] ?? 0);
$wordsCorrect = intval($input['words_correct'] ?? 0);
$wordsTotal = intval($input['words_total'] ?? 0);
$accuracyPercentage = floatval($input['accuracy_percentage'] ?? 0);

if (!$contentId || $wordsTotal <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid recitation data']);
    exit;
}

try {
    $conn = getConnection();
    $conn->autocommit(false);
    
    try {
        // Save recitation record
        executeQuery(
            "INSERT INTO recitations (user_id, content_id, score, final_score, words_correct, words_total, accuracy_percentage, is_completed, points_awarded) 
             VALUES (?, ?, 100, ?, ?, ?, ?, 1, ?)",
            'iiiiidi',
            [$userId, $contentId, $score, $wordsCorrect, $wordsTotal, $accuracyPercentage, $score >= 70 ? 1 : 0]
        );
        
        $recitationId = $conn->insert_id;
        
        // Award points if performance is good
        if ($score >= 70) {
            executeQuery(
                "UPDATE users SET points_balance = points_balance + 1 WHERE id = ?",
                'i',
                [$userId]
            );
            
            // Log points transaction
            executeQuery(
                "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'bonus', 1, 'Recitation completion bonus', 'completed')",
                'i',
                [$userId]
            );
        }
        
        // Check for streak bonus (100 recitations in 72 hours)
        $streakResult = executeQuery(
            "SELECT COUNT(*) as recent_count FROM recitations 
             WHERE user_id = ? AND is_completed = 1 
             AND created_at >= DATE_SUB(NOW(), INTERVAL 72 HOUR)",
            'i',
            [$userId]
        );
        
        $streakData = $streakResult->fetch_assoc();
        $recentRecitations = intval($streakData['recent_count']);
        
        if ($recentRecitations >= 100) {
            // Check if bonus already awarded for this period
            $bonusResult = executeQuery(
                "SELECT id FROM transactions 
                 WHERE user_id = ? AND transaction_type = 'bonus' 
                 AND description = 'Streak bonus: 100 recitations in 72 hours'
                 AND created_at >= DATE_SUB(NOW(), INTERVAL 72 HOUR)",
                'i',
                [$userId]
            );
            
            if ($bonusResult->num_rows === 0) {
                // Award streak bonus
                executeQuery(
                    "UPDATE users SET points_balance = points_balance + 100 WHERE id = ?",
                    'i',
                    [$userId]
                );
                
                executeQuery(
                    "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'bonus', 100, 'Streak bonus: 100 recitations in 72 hours', 'completed')",
                    'i',
                    [$userId]
                );
            }
        }
        
        // Update rankings
        $rankings = updateUserRanking($userId);
        
        $conn->commit();
        $conn->autocommit(true);
        
        echo json_encode([
            'success' => true,
            'message' => 'Recitation saved successfully',
            'recitation_id' => $recitationId,
            'points_earned' => $score >= 70 ? 1 : 0,
            'streak_bonus' => $recentRecitations >= 100,
            'rankings' => $rankings
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Save recitation error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to save recitation']);
}

/**
 * Update user ranking and return new rankings
 */
function updateUserRanking($userId) {
    try {
        $conn = getConnection();
        
        // Get user location info
        $userResult = executeQuery(
            "SELECT ward, lga, state, country FROM users WHERE id = ?",
            'i',
            [$userId]
        );
        
        $user = $userResult->fetch_assoc();
        
        // Calculate total score and recitation count
        $statsResult = executeQuery(
            "SELECT COUNT(*) as total_recitations, AVG(final_score) as avg_score, SUM(final_score) as total_score 
             FROM recitations WHERE user_id = ? AND is_completed = 1",
            'i',
            [$userId]
        );
        
        $stats = $statsResult->fetch_assoc();
        $totalScore = intval($stats['total_score'] ?? 0);
        $totalRecitations = intval($stats['total_recitations'] ?? 0);
        
        // Calculate rankings for each location level
        $wardRank = calculateRank($conn, $totalScore, $user['ward'], 'ward');
        $lgaRank = calculateRank($conn, $totalScore, $user['lga'], 'lga');
        $stateRank = calculateRank($conn, $totalScore, $user['state'], 'state');
        $countryRank = calculateRank($conn, $totalScore, $user['country'], 'country');
        
        // Update or insert ranking record
        executeQuery(
            "INSERT INTO rankings (user_id, ward_rank, lga_rank, state_rank, country_rank, total_score, total_recitations, last_recitation_date) 
             VALUES (?, ?, ?, ?, ?, ?, ?, CURDATE())
             ON DUPLICATE KEY UPDATE 
             ward_rank = VALUES(ward_rank),
             lga_rank = VALUES(lga_rank), 
             state_rank = VALUES(state_rank),
             country_rank = VALUES(country_rank),
             total_score = VALUES(total_score),
             total_recitations = VALUES(total_recitations),
             last_recitation_date = VALUES(last_recitation_date)",
            'iiiiiii',
            [$userId, $wardRank, $lgaRank, $stateRank, $countryRank, $totalScore, $totalRecitations]
        );
        
        return [
            'ward_rank' => $wardRank,
            'lga_rank' => $lgaRank,
            'state_rank' => $stateRank,
            'country_rank' => $countryRank,
            'total_score' => $totalScore
        ];
        
    } catch (Exception $e) {
        error_log("Ranking update error: " . $e->getMessage());
        return null;
    }
}

/**
 * Calculate user rank within a location group
 */
function calculateRank($conn, $userScore, $location, $locationField) {
    if (empty($location)) {
        return 999999; // Default rank if location not set
    }
    
    $result = executeQuery(
        "SELECT COUNT(*) + 1 as rank 
         FROM rankings r 
         JOIN users u ON r.user_id = u.id 
         WHERE u.{$locationField} = ? AND r.total_score > ? AND u.is_active = 1",
        'si',
        [$location, $userScore]
    );
    
    $rank = $result->fetch_assoc();
    return intval($rank['rank']);
}
?> 