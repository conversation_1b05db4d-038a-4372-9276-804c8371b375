<?php
require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        throw new Exception('Invalid CSRF token');
    }
    
    $conn = getConnection();
    
    $recipientId = intval($_POST['recipient_id'] ?? 0);
    $message = sanitize($_POST['message'] ?? '');
    $messageType = sanitize($_POST['message_type'] ?? 'text');
    $senderId = $_SESSION['user_id'];
    
    if ($recipientId <= 0) {
        throw new Exception('Invalid recipient ID');
    }
    
    if (empty($message)) {
        throw new Exception('Message cannot be empty');
    }
    
    if (strlen($message) > 500) {
        throw new Exception('Message too long. Maximum 500 characters allowed.');
    }
    
    // Check if recipient exists and is not banned
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1 AND is_banned = 0");
    $stmt->bind_param("i", $recipientId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        throw new Exception('Recipient not found or inactive');
    }
    $stmt->close();
    
    // Insert message
    $stmt = $conn->prepare("
        INSERT INTO chat_logs (sender_id, recipient_id, message, message_type, is_read) 
        VALUES (?, ?, ?, ?, 0)
    ");
    $stmt->bind_param("iiss", $senderId, $recipientId, $message, $messageType);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to send message');
    }
    
    $messageId = $conn->insert_id;
    
    $stmt->close();
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'Message sent successfully',
        'message_id' => $messageId
    ]);
    
} catch (Exception $e) {
    error_log("Send message error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 