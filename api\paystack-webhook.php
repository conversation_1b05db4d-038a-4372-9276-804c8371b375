<?php
require_once '../config.php';

// Verify webhook signature
function verifyPaystackSignature($payload, $signature) {
    $secret = PAYSTACK_SECRET_KEY;
    $computedSignature = hash_hmac('sha512', $payload, $secret);
    return hash_equals($signature, $computedSignature);
}

// Get the payload and signature
$payload = file_get_contents('php://input');
$signature = $_SERVER['HTTP_X_PAYSTACK_SIGNATURE'] ?? '';

// Log the webhook request
logError("Paystack webhook received: " . $payload);

// Verify the signature
if (!verifyPaystackSignature($payload, $signature)) {
    logError("Invalid Paystack webhook signature");
    http_response_code(400);
    exit('Invalid signature');
}

// Parse the payload
$event = json_decode($payload, true);

if (!$event) {
    logError("Invalid JSON payload in webhook");
    http_response_code(400);
    exit('Invalid JSON');
}

// Handle different event types
switch ($event['event']) {
    case 'charge.success':
        handleChargeSuccess($event['data']);
        break;
        
    case 'charge.failed':
        handleChargeFailed($event['data']);
        break;
        
    case 'transfer.success':
        handleTransferSuccess($event['data']);
        break;
        
    case 'transfer.failed':
        handleTransferFailed($event['data']);
        break;
        
    default:
        logError("Unhandled Paystack event: " . $event['event']);
        break;
}

// Return success response
http_response_code(200);
echo 'OK';

function handleChargeSuccess($data) {
    try {
        $conn = getConnection();
        $reference = $data['reference'];
        $amount = $data['amount'] / 100; // Convert from kobo to naira
        $email = $data['customer']['email'];
        $status = $data['status'];
        
        logError("Processing successful charge: $reference for $email");
        
        // Check if this is a registration payment
        if (isset($data['metadata']['purpose']) && $data['metadata']['purpose'] === 'registration') {
            handleRegistrationPayment($reference, $amount, $email, $conn);
        }
        // Check if this is a point purchase
        elseif (isset($data['metadata']['purpose']) && $data['metadata']['purpose'] === 'points_purchase') {
            handlePointsPurchase($reference, $amount, $email, $conn);
        }
        // Check if this is a general wallet top-up
        else {
            handleWalletTopup($reference, $amount, $email, $conn);
        }
        
        $conn->close();
        
    } catch (Exception $e) {
        logError("Error handling charge success: " . $e->getMessage());
    }
}

function handleRegistrationPayment($reference, $amount, $email, $conn) {
    // Update user registration status
    $stmt = $conn->prepare("
        UPDATE users 
        SET registration_paid = 1, payment_reference = ? 
        WHERE email = ? AND registration_paid = 0
    ");
    $stmt->bind_param("ss", $reference, $email);
    
    if ($stmt->execute() && $stmt->affected_rows > 0) {
        // Get user ID
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        if ($user) {
            // Record the transaction
            $stmt = $conn->prepare("
                INSERT INTO transactions (user_id, transaction_type, amount, description, reference, status) 
                VALUES (?, 'deposit', ?, 'Registration payment via webhook', ?, 'completed')
            ");
            $stmt->bind_param("ids", $user['id'], $amount, $reference);
            $stmt->execute();
            
            // Initialize user ranking if not exists
            $stmt = $conn->prepare("
                INSERT IGNORE INTO rankings (user_id, total_score) VALUES (?, 0)
            ");
            $stmt->bind_param("i", $user['id']);
            $stmt->execute();
            
            logError("Registration payment processed for user: " . $user['id']);
        }
    }
    $stmt->close();
}

function handlePointsPurchase($reference, $amount, $email, $conn) {
    // Get user by email
    $stmt = $conn->prepare("SELECT id, points_balance FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    if ($user) {
        // Calculate points (₦70 per point)
        $pointsToAdd = floor($amount / 70);
        
        if ($pointsToAdd > 0) {
            // Update user points
            $newPointsBalance = $user['points_balance'] + $pointsToAdd;
            $stmt = $conn->prepare("UPDATE users SET points_balance = ? WHERE id = ?");
            $stmt->bind_param("ii", $newPointsBalance, $user['id']);
            $stmt->execute();
            
            // Record transaction
            $stmt = $conn->prepare("
                INSERT INTO transactions (user_id, transaction_type, amount, points, description, reference, status) 
                VALUES (?, 'point_purchase', ?, ?, 'Points purchase via webhook', ?, 'completed')
            ");
            $stmt->bind_param("idis", $user['id'], $amount, $pointsToAdd, $reference);
            $stmt->execute();
            
            logError("Points purchase processed: {$pointsToAdd} points for user {$user['id']}");
        }
    }
    $stmt->close();
}

function handleWalletTopup($reference, $amount, $email, $conn) {
    // Get user by email
    $stmt = $conn->prepare("SELECT id, wallet_balance FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    if ($user) {
        // Update wallet balance
        $newWalletBalance = $user['wallet_balance'] + $amount;
        $stmt = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
        $stmt->bind_param("di", $newWalletBalance, $user['id']);
        $stmt->execute();
        
        // Record transaction
        $stmt = $conn->prepare("
            INSERT INTO transactions (user_id, transaction_type, amount, description, reference, status) 
            VALUES (?, 'deposit', ?, 'Wallet top-up via webhook', ?, 'completed')
        ");
        $stmt->bind_param("ids", $user['id'], $amount, $reference);
        $stmt->execute();
        
        logError("Wallet top-up processed: ₦{$amount} for user {$user['id']}");
    }
    $stmt->close();
}

function handleChargeFailed($data) {
    $reference = $data['reference'];
    $email = $data['customer']['email'];
    
    logError("Payment failed for $email with reference: $reference");
    
    try {
        $conn = getConnection();
        
        // Get user by email
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        if ($user) {
            // Record failed transaction
            $stmt = $conn->prepare("
                INSERT INTO transactions (user_id, transaction_type, amount, description, reference, status) 
                VALUES (?, 'deposit', 0, 'Failed payment via webhook', ?, 'failed')
            ");
            $stmt->bind_param("is", $user['id'], $reference);
            $stmt->execute();
        }
        
        $stmt->close();
        $conn->close();
        
    } catch (Exception $e) {
        logError("Error handling charge failed: " . $e->getMessage());
    }
}

function handleTransferSuccess($data) {
    $reference = $data['reference'];
    $amount = $data['amount'] / 100; // Convert from kobo to naira
    $recipient = $data['recipient']['details']['account_number'];
    
    logError("Transfer successful: ₦{$amount} to account {$recipient} with reference: $reference");
    
    try {
        $conn = getConnection();
        
        // Update withdrawal request status
        $stmt = $conn->prepare("
            UPDATE withdrawal_requests 
            SET status = 'completed', processed_at = NOW() 
            WHERE id = ? AND status = 'processing'
        ");
        
        // Extract withdrawal ID from reference (assuming format: WD_123)
        if (preg_match('/WD_(\d+)/', $reference, $matches)) {
            $withdrawalId = $matches[1];
            $stmt->bind_param("i", $withdrawalId);
            $stmt->execute();
            
            if ($stmt->affected_rows > 0) {
                logError("Withdrawal request {$withdrawalId} marked as completed");
            }
        }
        
        $stmt->close();
        $conn->close();
        
    } catch (Exception $e) {
        logError("Error handling transfer success: " . $e->getMessage());
    }
}

function handleTransferFailed($data) {
    $reference = $data['reference'];
    $reason = $data['failure_reason'] ?? 'Unknown reason';
    
    logError("Transfer failed with reference: $reference. Reason: $reason");
    
    try {
        $conn = getConnection();
        
        // Update withdrawal request status and add notes
        $stmt = $conn->prepare("
            UPDATE withdrawal_requests 
            SET status = 'rejected', admin_notes = ? 
            WHERE id = ? AND status = 'processing'
        ");
        
        // Extract withdrawal ID from reference
        if (preg_match('/WD_(\d+)/', $reference, $matches)) {
            $withdrawalId = $matches[1];
            $stmt->bind_param("si", $reason, $withdrawalId);
            $stmt->execute();
            
            if ($stmt->affected_rows > 0) {
                // Get withdrawal details to refund user
                $stmt = $conn->prepare("SELECT user_id, amount FROM withdrawal_requests WHERE id = ?");
                $stmt->bind_param("i", $withdrawalId);
                $stmt->execute();
                $result = $stmt->get_result();
                $withdrawal = $result->fetch_assoc();
                
                if ($withdrawal) {
                    // Refund the amount to user's wallet
                    $stmt = $conn->prepare("
                        UPDATE users 
                        SET wallet_balance = wallet_balance + ? 
                        WHERE id = ?
                    ");
                    $stmt->bind_param("di", $withdrawal['amount'], $withdrawal['user_id']);
                    $stmt->execute();
                    
                    // Record refund transaction
                    $stmt = $conn->prepare("
                        INSERT INTO transactions (user_id, transaction_type, amount, description, reference, status) 
                        VALUES (?, 'deposit', ?, 'Withdrawal refund - transfer failed', ?, 'completed')
                    ");
                    $stmt->bind_param("ids", $withdrawal['user_id'], $withdrawal['amount'], $reference);
                    $stmt->execute();
                    
                    logError("Withdrawal {$withdrawalId} refunded to user {$withdrawal['user_id']}");
                }
            }
        }
        
        $stmt->close();
        $conn->close();
        
    } catch (Exception $e) {
        logError("Error handling transfer failed: " . $e->getMessage());
    }
}
?> 