You are an expert full-stack developer. Your task is to generate the complete code for a responsive web application called "Recite!". The application is a social, educational, and financial platform for users to practice reciting the Holy Qur'an.

You must generate the complete code for all files, including a PHP backend, a MySQL database structure, and an HTML/CSS/JavaScript frontend. Adhere strictly to all requirements detailed below.

I. CORE TECHNOLOGY STACK
Backend: PHP (procedural or object-oriented, but must be secure).

Database: MySQL.

Frontend: HTML5, CSS3, JavaScript (ES6+). No frontend frameworks like React or Vue are needed.

APIs: Paystack API for payments    . USER AUTHENTICATION & ONBOARDING
Registration Page:

Fields: Name, Email, Password, Ward, LGA, State.

On submission, register the user in the users table with a hashed password.

After successful database registration, trigger a Paystack payment popup for a one-time fee of ₦1000.

Use Paystack Test Key: pk_test_79ab46bc7ad4029705e3593e00dd2feb7c7cdb77.

Create a backend verify_payment.php endpoint to confirm the payment with Paystack, log the transaction, and activate the user's account.

Login Page:

Fields: Email, Password.

Backend must verify credentials against the hashed password.

Use PHP sessions to manage login state.

Logout:

A logout button must be present on all authenticated pages. It should call a logout.php script that destroys the PHP session and redirects to the login page.

IV. THE USER DASHBOARD
After login, the user lands on dashboard.html. The page must be a responsive 4-quadrant grid.

Quadrant 1: Content Library & Video Player

Initially, display a list of all available Surahs from the content table.

Each item shows "Surah Name" and a button. If the user hasn't unlocked it, the button says "Unlock (₦30)".

Clicking "Unlock" triggers a backend call to deduct ₦30 from the user's wallet and adds a record to unlocked_content.

Once unlocked, the button changes to "Start Recitation".

Clicking "Start Recitation" loads the Surah's text into Quadrant 2 and loads the associated YouTube video into this quadrant (Quadrant 1), which plays automatically.

Quadrant 2: Recitation Engine

This is the practice area. When a Surah is selected, its full Arabic text appears here.

A "Start Reciting" button begins the process.

Voice Recognition: Use the Web Speech API (lang: 'ar-SA').

Real-time Highlighting: As the user recites, compare their speech to the displayed text word-by-word.

If a word is spoken correctly, highlight it in blue.

Error Handling & Deduction:

If the user says a word that does not match the expected word, the highlighting stops.

1st attempt error: Deduct 1 mark from the starting score of 100.

2nd attempt error (on the same word): Deduct 2 marks.

3rd attempt error (on the same word): Deduct 3 marks, highlight the missed word in red, and automatically move on to the next word for the user to attempt.

Screen Recording:

Recording of the entire screen must start automatically and silently the moment the user clicks "Start Reciting".

The recording must stop automatically when the recitation is finished or stopped.

The final video file must be uploaded to the server (/uploads/screen_records/) and a new entry created in the recitations and streams tables.

Points: Upon successful completion of a recitation, award the user 1 point to their points_balance.

Quadrant 3: Selfie Mirror

A simple, continuous live feed from the user's webcam. It should not record or save anything.

Quadrant 4: Ranking

Display the user's current rank based on their total recitation scores compared to others.

Show ranks for: Ward, LGA, State, and Country.

V. THE ADMIN DASHBOARD
Create a separate, secure area (/admin/) for administration.

Admin Login: A simple login page checking against a hardcoded admin username/password.

Main View: A dashboard with overview stats: Total Users, Total Revenue (from fees), Recent Registrations, etc.

User Management:

A searchable, paginated table of all users from the users table.

Ability to View a user's full profile.

Ability to Block/Unblock a user (toggles the is_blocked flag).

Ability to manually edit a user's wallet_balance and points_balance.

Content Management (CRUD):

A page to view all Surahs in the content table.

A form to add new content (Surah Number, Name, YouTube ID, Qur'an Text). This form is protected by the password: 1@3Usazladan.

Ability to edit or delete existing content.

Stream Management:

View a feed of all posts from the streams table.

Ability to delete any post deemed inappropriate.

VI. OTHER KEY PAGES & FEATURES
Streams Page (streams.html):

An infinite-scrolling public feed of all streams posts.

Users can create a new text post, or upload a picture/video. Uploading a picture/video costs ₦1000 from the user's wallet.

Interactions (Transaction Logic):

When User A likes, comments on, or shares a post by User B:

Deduct ₦3 from User A's wallet_balance.

Add ₦1 to User B's wallet_balance.

The remaining ₦2 is the app's revenue.

Log all three transactions in the transactions table.

Users can delete their own posts. Deleting a post requires the password: 1!3usazladan. When a post is deleted, all money it generated for the user is deducted from their wallet and transferred to the app.

A "Report Post" button for users to flag content for admin review.

Wallet Page (wallet.html):

Display current wallet_balance and points_balance.

Fund Wallet: Use Paystack to add money.

Buy Points: Use wallet balance to buy points at a rate of ₦70 per point.

Sell Points: Convert points back to wallet balance at a rate of ₦50 per point.

Withdrawal: After 30 days of holding points, they can be withdrawn at a rate of ₦100 per point.

Profile Page (profile.html):

Display user's picture, name, email, location, and member since date.

Allow users to upload/change their profile picture.

Display the user's unique referral code. When a new user registers using this code's link, the referrer gets 1 point.

Display the user's recitation streak. Give a bonus of 100 points if the user completes 100 recitations within a 72-hour period.

Show a grid of the user's own recitation posts.

About & Contact Pages:

Create static about.html and contact.html pages with relevant information and a working contact form that sends an email.

VII. SECURITY & BEST PRACTICES
All database queries must use prepared statements to prevent SQL injection.

All user passwords must be securely hashed using password_hash() and verified with password_verify().

Use PHP sessions for managing user authentication state.

Perform server-side validation for all user input.

Ensure all files and folders have the correct permissions.

Please generate the complete, well-commented code for all required files in the specified project structure.