 App Concept: Qur’an Recite App
Developed by: Attanzil Universal Reciters Foundation & Universal Reciters Foundation
Founder: <PERSON><PERSON>
Supported by Scholars: Prof. <PERSON><PERSON><PERSON>, Sheikh <PERSON><PERSON>

💡 App Summary
Qur’an Recite App is a powerful all-in-one Islamic learning and earning platform that merges Qur’anic education, AI voice analysis, real-time ranking, gamified recitation, and a wallet-based reward system into a single mobile and web application — fully accessible to Muslims across Nigeria and beyond.

It is multilingual, community-based, and financially rewarding, designed to revive Qur’anic recitation using modern tools.

🔑 Core Features
🧠 1. Speech-to-Text Qur’an Recitation Evaluation
Users listen to a YouTube video (Qur’an recitation)

The app listens to user recitation via AI voice analysis

Words are highlighted:

✅ Blue if correct

❌ Red if incorrect

Users get 3 attempts per error before deduction increases (1 mark, 2, then 3)

🖥️ 2. 4-Part Interactive Dashboard
Part 1: YouTube Recitation Video (admin-controlled via password)

Part 2: Mirror Camera (selfie feedback without saving)

Part 3: Live transcription + evaluation of user speech

Part 4: Live ranking system by:

Ward

LGEA

State

Country

📼 3. Auto Screen Recording & Streaming Home Page
Starts recording automatically after recitation begins

Saves to user's profile and streaming feed

Public users can:

Watch

Like (₦3 deduction)

Comment (₦3 deduction)

Share (₦3 deduction)

Report (auto flags to admin)

🪙 4. Wallet System
₦30 deducted before each screen record (for streaming participation)

Point System:

Buy: ₦70/point

Sell: ₦50/point

Withdraw (after 30 days): ₦100/point

Point Reward:

1 point per perfect recitation

100-point streak bonus for 100 recitations in 72 hrs

🧑‍🤝‍🧑 5. Referral System
Users generate referral links

1 point earned for each successful referred registration (₦1000 via Paystack)

🧾 6. Posting and Moderation
Users can:

Post their own video or image: ₦1000 deduction

Delete their own post (with password): loss of all income earned on that post

Report inappropriate content

Admin (via master password 1@3Usazladan) can:

Remove any user

Ban user from posting

Delete content

🧑‍🎓 7. User Profile
Upload profile picture

See:

Name

Email

Wallet balance

Points

Rank by location

Streaming video history

🌐 8. Social Features
Live video call

Group call & voice rooms

1-on-1 chat and recitation practice

Real-time status of users online (by ward/state)

📄 9. About Us / Contact Us
Info about the Foundation, Founders, Scholars

Feedback form

Support & sponsorship inquiries

🔍 Target Audience
Qur’an students (youth & adults)

Madrasas & Islamic schools

Local teachers & Qur’anic circles

Parents who want digital da’wah tools

Sponsors & donors

🌍 Languages Supported
English

Hausa

Arabic

Hindi

Chinese (Simplified)

🎯 Vision
To create a global digital recitation movement powered by voice, reward, and technology, where Qur’anic literacy is revived, rewarded, and ranked — one verse at a time.

🔧 Technology Stack
Frontend: HTML5, CSS3, JS, Bootstrap

Backend: PHP, MySQL

Payment: Paystack API

Voice & Speech: Web Speech API, Google Speech-to-Text (future), AI integration

Storage: Auto screen recording to server