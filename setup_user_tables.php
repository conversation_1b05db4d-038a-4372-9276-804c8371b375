<?php
/**
 * Setup User Tables Script
 * Creates necessary tables for user registration, payment, and referrals
 */

require_once 'config/database.php';

try {
    $conn = getConnection();
    
    echo "<h2>Setting up User Registration & Payment Tables</h2>";
    
    // Create users table
    $createUsersTable = "
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VA<PERSON>HAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            ward VARCHAR(50) NOT NULL,
            lga VARCHAR(50) NOT NULL,
            state VARCHAR(50) NOT NULL,
            referral_code VARCHAR(20) UNIQUE,
            referred_by INT DEFAULT NULL,
            is_active TINYINT(1) DEFAULT 0,
            payment_verified TINYINT(1) DEFAULT 0,
            is_blocked TINYINT(1) DEFAULT 0,
            points_balance INT DEFAULT 0,
            wallet_balance DECIMAL(10,2) DEFAULT 0.00,
            profile_picture VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    
    if ($conn->query($createUsersTable)) {
        echo "✅ Users table created/verified successfully<br>";
    }
    
    // Create transactions table
    $createTransactionsTable = "
        CREATE TABLE IF NOT EXISTS transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            transaction_type ENUM('payment', 'referral', 'earning', 'withdrawal', 'deposit') NOT NULL,
            amount DECIMAL(10,2) DEFAULT 0.00,
            points INT DEFAULT 0,
            description VARCHAR(255),
            reference_id VARCHAR(100),
            status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ";
    
    if ($conn->query($createTransactionsTable)) {
        echo "✅ Transactions table created/verified successfully<br>";
    }
    
    // Create referrals table
    $createReferralsTable = "
        CREATE TABLE IF NOT EXISTS referrals (
            id INT AUTO_INCREMENT PRIMARY KEY,
            referrer_id INT NOT NULL,
            referred_id INT NOT NULL,
            points_awarded INT DEFAULT 1,
            status ENUM('pending', 'completed') DEFAULT 'completed',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ";
    
    if ($conn->query($createReferralsTable)) {
        echo "✅ Referrals table created/verified successfully<br>";
    }
    
    // Create withdrawal_requests table
    $createWithdrawalTable = "
        CREATE TABLE IF NOT EXISTS withdrawal_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            points_withdrawn INT DEFAULT 0,
            status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
            bank_details TEXT NOT NULL,
            admin_notes TEXT,
            processed_by INT DEFAULT NULL,
            processed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    
    if ($conn->query($createWithdrawalTable)) {
        echo "✅ Withdrawal requests table created/verified successfully<br>";
    }
    
    // Create a test user if no users exist
    $userCount = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    
    if ($userCount == 0) {
        $testEmail = '<EMAIL>';
        $testPassword = password_hash('password123', PASSWORD_DEFAULT);
        $testReferralCode = 'REF000001TEST';
        
        $insertTestUser = $conn->prepare("
            INSERT INTO users (full_name, email, password_hash, ward, lga, state, referral_code, is_active, payment_verified) 
            VALUES (?, ?, ?, 'Test Ward', 'Test LGA', 'Test State', ?, 1, 1)
        ");
        $fullName = 'Test User';
        $insertTestUser->bind_param("ssss", $fullName, $testEmail, $testPassword, $testReferralCode);
        
        if ($insertTestUser->execute()) {
            echo "✅ Test user created successfully<br>";
            echo "📧 Email: <EMAIL><br>";
            echo "🔑 Password: password123<br>";
        }
    }
    
    echo "<br><h3>✅ Database Setup Complete!</h3>";
    echo "<p>All necessary tables have been created and configured.</p>";
    echo "<br><a href='register.php'>Test Registration</a> | ";
    echo "<a href='login.php'>Test Login</a>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?> 