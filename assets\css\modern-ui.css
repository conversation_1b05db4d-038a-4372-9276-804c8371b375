/* Qur'an Recite App - Modern UI Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
    --primary-color: #5C4DFF;
    --accent-color: #8A00FF;
    --background: #F5F7FA;
    --white: #FFFFFF;
    --black: #000000;
    --gray-light: #E5E7EB;
    --gray-medium: #D1D5DB;
    --gray-dark: #6B7280;
    --success: #22C55E;
    --danger: #EF4444;
    --warning: #F59E0B;
    --background-light: #F9FAFB;
    --font-family: 'Inter', sans-serif;
    --radius-sm: 8px;
    --radius-lg: 16px;
    --shadow-sm: 0 4px 12px rgba(0, 0, 0, 0.05);
    --shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: 16px;
    line-height: 1.6;
    color: var(--black);
    background-color: var(--background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 24px;
    transition: var(--transition);
    border: none;
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: var(--radius-sm);
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #4F3BFF;
    color: var(--white);
    text-decoration: none;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #F3F4F6;
    color: #111827;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 18px;
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    padding: 48px 0;
    text-align: center;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 24px;
}

.hero-subtitle {
    font-size: 18px;
    margin-bottom: 32px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 32px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    margin-top: 32px;
}

.feature-card {
    text-align: center;
    padding: 32px;
}

.feature-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: var(--white);
    font-size: 24px;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
}

.auth-card {
    width: 100%;
    max-width: 400px;
    padding: 32px;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-title {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 600;
}

.auth-subtitle {
    color: var(--gray-dark);
    font-size: 14px;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--black);
    margin-bottom: 4px;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--gray-medium);
    border-radius: var(--radius-sm);
    font-size: 16px;
    background: var(--white);
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(92, 77, 255, 0.1);
}

.text-center { text-align: center; }
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--gray-dark); }
.text-danger { color: var(--danger); }

.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 16px; }
.mb-4 { margin-bottom: 24px; }
.mb-5 { margin-bottom: 32px; }

.w-100 { width: 100%; }
.d-flex { display: flex; }
.justify-content-center { justify-content: center; }

.bg-white { background-color: var(--white); }

@media (max-width: 768px) {
    .hero-title { font-size: 36px; }
    .hero-buttons { flex-direction: column; align-items: center; }
    .features-grid { grid-template-columns: 1fr; }
    .main-container { padding: 16px; }
}

/* === INTERACTIVE DASHBOARD === */
.interactive-dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 24px;
    height: calc(100vh - 120px);
    min-height: 600px;
}

.dashboard-panel {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 24px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-light);
}

.panel-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--black);
    margin: 0;
}

.panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Video Panel */
.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    margin-bottom: 16px;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: var(--radius-sm);
}

/* Mirror Camera Panel */
.camera-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    border-radius: var(--radius-sm);
    overflow: hidden;
    position: relative;
}

.camera-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scaleX(-1);
}

.camera-placeholder {
    color: var(--white);
    text-align: center;
}

/* Transcript Panel */
.transcript-container {
    flex: 1;
    padding: 16px;
    background: var(--background-light);
    border-radius: var(--radius-sm);
    overflow-y: auto;
    font-size: 18px;
    line-height: 1.8;
}

/* Rankings Panel */
.ranking-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--background-light);
    border-radius: var(--radius-sm);
    margin-bottom: 8px;
}

.ranking-label {
    font-weight: 500;
    color: var(--gray-dark);
}

.ranking-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Speech Recognition */
.word {
    padding: 2px 4px;
    margin: 1px;
    border-radius: 3px;
    transition: var(--transition);
    display: inline-block;
}

.word.correct {
    background-color: rgba(92, 77, 255, 0.1);
    border: 1px solid rgba(92, 77, 255, 0.3);
    color: var(--primary-color);
}

.word.incorrect {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: var(--danger);
}

.word.current {
    background-color: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: var(--warning);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

@media (max-width: 1200px) {
    .interactive-dashboard {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        height: auto;
    }
}
