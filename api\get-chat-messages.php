<?php
require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

try {
    $conn = getConnection();
    
    $otherUserId = intval($_GET['user_id'] ?? 0);
    $currentUserId = $_SESSION['user_id'];
    
    if ($otherUserId <= 0) {
        throw new Exception('Invalid user ID');
    }
    
    // Get chat messages between current user and other user
    $stmt = $conn->prepare("
        SELECT c.*, u.full_name as sender_name, u.profile_picture as sender_avatar
        FROM chat_logs c
        JOIN users u ON c.sender_id = u.id
        WHERE (c.sender_id = ? AND c.recipient_id = ?) 
           OR (c.sender_id = ? AND c.recipient_id = ?)
        ORDER BY c.created_at ASC
        LIMIT 100
    ");
    $stmt->bind_param("iiii", $currentUserId, $otherUserId, $otherUserId, $currentUserId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $row['time_ago'] = timeAgo($row['created_at']);
        $messages[] = $row;
    }
    
    $stmt->close();
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);
    
} catch (Exception $e) {
    error_log("Get chat messages error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch messages: ' . $e->getMessage()
    ]);
}
?> 