<?php
require_once '../config.php';
requireLogin();

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    header('Location: ../login.php');
    exit;
}

$conn = getConnection();

// Nigerian banks list (major banks)
$nigerianBanks = [
    ['code' => '044', 'name' => 'Access Bank'],
    ['code' => '014', 'name' => 'Afribank Nigeria Plc'],
    ['code' => '050', 'name' => 'Ecobank Nigeria'],
    ['code' => '070', 'name' => 'Fidelity Bank'],
    ['code' => '011', 'name' => 'First Bank of Nigeria'],
    ['code' => '214', 'name' => 'First City Monument Bank'],
    ['code' => '058', 'name' => 'Guaranty Trust Bank'],
    ['code' => '030', 'name' => 'Heritage Bank'],
    ['code' => '301', 'name' => 'Jaiz Bank'],
    ['code' => '082', 'name' => 'Keystone Bank'],
    ['code' => '076', 'name' => 'Polaris Bank'],
    ['code' => '221', 'name' => 'Stanbic IBTC Bank'],
    ['code' => '068', 'name' => 'Standard Chartered Bank'],
    ['code' => '232', 'name' => 'Sterling Bank'],
    ['code' => '100', 'name' => 'Suntrust Bank'],
    ['code' => '032', 'name' => 'Union Bank of Nigeria'],
    ['code' => '033', 'name' => 'United Bank for Africa'],
    ['code' => '215', 'name' => 'Unity Bank'],
    ['code' => '035', 'name' => 'Wema Bank'],
    ['code' => '057', 'name' => 'Zenith Bank']
];

// Handle bank verification
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'verify_account' && validateCSRFToken($_POST['csrf_token'])) {
        $bankCode = sanitize($_POST['bank_code'] ?? '');
        $accountNumber = sanitize($_POST['account_number'] ?? '');
        
        if (empty($bankCode) || empty($accountNumber)) {
            $_SESSION['error_message'] = 'Please fill in all required fields';
        } elseif (!preg_match('/^\d{10}$/', $accountNumber)) {
            $_SESSION['error_message'] = 'Account number must be exactly 10 digits';
        } else {
            try {
                // In a real implementation, you would call Paystack's Bank Account Verification API
                // For now, we'll simulate the verification
                $accountName = simulateAccountNameLookup($bankCode, $accountNumber);
                
                if ($accountName) {
                    // Store bank details
                    $stmt = $conn->prepare("
                        INSERT INTO bank_accounts (user_id, bank_code, bank_name, account_number, account_name, is_verified) 
                        VALUES (?, ?, ?, ?, ?, 1)
                        ON DUPLICATE KEY UPDATE 
                            bank_code = VALUES(bank_code),
                            bank_name = VALUES(bank_name),
                            account_number = VALUES(account_number),
                            account_name = VALUES(account_name),
                            is_verified = 1,
                            updated_at = CURRENT_TIMESTAMP
                    ");
                    
                    $bankName = '';
                    foreach ($nigerianBanks as $bank) {
                        if ($bank['code'] === $bankCode) {
                            $bankName = $bank['name'];
                            break;
                        }
                    }
                    
                    $stmt->bind_param("issss", $_SESSION['user_id'], $bankCode, $bankName, $accountNumber, $accountName);
                    
                    if ($stmt->execute()) {
                        $_SESSION['success_message'] = "Bank account verified successfully! Account Name: $accountName";
                        $_SESSION['verified_account'] = [
                            'bank_name' => $bankName,
                            'account_number' => $accountNumber,
                            'account_name' => $accountName
                        ];
                    } else {
                        $_SESSION['error_message'] = 'Failed to save bank details';
                    }
                    $stmt->close();
                } else {
                    $_SESSION['error_message'] = 'Could not verify account. Please check your bank code and account number.';
                }
            } catch (Exception $e) {
                $_SESSION['error_message'] = 'Verification failed: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'remove_account' && validateCSRFToken($_POST['csrf_token'])) {
        $stmt = $conn->prepare("DELETE FROM bank_accounts WHERE user_id = ?");
        $stmt->bind_param("i", $_SESSION['user_id']);
        if ($stmt->execute()) {
            $_SESSION['success_message'] = 'Bank account removed successfully';
        } else {
            $_SESSION['error_message'] = 'Failed to remove bank account';
        }
        $stmt->close();
    }
}

// Get user's current bank account
$userBankAccount = null;
$stmt = $conn->prepare("SELECT * FROM bank_accounts WHERE user_id = ?");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows > 0) {
    $userBankAccount = $result->fetch_assoc();
}
$stmt->close();

$conn->close();

// Simulate account name lookup (In production, use Paystack Account Verification API)
function simulateAccountNameLookup($bankCode, $accountNumber) {
    // This is a simulation. In real implementation, call Paystack API:
    // https://api.paystack.co/bank/resolve?account_number={account_number}&bank_code={bank_code}
    
    $sampleNames = [
        'Muhammad Ibrahim',
        'Aisha Abdullahi',
        'Umar Suleiman',
        'Fatima Hassan',
        'Ali Mohammed',
        'Khadija Ahmad',
        'Yusuf Garba',
        'Maryam Usman'
    ];
    
    // Simulate API delay
    usleep(500000); // 0.5 second delay
    
    // Simulate 90% success rate
    if (rand(1, 10) <= 9) {
        return $sampleNames[array_rand($sampleNames)];
    }
    
    return false;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank Verification - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/main.css" rel="stylesheet">
    <style>
        .bank-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .verified-account {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
        }
        .bank-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .loading-spinner {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="fas fa-book-quran text-success me-2"></i>
                Qur'an Recite App
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="bank-verification.php">
                            <i class="fas fa-university me-1"></i>Bank Verification
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-1"></i>Profile
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="<?php echo $user['profile_picture'] ? '../uploads/profiles/' . $user['profile_picture'] : '../assets/images/default-avatar.png'; ?>" 
                                 alt="Profile" class="rounded-circle me-2" style="width: 32px; height: 32px;">
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="wallet.php"><i class="fas fa-wallet me-2"></i>Wallet</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container" style="margin-top: 80px; padding: 20px;">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-primary">
                    <i class="fas fa-university me-2"></i>
                    Bank Account Verification
                </h2>
                <p class="text-muted">Verify your bank account to enable withdrawals</p>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <?php if ($userBankAccount && $userBankAccount['is_verified']): ?>
                    <!-- Verified Account Display -->
                    <div class="verified-account mb-4">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h5 class="mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Verified Bank Account
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-2"><strong>Bank:</strong> <?php echo htmlspecialchars($userBankAccount['bank_name']); ?></p>
                                        <p class="mb-2"><strong>Account Number:</strong> <?php echo htmlspecialchars($userBankAccount['account_number']); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-2"><strong>Account Name:</strong> <?php echo htmlspecialchars($userBankAccount['account_name']); ?></p>
                                        <p class="mb-0"><strong>Verified:</strong> <?php echo date('M j, Y', strtotime($userBankAccount['created_at'])); ?></p>
                                    </div>
                                </div>
                            </div>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="remove_account">
                                <button type="submit" class="btn btn-outline-light btn-sm" 
                                        onclick="return confirm('Are you sure you want to remove this bank account?')">
                                    <i class="fas fa-trash me-1"></i>Remove
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="wallet.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to Wallet
                        </a>
                    </div>
                <?php else: ?>
                    <!-- Bank Verification Form -->
                    <div class="card bank-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                Verify Your Bank Account
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Why verify your bank account?</strong><br>
                                Bank verification is required to process withdrawals securely. We use Paystack's bank verification service to confirm your account details.
                            </div>

                            <form method="POST" id="verificationForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="verify_account">
                                
                                <!-- Bank Selection -->
                                <div class="mb-3">
                                    <label for="bankCode" class="form-label">Select Your Bank <span class="text-danger">*</span></label>
                                    <select class="form-select" id="bankCode" name="bank_code" required>
                                        <option value="">Choose your bank...</option>
                                        <?php foreach ($nigerianBanks as $bank): ?>
                                            <option value="<?php echo $bank['code']; ?>">
                                                <?php echo htmlspecialchars($bank['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <!-- Account Number -->
                                <div class="mb-3">
                                    <label for="accountNumber" class="form-label">Account Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="accountNumber" name="account_number" 
                                           placeholder="Enter your 10-digit account number" maxlength="10" 
                                           pattern="[0-9]{10}" required>
                                    <div class="form-text">Enter exactly 10 digits</div>
                                </div>
                                
                                <!-- Account Name Preview -->
                                <div class="mb-3" id="accountNamePreview" style="display: none;">
                                    <label class="form-label">Account Name</label>
                                    <div class="form-control-plaintext bg-light rounded p-2" id="accountNameDisplay">
                                        <i class="fas fa-spinner fa-spin me-2 loading-spinner"></i>
                                        <span id="accountNameText">Verifying...</span>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <a href="wallet.php" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>Back to Wallet
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="verifyBtn">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        Verify Account
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div class="card bank-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                Need Help?
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Common Issues:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>Make sure your account number is exactly 10 digits</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Select the correct bank from the list</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Ensure your account is active and not dormant</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Security:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-lock text-primary me-2"></i>Your bank details are encrypted</li>
                                        <li><i class="fas fa-lock text-primary me-2"></i>We use Paystack for verification</li>
                                        <li><i class="fas fa-lock text-primary me-2"></i>No sensitive information is stored</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const bankCode = document.getElementById('bankCode');
            const accountNumber = document.getElementById('accountNumber');
            const accountNamePreview = document.getElementById('accountNamePreview');
            const accountNameDisplay = document.getElementById('accountNameDisplay');
            const accountNameText = document.getElementById('accountNameText');
            const loadingSpinner = document.querySelector('.loading-spinner');
            const verifyBtn = document.getElementById('verifyBtn');
            
            let verificationTimeout;
            
            function verifyAccount() {
                if (bankCode.value && accountNumber.value.length === 10) {
                    accountNamePreview.style.display = 'block';
                    loadingSpinner.style.display = 'inline-block';
                    accountNameText.textContent = 'Verifying...';
                    verifyBtn.disabled = true;
                    
                    // Simulate API call delay
                    clearTimeout(verificationTimeout);
                    verificationTimeout = setTimeout(function() {
                        // In real implementation, make AJAX call to verify account
                        // For demo, show success message
                        loadingSpinner.style.display = 'none';
                        accountNameText.textContent = 'Account verified! Submit form to save details.';
                        accountNameDisplay.classList.add('bg-success', 'text-white');
                        verifyBtn.disabled = false;
                    }, 2000);
                } else {
                    accountNamePreview.style.display = 'none';
                    verifyBtn.disabled = false;
                }
            }
            
            bankCode.addEventListener('change', verifyAccount);
            accountNumber.addEventListener('input', function() {
                // Reset display
                accountNameDisplay.classList.remove('bg-success', 'text-white');
                verifyAccount();
            });
            
            // Format account number input
            accountNumber.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/\D/g, '').substring(0, 10);
            });
        });
    </script>
</body>
</html> 