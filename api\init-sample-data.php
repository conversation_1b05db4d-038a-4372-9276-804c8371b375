<?php
require_once '../config.php';

// Check if running in development mode
if (!DEVELOPMENT_MODE) {
    die('This script can only be run in development mode');
}

// Function to generate random data
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

function generateRandomEmail() {
    $domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
    return generateRandomString(8) . '@' . $domains[array_rand($domains)];
}

function generateRandomLocation() {
    $states = ['Lagos', 'Kano', 'Rivers', 'Kaduna', 'Oyo', 'Delta', 'Sokoto', 'FCT'];
    $lgeas = ['Ikeja', 'Surulere', 'Ikorodu', 'Alimosho', 'Eti-Osa', 'Mainland', 'Island'];
    $wards = ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'];
    
    return [
        'country' => 'Nigeria',
        'state' => $states[array_rand($states)],
        'lgea' => $lgeas[array_rand($lgeas)],
        'ward' => $wards[array_rand($wards)]
    ];
}

try {
    echo "<h1>Initializing Sample Data for Qur'an Recite App</h1>";
    
    // 1. Create sample admin
    echo "<h3>Creating admin account...</h3>";
    
    $admin_exists = $conn->query("SELECT id FROM users WHERE email = '<EMAIL>'")->num_rows > 0;
    
    if (!$admin_exists) {
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, phone, country, state, lgea, ward, is_admin, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 'active', NOW())");
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt->bind_param("ssssssss", 
            $admin_name = "Admin User",
            $admin_email = "<EMAIL>", 
            $admin_password,
            $admin_phone = "***********",
            $admin_country = "Nigeria",
            $admin_state = "FCT",
            $admin_lgea = "Abuja Municipal",
            $admin_ward = "Central Ward"
        );
        $stmt->execute();
        echo "✓ Admin account created (email: <EMAIL>, password: admin123)<br>";
    } else {
        echo "✓ Admin account already exists<br>";
    }
    
    // 2. Create sample users
    echo "<h3>Creating sample users...</h3>";
    
    $user_count = $conn->query("SELECT COUNT(*) as count FROM users WHERE is_admin = 0")->fetch_assoc()['count'];
    
    if ($user_count < 20) {
        $names = [
            'Abdullah Muhammad', 'Aisha Ibrahim', 'Usman Aliyu', 'Fatima Ahmed', 
            'Ibrahim Hassan', 'Khadija Musa', 'Yusuf Bello', 'Maryam Adamu',
            'Ahmad Suleiman', 'Hafsat Garba', 'Mahmud Umar', 'Zainab Tijjani',
            'Ismail Abdullahi', 'Ruqayya Salisu', 'Mubarak Lawal', 'Safiya Yakubu',
            'Bashir Tanko', 'Halima Shehu', 'Nasir Danjuma', 'Amina Rabiu'
        ];
        
        foreach ($names as $name) {
            $existing = $conn->query("SELECT id FROM users WHERE name = '$name'")->num_rows > 0;
            if (!$existing) {
                $location = generateRandomLocation();
                $email = generateRandomEmail();
                $password = password_hash('user123', PASSWORD_DEFAULT);
                $phone = '080' . rand(10000000, 99999999);
                
                $stmt = $conn->prepare("INSERT INTO users (name, email, password, phone, country, state, lgea, ward, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())");
                $stmt->bind_param("ssssssss", 
                    $name, $email, $password, $phone,
                    $location['country'], $location['state'], $location['lgea'], $location['ward']
                );
                $stmt->execute();
                $user_id = $conn->insert_id;
                
                // Create wallet for user
                $stmt = $conn->prepare("INSERT INTO wallets (user_id, balance, points, created_at) VALUES (?, 0, ?, NOW())");
                $initial_points = rand(10, 100);
                $stmt->bind_param("ii", $user_id, $initial_points);
                $stmt->execute();
                
                echo "✓ User created: $name ($email)<br>";
            }
        }
    } else {
        echo "✓ Sample users already exist<br>";
    }
    
    // 3. Create sample videos
    echo "<h3>Creating sample videos...</h3>";
    
    $video_count = $conn->query("SELECT COUNT(*) as count FROM videos")->fetch_assoc()['count'];
    
    if ($video_count < 10) {
        $sample_videos = [
            [
                'title' => 'Surah Al-Fatiha Recitation',
                'url' => 'https://www.youtube.com/watch?v=DkS1pkIJ7u8',
                'description' => 'Beautiful recitation of Surah Al-Fatiha',
                'reciter' => 'Sheikh Abdul Rahman Al-Sudais',
                'category' => 'Short Surahs'
            ],
            [
                'title' => 'Surah Al-Baqarah (1-10)',
                'url' => 'https://www.youtube.com/watch?v=5VhjU16DbVM',
                'description' => 'First 10 verses of Surah Al-Baqarah',
                'reciter' => 'Sheikh Mishary Al-Afasy',
                'category' => 'Long Surahs'
            ],
            [
                'title' => 'Surah Al-Ikhlas',
                'url' => 'https://www.youtube.com/watch?v=ZXzpYGMKD8A',
                'description' => 'Surah Al-Ikhlas recitation',
                'reciter' => 'Sheikh Saad Al-Ghamdi',
                'category' => 'Short Surahs'
            ],
            [
                'title' => 'Surah An-Nas',
                'url' => 'https://www.youtube.com/watch?v=wB0YhOMh40Y',
                'description' => 'Surah An-Nas with beautiful voice',
                'reciter' => 'Sheikh Maher Al-Muaiqly',
                'category' => 'Short Surahs'
            ],
            [
                'title' => 'Surah Al-Falaq',
                'url' => 'https://www.youtube.com/watch?v=ySkRj24Cwlg',
                'description' => 'Protection surah recitation',
                'reciter' => 'Sheikh Ahmad Al-Ajmi',
                'category' => 'Short Surahs'
            ]
        ];
        
        foreach ($sample_videos as $video) {
            $existing = $conn->query("SELECT id FROM videos WHERE title = '{$video['title']}'")->num_rows > 0;
            if (!$existing) {
                $stmt = $conn->prepare("INSERT INTO videos (title, url, description, reciter, category, status, uploaded_at) VALUES (?, ?, ?, ?, ?, 'active', NOW())");
                $stmt->bind_param("sssss", 
                    $video['title'], $video['url'], $video['description'], 
                    $video['reciter'], $video['category']
                );
                $stmt->execute();
                echo "✓ Video added: {$video['title']}<br>";
            }
        }
    } else {
        echo "✓ Sample videos already exist<br>";
    }
    
    // 4. Create sample recitations
    echo "<h3>Creating sample recitations...</h3>";
    
    $recitation_count = $conn->query("SELECT COUNT(*) as count FROM recitations")->fetch_assoc()['count'];
    
    if ($recitation_count < 50) {
        $users = $conn->query("SELECT id FROM users WHERE is_admin = 0 LIMIT 10")->fetch_all(MYSQLI_ASSOC);
        $videos = $conn->query("SELECT id FROM videos LIMIT 5")->fetch_all(MYSQLI_ASSOC);
        
        for ($i = 0; $i < 50; $i++) {
            $user = $users[array_rand($users)];
            $video = $videos[array_rand($videos)];
            $accuracy = rand(70, 100);
            $score = rand(80, 100);
            $attempts = rand(1, 3);
            
            $stmt = $conn->prepare("INSERT INTO recitations (user_id, video_id, accuracy, score, attempts, completed_at) VALUES (?, ?, ?, ?, ?, NOW() - INTERVAL ? DAY)");
            $days_ago = rand(0, 30);
            $stmt->bind_param("iiiiii", 
                $user['id'], $video['id'], $accuracy, $score, $attempts, $days_ago
            );
            $stmt->execute();
        }
        echo "✓ 50 sample recitations created<br>";
    } else {
        echo "✓ Sample recitations already exist<br>";
    }
    
    // 5. Create sample screen recordings
    echo "<h3>Creating sample screen recordings...</h3>";
    
    $recording_count = $conn->query("SELECT COUNT(*) as count FROM screen_records")->fetch_assoc()['count'];
    
    if ($recording_count < 20) {
        $users = $conn->query("SELECT id, name FROM users WHERE is_admin = 0 LIMIT 10")->fetch_all(MYSQLI_ASSOC);
        $videos = $conn->query("SELECT id, title FROM videos LIMIT 5")->fetch_all(MYSQLI_ASSOC);
        
        for ($i = 0; $i < 20; $i++) {
            $user = $users[array_rand($users)];
            $video = $videos[array_rand($videos)];
            $views = rand(0, 100);
            $likes = rand(0, 20);
            
            $stmt = $conn->prepare("INSERT INTO screen_records (user_id, video_id, title, filename, views, likes, status, created_at) VALUES (?, ?, ?, ?, ?, ?, 'active', NOW() - INTERVAL ? DAY)");
            $title = $user['name'] . " reciting " . $video['title'];
            $filename = 'sample_recording_' . $i . '.webm';
            $days_ago = rand(0, 15);
            
            $stmt->bind_param("iissiii", 
                $user['id'], $video['id'], $title, $filename, $views, $likes, $days_ago
            );
            $stmt->execute();
        }
        echo "✓ 20 sample screen recordings created<br>";
    } else {
        echo "✓ Sample screen recordings already exist<br>";
    }
    
    // 6. Update rankings
    echo "<h3>Updating rankings...</h3>";
    updateRankings();
    echo "✓ Rankings updated<br>";
    
    // 7. Create sample posts
    echo "<h3>Creating sample posts...</h3>";
    
    $post_count = $conn->query("SELECT COUNT(*) as count FROM posts")->fetch_assoc()['count'];
    
    if ($post_count < 10) {
        $users = $conn->query("SELECT id, name FROM users WHERE is_admin = 0 LIMIT 5")->fetch_all(MYSQLI_ASSOC);
        
        $sample_posts = [
            'Sharing my journey in learning Qur\'an recitation',
            'Amazing experience reciting today!',
            'Alhamdulillah for this blessing',
            'Keep practicing brothers and sisters',
            'May Allah accept our recitations'
        ];
        
        foreach ($sample_posts as $content) {
            $user = $users[array_rand($users)];
            $stmt = $conn->prepare("INSERT INTO posts (user_id, content, type, status, created_at) VALUES (?, ?, 'text', 'active', NOW() - INTERVAL ? DAY)");
            $days_ago = rand(0, 10);
            $stmt->bind_param("isi", $user['id'], $content, $days_ago);
            $stmt->execute();
            echo "✓ Post created by {$user['name']}<br>";
        }
    } else {
        echo "✓ Sample posts already exist<br>";
    }
    
    // 8. Create sample referrals
    echo "<h3>Creating sample referrals...</h3>";
    
    $referral_count = $conn->query("SELECT COUNT(*) as count FROM referrals")->fetch_assoc()['count'];
    
    if ($referral_count < 15) {
        $users = $conn->query("SELECT id FROM users WHERE is_admin = 0")->fetch_all(MYSQLI_ASSOC);
        
        for ($i = 0; $i < 15; $i++) {
            $referrer = $users[array_rand($users)];
            $referred = $users[array_rand($users)];
            
            if ($referrer['id'] != $referred['id']) {
                $existing = $conn->query("SELECT id FROM referrals WHERE referrer_id = {$referrer['id']} AND referred_id = {$referred['id']}")->num_rows;
                if ($existing == 0) {
                    $stmt = $conn->prepare("INSERT INTO referrals (referrer_id, referred_id, points_awarded, created_at) VALUES (?, ?, 1, NOW() - INTERVAL ? DAY)");
                    $days_ago = rand(0, 20);
                    $stmt->bind_param("iii", $referrer['id'], $referred['id'], $days_ago);
                    $stmt->execute();
                    
                    // Award points to referrer
                    $conn->query("UPDATE wallets SET points = points + 1 WHERE user_id = {$referrer['id']}");
                }
            }
        }
        echo "✓ Sample referrals created<br>";
    } else {
        echo "✓ Sample referrals already exist<br>";
    }
    
    echo "<h2>✅ Sample data initialization completed!</h2>";
    echo "<h3>Test Accounts Created:</h3>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> <EMAIL> / admin123</li>";
    echo "<li><strong>Users:</strong> All users have password 'user123'</li>";
    echo "</ul>";
    
    echo "<h3>What was created:</h3>";
    echo "<ul>";
    echo "<li>1 Admin account</li>";
    echo "<li>20 Sample users with wallets</li>";
    echo "<li>5 Sample Qur'an videos</li>";
    echo "<li>50 Sample recitations</li>";
    echo "<li>20 Sample screen recordings</li>";
    echo "<li>10 Sample posts</li>";
    echo "<li>15 Sample referrals</li>";
    echo "<li>Updated rankings</li>";
    echo "</ul>";
    
    echo "<p><a href='../index.php' class='btn btn-primary'>Go to Homepage</a></p>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Sample Data Initialization</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="container mt-4">
</body>
</html> 