<?php
$page_title = 'Manage Videos';
require_once __DIR__ . '/../components/admin_header.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle video upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_video') {
    $title = sanitize($_POST['title']);
    $youtube_url = sanitize($_POST['youtube_url']);
    $category = sanitize($_POST['category']);
    $reciter = sanitize($_POST['reciter']);
    $description = sanitize($_POST['description']);
    $transcript = sanitize($_POST['transcript']);
    
    if (empty($title) || empty($youtube_url)) {
        $error = 'Title and YouTube URL are required.';
    } else {
        try {
            $conn = getConnection();
            
            // Extract YouTube ID
            $youtube_id = extractYouTubeId($youtube_url);
            if (!$youtube_id) {
                throw new Exception('Invalid YouTube URL format.');
            }
            
            $stmt = $conn->prepare("
                INSERT INTO videos (title, youtube_url, youtube_id, category, reciter, description, transcript, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $adminId = $_SESSION['admin_id'];
            $stmt->bind_param("sssssssi", $title, $youtube_url, $youtube_id, $category, $reciter, $description, $transcript, $adminId);
            
            if ($stmt->execute()) {
                $message = 'Video added successfully!';
            } else {
                $error = 'Failed to add video.';
            }
            
            $stmt->close();
            $conn->close();
        } catch (Exception $e) {
            $error = $e->getMessage();
            logError("Admin add video error: " . $e->getMessage());
        }
    }
}

// Handle video toggle (activate/deactivate)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'toggle_video') {
    $videoId = intval($_POST['video_id']);
    $isActive = intval($_POST['is_active']);
    
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("UPDATE videos SET is_active = ? WHERE id = ?");
        $stmt->bind_param("ii", $isActive, $videoId);
        
        if ($stmt->execute()) {
            $message = $isActive ? 'Video activated successfully!' : 'Video deactivated successfully!';
        } else {
            $error = 'Failed to update video status.';
        }
        
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
        logError("Admin toggle video error: " . $e->getMessage());
    }
}

// Handle video deletion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'delete_video') {
    $videoId = intval($_POST['video_id']);
    
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("DELETE FROM videos WHERE id = ?");
        $stmt->bind_param("i", $videoId);
        
        if ($stmt->execute()) {
            $message = 'Video deleted successfully!';
        } else {
            $error = 'Failed to delete video.';
        }
        
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
        logError("Admin delete video error: " . $e->getMessage());
    }
}

// Get videos list
try {
    $conn = getConnection();
    $stmt = $conn->prepare("
        SELECT v.*, 
               COUNT(r.id) as recitation_count,
               AVG(r.accuracy_percentage) as avg_accuracy
        FROM videos v
        LEFT JOIN recitations r ON v.id = r.video_id AND r.is_completed = 1
        GROUP BY v.id
        ORDER BY v.created_at DESC
    ");
    $stmt->execute();
    $videos = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    $conn->close();
} catch (Exception $e) {
    $videos = [];
    logError("Admin fetch videos error: " . $e->getMessage());
}

// Function to extract YouTube video ID from URL
function extractYouTubeId($url) {
    $patterns = [
        '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/v\/([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/user\/[^\/]+#.*\/([a-zA-Z0-9_-]+)/'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>

<!-- Add Video Button/Modal -->
<div class="mb-4 text-end">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVideoModal">
        <i class="fas fa-plus me-2"></i>Add New Video
    </button>
</div>

<?php if ($message): ?>
    <div class="alert alert-success mb-4"><?php echo $message; ?></div>
<?php endif; ?>
<?php if ($error): ?>
    <div class="alert alert-danger mb-4"><?php echo $error; ?></div>
<?php endif; ?>

<!-- Videos Table -->
<div class="data-table-container">
    <div class="table-header">
        <h4 class="table-title">All Recitation Videos</h4>
    </div>
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Reciter</th>
                    <th>YouTube ID</th>
                    <th>Status</th>
                    <th class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($videos)): ?>
                    <tr><td colspan="5" class="text-center py-5">No videos found.</td></tr>
                <?php else: ?>
                    <?php foreach ($videos as $video): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <img src="https://img.youtube.com/vi/<?php echo htmlspecialchars($video['youtube_id']); ?>/mqdefault.jpg" alt="thumbnail" class="img-thumbnail me-3" style="width: 120px; height: 68px; object-fit: cover;">
                                <div>
                                    <strong><?php echo htmlspecialchars($video['title']); ?></strong>
                                    <div class="text-muted small"><?php echo htmlspecialchars($video['category']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($video['reciter']); ?></td>
                        <td><a href="<?php echo htmlspecialchars($video['youtube_url']); ?>" target="_blank"><?php echo htmlspecialchars($video['youtube_id']); ?></a></td>
                        <td>
                            <form action="videos.php" method="POST" class="d-inline">
                                <input type="hidden" name="action" value="toggle_video">
                                <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                <input type="hidden" name="is_active" value="<?php echo $video['is_active'] ? '0' : '1'; ?>">
                                <button type="submit" class="btn btn-sm btn-<?php echo $video['is_active'] ? 'success' : 'secondary'; ?>">
                                    <?php echo $video['is_active'] ? 'Active' : 'Inactive'; ?>
                                </button>
                            </form>
                        </td>
                        <td class="text-end">
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editVideoModal-<?php echo $video['id']; ?>">Edit</button>
                            <form action="videos.php" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this video?');">
                                <input type="hidden" name="action" value="delete_video">
                                <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Add Video Modal -->
<div class="modal fade" id="addVideoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
             <form action="videos.php" method="POST">
                <input type="hidden" name="action" value="add_video">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="title" placeholder="Video Title" required>
                                <label>Video Title</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="reciter" placeholder="Reciter Name" required>
                                <label>Reciter Name</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="url" class="form-control" name="youtube_url" placeholder="Full YouTube URL (e.g., https://www.youtube.com/watch?v=...)" required>
                        <label>Full YouTube URL</label>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" name="category" placeholder="Category (e.g., Surah Al-Fatiha)">
                        <label>Category</label>
                    </div>
                    <div class="form-floating">
                        <textarea class="form-control" name="description" placeholder="Description" style="height: 100px"></textarea>
                        <label>Description</label>
                    </div>
                    <div class="form-floating mt-3">
                        <textarea class="form-control" name="transcript" placeholder="Full transcript for recitation" style="height: 150px"></textarea>
                        <label>Transcript</label>
                        <div class="form-text">Enter the exact text that users will recite. This will be used for speech recognition feedback. Example: "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Video</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?> 