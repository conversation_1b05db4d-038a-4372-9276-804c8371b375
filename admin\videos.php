<?php
$page_title = 'Manage Videos';
require_once __DIR__ . '/../components/admin_header.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle delete recording
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_recording') {
    $recordingId = intval($_POST['recording_id']);
    $recordType = $_POST['record_type'];

    if ($recordingId > 0) {
        try {
            $conn = getConnection();

            // Get file path before deletion
            $table = ($recordType === 'screen_record') ? 'screen_records' : 'mirror_recordings';
            $stmt = $conn->prepare("SELECT file_path FROM $table WHERE id = ?");
            $stmt->bind_param("i", $recordingId);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $filePath = $row['file_path'];

                // Delete from database
                $deleteStmt = $conn->prepare("DELETE FROM $table WHERE id = ?");
                $deleteStmt->bind_param("i", $recordingId);

                if ($deleteStmt->execute()) {
                    // Delete physical file
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    $message = 'Recording deleted successfully!';
                } else {
                    $error = 'Failed to delete recording from database.';
                }
            } else {
                $error = 'Recording not found.';
            }
        } catch (Exception $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

// Handle video upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_video') {
    $title = sanitize($_POST['title']);
    $youtube_url = sanitize($_POST['youtube_url']);
    $category = sanitize($_POST['category']);
    $reciter = sanitize($_POST['reciter']);
    $description = sanitize($_POST['description']);
    $transcript = sanitize($_POST['transcript']);
    
    if (empty($title) || empty($youtube_url)) {
        $error = 'Title and YouTube URL are required.';
    } else {
        try {
            $conn = getConnection();
            
            // Extract YouTube ID
            $youtube_id = extractYouTubeId($youtube_url);
            if (!$youtube_id) {
                throw new Exception('Invalid YouTube URL format.');
            }
            
            $stmt = $conn->prepare("
                INSERT INTO videos (title, youtube_url, youtube_id, category, reciter, description, transcript, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $adminId = $_SESSION['admin_id'];
            $stmt->bind_param("sssssssi", $title, $youtube_url, $youtube_id, $category, $reciter, $description, $transcript, $adminId);
            
            if ($stmt->execute()) {
                $message = 'Video added successfully!';
            } else {
                $error = 'Failed to add video.';
            }
            
            $stmt->close();
            $conn->close();
        } catch (Exception $e) {
            $error = $e->getMessage();
            logError("Admin add video error: " . $e->getMessage());
        }
    }
}

// Handle video toggle (activate/deactivate)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'toggle_video') {
    $videoId = intval($_POST['video_id']);
    $isActive = intval($_POST['is_active']);
    
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("UPDATE videos SET is_active = ? WHERE id = ?");
        $stmt->bind_param("ii", $isActive, $videoId);
        
        if ($stmt->execute()) {
            $message = $isActive ? 'Video activated successfully!' : 'Video deactivated successfully!';
        } else {
            $error = 'Failed to update video status.';
        }
        
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
        logError("Admin toggle video error: " . $e->getMessage());
    }
}

// Handle video deletion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'delete_video') {
    $videoId = intval($_POST['video_id']);
    
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("DELETE FROM videos WHERE id = ?");
        $stmt->bind_param("i", $videoId);
        
        if ($stmt->execute()) {
            $message = 'Video deleted successfully!';
        } else {
            $error = 'Failed to delete video.';
        }
        
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
        logError("Admin delete video error: " . $e->getMessage());
    }
}

// Get user recordings from both screen_records and mirror_recordings
try {
    $conn = getConnection();

    // Get screen recordings
    $screenRecordsQuery = "
        SELECT sr.*, u.full_name, u.email, u.profile_picture,
               'screen_record' as record_type,
               DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date
        FROM screen_records sr
        JOIN users u ON sr.user_id = u.id
        ORDER BY sr.created_at DESC
    ";
    $screenResult = $conn->query($screenRecordsQuery);
    $screenRecords = [];
    if ($screenResult) {
        while ($row = $screenResult->fetch_assoc()) {
            $screenRecords[] = $row;
        }
    }

    // Get mirror recordings
    $mirrorRecordsQuery = "
        SELECT mr.*, u.full_name, u.email, u.profile_picture,
               'mirror_record' as record_type,
               DATE_FORMAT(mr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date
        FROM mirror_recordings mr
        JOIN users u ON mr.user_id = u.id
        ORDER BY mr.created_at DESC
    ";
    $mirrorResult = $conn->query($mirrorRecordsQuery);
    $mirrorRecords = [];
    if ($mirrorResult) {
        while ($row = $mirrorResult->fetch_assoc()) {
            $mirrorRecords[] = $row;
        }
    }

    // Combine all recordings
    $videos = array_merge($screenRecords, $mirrorRecords);

    // Sort by created_at descending
    usort($videos, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

} catch (Exception $e) {
    $videos = [];
    logError("Admin fetch videos error: " . $e->getMessage());
}

// Function to extract YouTube video ID from URL
function extractYouTubeId($url) {
    $patterns = [
        '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/v\/([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/user\/[^\/]+#.*\/([a-zA-Z0-9_-]+)/'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>

<!-- Add Video Button/Modal -->
<div class="mb-4 text-end">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVideoModal">
        <i class="fas fa-plus me-2"></i>Add New Video
    </button>
</div>

<?php if ($message): ?>
    <div class="alert alert-success mb-4"><?php echo $message; ?></div>
<?php endif; ?>
<?php if ($error): ?>
    <div class="alert alert-danger mb-4"><?php echo $error; ?></div>
<?php endif; ?>

<!-- User Recordings Table -->
<div class="data-table-container">
    <div class="table-header">
        <h4 class="table-title">User Recordings & Streams</h4>
        <p class="text-muted">All recordings uploaded by users from the dashboard and selfie mirror</p>
    </div>
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>Recording</th>
                    <th>User</th>
                    <th>Type</th>
                    <th>File Size</th>
                    <th>Date</th>
                    <th class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($videos)): ?>
                    <tr><td colspan="6" class="text-center py-5">No recordings found.</td></tr>
                <?php else: ?>
                    <?php foreach ($videos as $video): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="recording-thumbnail me-3" style="width: 80px; height: 60px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-video text-muted"></i>
                                </div>
                                <div>
                                    <strong><?php echo htmlspecialchars($video['title']); ?></strong>
                                    <div class="text-muted small">
                                        <?php echo htmlspecialchars($video['description'] ?? 'User recording'); ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2" style="width: 32px; height: 32px; background: #1a5f3f; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 0.8rem; font-weight: bold;">
                                    <?php echo strtoupper(substr($video['full_name'], 0, 1)); ?>
                                </div>
                                <div>
                                    <div><?php echo htmlspecialchars($video['full_name']); ?></div>
                                    <div class="text-muted small"><?php echo htmlspecialchars($video['email']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge <?php echo $video['record_type'] === 'screen_record' ? 'bg-primary' : 'bg-success'; ?>">
                                <?php echo $video['record_type'] === 'screen_record' ? 'Dashboard' : 'Selfie Mirror'; ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            if (isset($video['file_size']) && $video['file_size'] > 0) {
                                echo number_format($video['file_size'] / 1024 / 1024, 2) . ' MB';
                            } else {
                                echo 'Unknown';
                            }
                            ?>
                        </td>
                        <td><?php echo $video['formatted_date']; ?></td>
                        <td class="text-end">
                            <a href="<?php echo htmlspecialchars($video['file_path']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-play"></i> View
                            </a>
                            <a href="<?php echo htmlspecialchars($video['file_path']); ?>" download class="btn btn-sm btn-outline-success">
                                <i class="fas fa-download"></i> Download
                            </a>
                            <form action="videos.php" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this recording?');">
                                <input type="hidden" name="action" value="delete_recording">
                                <input type="hidden" name="recording_id" value="<?php echo $video['id']; ?>">
                                <input type="hidden" name="record_type" value="<?php echo $video['record_type']; ?>">
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Add Video Modal -->
<div class="modal fade" id="addVideoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
             <form action="videos.php" method="POST">
                <input type="hidden" name="action" value="add_video">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="title" placeholder="Video Title" required>
                                <label>Video Title</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="reciter" placeholder="Reciter Name" required>
                                <label>Reciter Name</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="url" class="form-control" name="youtube_url" placeholder="Full YouTube URL (e.g., https://www.youtube.com/watch?v=...)" required>
                        <label>Full YouTube URL</label>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" name="category" placeholder="Category (e.g., Surah Al-Fatiha)">
                        <label>Category</label>
                    </div>
                    <div class="form-floating">
                        <textarea class="form-control" name="description" placeholder="Description" style="height: 100px"></textarea>
                        <label>Description</label>
                    </div>
                    <div class="form-floating mt-3">
                        <textarea class="form-control" name="transcript" placeholder="Full transcript for recitation" style="height: 150px"></textarea>
                        <label>Transcript</label>
                        <div class="form-text">Enter the exact text that users will recite. This will be used for speech recognition feedback. Example: "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Video</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?> 