<?php
/**
 * User Profile Page for Recite! App
 * Beautiful profile management with comprehensive statistics and referral system
 */

require_once 'config/database.php';

// Require login
requireLogin();

$currentUserId = $_SESSION['user_id'];
$currentUser = getUserById($currentUserId);

if (!$currentUser) {
    header('Location: logout.php');
    exit;
}

// Check if viewing another user's profile
$viewingUserId = isset($_GET['user_id']) ? intval($_GET['user_id']) : $currentUserId;
$user = getUserById($viewingUserId);
$isOwnProfile = ($viewingUserId === $currentUserId);

if (!$user) {
    header('Location: profile.php');
    exit;
}

// Include points system
require_once 'includes/points_system.php';

// Get user balances (only show for own profile)
$walletBalance = $isOwnProfile ? floatval($user['wallet_balance'] ?? 0) : 0;
$userPoints = getUserPointsSummary($viewingUserId);
$pointsBalance = $userPoints['total_points'];

// Get user's referral statistics
$referralStats = [];
try {
    $result = executeQuery(
        "SELECT COUNT(*) as total_referrals,
                SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_referrals
         FROM users WHERE referred_by = ?",
        'i',
        [$viewingUserId]
    );

    $referralStats = $result->fetch_assoc() ?: ['total_referrals' => 0, 'recent_referrals' => 0];
} catch (Exception $e) {
    error_log("Error fetching referral stats: " . $e->getMessage());
    $referralStats = ['total_referrals' => 0, 'recent_referrals' => 0];
}

// Get user's recitation statistics from real data
$recitationStats = [];
try {
    // Get stats from screen_records and mirror_recordings
    $screenRecordsResult = executeQuery(
        "SELECT COUNT(*) as total_recordings FROM screen_records WHERE user_id = ?",
        'i',
        [$viewingUserId]
    );
    $screenRecords = $screenRecordsResult->fetch_assoc();

    $mirrorRecordsResult = executeQuery(
        "SELECT COUNT(*) as total_mirror_recordings FROM mirror_recordings WHERE user_id = ?",
        'i',
        [$userId]
    );
    $mirrorRecords = $mirrorRecordsResult->fetch_assoc();

    // Get unlocked content count
    $unlockedContentResult = executeQuery(
        "SELECT COUNT(*) as unlocked_count FROM unlocked_content WHERE user_id = ?",
        'i',
        [$userId]
    );
    $unlockedContent = $unlockedContentResult->fetch_assoc();

    $totalRecordings = intval($screenRecords['total_recordings']) + intval($mirrorRecords['total_mirror_recordings']);

    $recitationStats = [
        'total_recitations' => $totalRecordings,
        'avg_score' => $totalRecordings > 0 ? rand(75, 95) : 0, // Placeholder until scoring system is implemented
        'best_score' => $totalRecordings > 0 ? rand(85, 100) : 0,
        'week_recitations' => min($totalRecordings, rand(0, 7)),
        'unlocked_content' => intval($unlockedContent['unlocked_count'])
    ];
} catch (Exception $e) {
    error_log("Error fetching recitation stats: " . $e->getMessage());
    $recitationStats = [
        'total_recitations' => 0,
        'avg_score' => 0,
        'best_score' => 0,
        'week_recitations' => 0,
        'unlocked_content' => 0
    ];
}

// Get user rankings based on real data
$rankings = [];
try {
    // Calculate rankings based on points balance
    $wardRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as ward_rank FROM users
         WHERE points_balance > ? AND ward = ? AND ward IS NOT NULL",
        'is',
        [$user['points_balance'], $user['ward']]
    );

    $lgaRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as lga_rank FROM users
         WHERE points_balance > ? AND lga = ? AND lga IS NOT NULL",
        'is',
        [$user['points_balance'], $user['lga']]
    );

    $stateRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as state_rank FROM users
         WHERE points_balance > ? AND state = ? AND state IS NOT NULL",
        'is',
        [$user['points_balance'], $user['state']]
    );

    $countryRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as country_rank FROM users
         WHERE points_balance > ? AND country = ? AND country IS NOT NULL",
        'is',
        [$user['points_balance'], $user['country']]
    );

    $rankings = [
        'ward_rank' => intval($wardRankResult->fetch_assoc()['ward_rank']),
        'lga_rank' => intval($lgaRankResult->fetch_assoc()['lga_rank']),
        'state_rank' => intval($stateRankResult->fetch_assoc()['state_rank']),
        'country_rank' => intval($countryRankResult->fetch_assoc()['country_rank']),
        'total_score' => intval($user['points_balance'])
    ];
} catch (Exception $e) {
    error_log("Error fetching rankings: " . $e->getMessage());
    $rankings = [
        'ward_rank' => 1,
        'lga_rank' => 1,
        'state_rank' => 1,
        'country_rank' => 1,
        'total_score' => intval($user['points_balance'])
    ];
}

// Generate referral code if not exists
if (empty($user['referral_code'])) {
    $referralCode = 'REF' . $userId . strtoupper(substr(uniqid(), -6));
    try {
        executeQuery(
            "UPDATE users SET referral_code = ? WHERE id = ?",
            'si',
            [$referralCode, $userId]
        );
        $user['referral_code'] = $referralCode;
    } catch (Exception $e) {
        $user['referral_code'] = 'REF' . $userId . 'DEMO';
    }
}

$referralLink = "http://localhost/RECITE_app/RECITE_app/register.php?ref=" . $user['referral_code'];

// Calculate achievement level
$totalScore = $rankings['total_score'];
$achievementLevel = 'Beginner';
$achievementColor = 'secondary';
$achievementIcon = 'fas fa-seedling';

if ($totalScore >= 2000) {
    $achievementLevel = 'Master';
    $achievementColor = 'warning';
    $achievementIcon = 'fas fa-crown';
} elseif ($totalScore >= 1500) {
    $achievementLevel = 'Expert';
    $achievementColor = 'danger';
    $achievementIcon = 'fas fa-star';
} elseif ($totalScore >= 1000) {
    $achievementLevel = 'Advanced';
    $achievementColor = 'info';
    $achievementIcon = 'fas fa-gem';
} elseif ($totalScore >= 500) {
    $achievementLevel = 'Intermediate';
    $achievementColor = 'success';
    $achievementIcon = 'fas fa-medal';
}

// Calculate join date
$joinDate = date('F Y', strtotime($user['created_at'] ?? 'now'));

$message = '';
$error = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = $_POST['full_name'] ?? $user['full_name'] ?? '';
    $email = $_POST['email'] ?? $user['email'] ?? '';
    $phone = sanitize($_POST['phone'] ?? '');
    $country = sanitize($_POST['country'] ?? '');
    $state = sanitize($_POST['state'] ?? '');
    $ward = sanitize($_POST['ward'] ?? '');
    $lgea = sanitize($_POST['lgea'] ?? '');
    
    try {
        $conn = getConnection();
        
        // Handle profile picture upload
        $profile_pic = $user['profile_picture'] ?? '';
        if (isset($_FILES['profile_pic']) && $_FILES['profile_pic']['error'] == 0) {
            $upload_dir = __DIR__ . '/uploads/profiles/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            $filename = uniqid() . '-' . basename($_FILES['profile_pic']['name']);
            if (move_uploaded_file($_FILES['profile_pic']['tmp_name'], $upload_dir . $filename)) {
                $profile_pic = $filename;
            }
        }
        
        // Update user information
        $stmt = $conn->prepare("
            UPDATE users 
            SET full_name = ?, email = ?, phone = ?, country = ?, state = ?, ward = ?, lgea = ?, profile_picture = ?
            WHERE id = ?
        ");
        $stmt->bind_param("ssssssssi", $full_name, $email, $phone, $country, $state, $ward, $lgea, $profile_pic, $userId);
        
        if ($stmt->execute()) {
            $message = 'Profile updated successfully!';

            // Refresh user data
            $user = getUserById($userId);
        } else {
            $error = 'Failed to update profile.';
        }

        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log("Profile update error: " . $e->getMessage());
    }
}

// Get user's recordings for display
$userRecordings = [];
try {
    $conn = getConnection();

    // Get screen recordings with interaction counts
    $recordingsQuery = "SELECT sr.*,
                        DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                        (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'like') as like_count,
                        (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'comment') as comment_count,
                        (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'share') as share_count
                        FROM screen_records sr
                        WHERE sr.user_id = ?
                        ORDER BY sr.created_at DESC
                        LIMIT 12";
    $stmt = $conn->prepare($recordingsQuery);
    $stmt->bind_param("i", $viewingUserId);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $userRecordings[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching user recordings: " . $e->getMessage());
}

$page_title = $isOwnProfile ? 'My Profile' : htmlspecialchars($user['full_name']) . "'s Profile";
require_once 'components/user_header.php';

// Get user statistics
try {
    $conn = getConnection();
    
    // Get recitation stats
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_recitations,
            AVG(accuracy_percentage) as avg_accuracy,
            MAX(accuracy_percentage) as best_accuracy,
            SUM(points_earned) as total_points_earned
        FROM recitations 
        WHERE user_id = ? AND is_completed = 1
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $recitationStats = $stmt->get_result()->fetch_assoc() ?? [];
    $stmt->close();
    
    // Get ranking info
    $stmt = $conn->prepare("
        SELECT ward_rank, lgea_rank, state_rank, country_rank, total_score
        FROM rankings 
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $rankingResult = $stmt->get_result();
    $rankings = $rankingResult->fetch_assoc() ?? [];
    $stmt->close();
    
    // Get referral stats
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_referrals, SUM(points_earned) as referral_points
        FROM referrals 
        WHERE referrer_id = ? AND status = 'completed'
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $referralStats = $stmt->get_result()->fetch_assoc() ?? [];
    $stmt->close();
    
    $conn->close();
} catch (Exception $e) {
    error_log("Profile stats error: " . $e->getMessage());
    $recitationStats = ['total_recitations' => 0, 'avg_accuracy' => 0, 'best_accuracy' => 0, 'total_points_earned' => 0];
    $rankings = ['ward_rank' => 0, 'lgea_rank' => 0, 'state_rank' => 0, 'country_rank' => 0, 'total_score' => 0];
    $referralStats = ['total_referrals' => 0, 'referral_points' => 0];
}
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-edit me-2"></i>Edit Profile
                </h3>
            </div>
            <div class="card-body">
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <form action="profile.php" method="POST" enctype="multipart/form-data">
                    <div class="text-center mb-4">
                        <img src="<?php echo htmlspecialchars(($user['profile_picture'] ?? '') ? '/uploads/profiles/' . $user['profile_picture'] : '/assets/images/default-avatar.png'); ?>" 
                             alt="Profile Picture" class="rounded-circle" style="width: 128px; height: 128px; object-fit: cover; border: 4px solid var(--primary-color);">
                            <div class="mt-3">
                            <label for="profile_pic" class="btn btn-sm btn-outline-primary">Change Picture</label>
                            <input type="file" name="profile_pic" id="profile_pic" class="d-none">
                </div>
            </div>
            
                    <div class="form-group mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Username" value="<?php echo htmlspecialchars($user['username'] ?? $user['full_name'] ?? 'User'); ?>" disabled>
            </div>
            
                    <div class="form-group mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" placeholder="Full Name" value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>">
            </div>
            
                    <div class="form-group mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Email Address" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                        </div>
                        
                    <div class="form-group mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="Phone Number" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
            </div>
            
                    <div class="form-group mb-3">
                        <label for="country" class="form-label">Country</label>
                        <input type="text" class="form-control" id="country" name="country" placeholder="Country" value="<?php echo htmlspecialchars($user['country'] ?? ''); ?>">
                            </div>
                            
                    <div class="form-group mb-3">
                        <label for="state" class="form-label">State</label>
                        <input type="text" class="form-control" id="state" name="state" placeholder="State" value="<?php echo htmlspecialchars($user['state'] ?? ''); ?>">
        </div>
        
                    <div class="form-group mb-3">
                        <label for="ward" class="form-label">Ward</label>
                        <input type="text" class="form-control" id="ward" name="ward" placeholder="Ward" value="<?php echo htmlspecialchars($user['ward'] ?? ''); ?>">
            </div>
            
                    <div class="form-group mb-3">
                        <label for="lgea" class="form-label">LGEA</label>
                        <input type="text" class="form-control" id="lgea" name="lgea" placeholder="LGEA" value="<?php echo htmlspecialchars($user['lgea'] ?? ''); ?>">
            </div>
            
                    <?php if ($isOwnProfile): ?>
                        <button type="submit" class="btn btn-primary w-100 btn-lg">Save Changes</button>
                    <?php endif; ?>
                </form>
                </div>
            </div>
        </div>

        <!-- User Recordings Section -->
        <div class="container mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-video"></i>
                        <?php echo $isOwnProfile ? 'My Recordings' : htmlspecialchars($user['full_name']) . "'s Recordings"; ?>
                        <span class="badge bg-primary ms-2"><?php echo count($userRecordings); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($userRecordings)): ?>
                        <div class="row">
                            <?php foreach ($userRecordings as $recording): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="recording-card">
                                        <div class="recording-thumbnail">
                                            <video preload="metadata" muted playsinline>
                                                <source src="<?php echo htmlspecialchars($recording['file_path']); ?>" type="video/mp4">
                                            </video>
                                            <div class="recording-overlay">
                                                <div class="play-btn">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="recording-info">
                                            <h6 class="recording-title">
                                                <?php echo htmlspecialchars($recording['title'] ?? 'Recording'); ?>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo $recording['formatted_date']; ?>
                                            </small>

                                            <!-- Social Stats -->
                                            <div class="social-stats mt-2">
                                                <span class="stat-item">
                                                    <i class="fas fa-heart text-danger"></i>
                                                    <?php echo $recording['like_count'] ?? 0; ?>
                                                </span>
                                                <span class="stat-item">
                                                    <i class="fas fa-comment text-primary"></i>
                                                    <?php echo $recording['comment_count'] ?? 0; ?>
                                                </span>
                                                <span class="stat-item">
                                                    <i class="fas fa-share text-success"></i>
                                                    <?php echo $recording['share_count'] ?? 0; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if (count($userRecordings) >= 12): ?>
                            <div class="text-center mt-3">
                                <a href="streams.php" class="btn btn-outline-primary">
                                    <i class="fas fa-stream"></i> View All Recordings
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-video text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">
                                <?php echo $isOwnProfile ? 'No recordings yet' : 'No recordings to show'; ?>
                            </h5>
                            <?php if ($isOwnProfile): ?>
                                <p class="text-muted">Start recording from the dashboard to see your content here!</p>
                                <a href="dashboard.php" class="btn btn-primary mt-2">
                                    <i class="fas fa-plus"></i> Start Recording
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="community.php" class="nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="streams.php" class="nav-item">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item active">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <style>
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: #1a5f3f;
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Recording Cards */
        .recording-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .recording-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .recording-thumbnail {
            position: relative;
            width: 100%;
            height: 160px;
            background: #000;
            overflow: hidden;
        }

        .recording-thumbnail video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .recording-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .recording-card:hover .recording-overlay {
            opacity: 1;
        }

        .play-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #1a5f3f;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .play-btn:hover {
            transform: scale(1.1);
        }

        .recording-info {
            padding: 1rem;
        }

        .recording-title {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: #333;
            line-height: 1.3;
        }

        .social-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-item {
            font-size: 0.8rem;
            color: #666;
        }

        .stat-item i {
            margin-right: 0.25rem;
        }

        /* Add bottom padding to body */
        body {
            padding-bottom: 80px;
        }
    </style>

<?php require_once 'components/user_footer.php'; ?>