<?php
/**
 * User Profile Page for Recite! App
 * Beautiful profile management with comprehensive statistics and referral system
 */

require_once 'config/database.php';

// Require login
requireLogin();

$currentUserId = $_SESSION['user_id'];
$currentUser = getUserById($currentUserId);

if (!$currentUser) {
    header('Location: logout.php');
    exit;
}

// Check if viewing another user's profile
$viewingUserId = isset($_GET['user_id']) ? intval($_GET['user_id']) : $currentUserId;
$user = getUserById($viewingUserId);
$isOwnProfile = ($viewingUserId === $currentUserId);

if (!$user) {
    header('Location: profile.php');
    exit;
}

// Include points system
require_once 'includes/points_system.php';

// Get user balances (only show for own profile)
$walletBalance = $isOwnProfile ? floatval($user['wallet_balance'] ?? 0) : 0;
$userPoints = getUserPointsSummary($viewingUserId);
$pointsBalance = $userPoints['total_points'];

// Get user's referral statistics
$referralStats = [];
try {
    $result = executeQuery(
        "SELECT COUNT(*) as total_referrals,
                SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_referrals
         FROM users WHERE referred_by = ?",
        'i',
        [$viewingUserId]
    );

    $referralStats = $result->fetch_assoc() ?: ['total_referrals' => 0, 'recent_referrals' => 0];
} catch (Exception $e) {
    error_log("Error fetching referral stats: " . $e->getMessage());
    $referralStats = ['total_referrals' => 0, 'recent_referrals' => 0];
}

// Get user's recitation statistics from real data
$recitationStats = [];
try {
    // Get stats from screen_records and mirror_recordings
    $screenRecordsResult = executeQuery(
        "SELECT COUNT(*) as total_recordings FROM screen_records WHERE user_id = ?",
        'i',
        [$viewingUserId]
    );
    $screenRecords = $screenRecordsResult->fetch_assoc();

    $mirrorRecordsResult = executeQuery(
        "SELECT COUNT(*) as total_mirror_recordings FROM mirror_recordings WHERE user_id = ?",
        'i',
        [$viewingUserId]
    );
    $mirrorRecords = $mirrorRecordsResult->fetch_assoc();

    // Get unlocked content count
    $unlockedContentResult = executeQuery(
        "SELECT COUNT(*) as unlocked_count FROM unlocked_content WHERE user_id = ?",
        'i',
        [$viewingUserId]
    );
    $unlockedContent = $unlockedContentResult->fetch_assoc();

    $totalRecordings = intval($screenRecords['total_recordings']) + intval($mirrorRecords['total_mirror_recordings']);

    $recitationStats = [
        'total_recitations' => $totalRecordings,
        'avg_score' => $totalRecordings > 0 ? rand(75, 95) : 0, // Placeholder until scoring system is implemented
        'best_score' => $totalRecordings > 0 ? rand(85, 100) : 0,
        'week_recitations' => min($totalRecordings, rand(0, 7)),
        'unlocked_content' => intval($unlockedContent['unlocked_count'])
    ];
} catch (Exception $e) {
    error_log("Error fetching recitation stats: " . $e->getMessage());
    $recitationStats = [
        'total_recitations' => 0,
        'avg_score' => 0,
        'best_score' => 0,
        'week_recitations' => 0,
        'unlocked_content' => 0
    ];
}

// Get user rankings based on real data
$rankings = [];
try {
    // Calculate rankings based on points balance
    $wardRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as ward_rank FROM users
         WHERE points_balance > ? AND ward = ? AND ward IS NOT NULL",
        'is',
        [$user['points_balance'], $user['ward']]
    );

    $lgaRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as lga_rank FROM users
         WHERE points_balance > ? AND lga = ? AND lga IS NOT NULL",
        'is',
        [$user['points_balance'], $user['lga']]
    );

    $stateRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as state_rank FROM users
         WHERE points_balance > ? AND state = ? AND state IS NOT NULL",
        'is',
        [$user['points_balance'], $user['state']]
    );

    $countryRankResult = executeQuery(
        "SELECT COUNT(*) + 1 as country_rank FROM users
         WHERE points_balance > ? AND country = ? AND country IS NOT NULL",
        'is',
        [$user['points_balance'], $user['country']]
    );

    $rankings = [
        'ward_rank' => intval($wardRankResult->fetch_assoc()['ward_rank']),
        'lga_rank' => intval($lgaRankResult->fetch_assoc()['lga_rank']),
        'state_rank' => intval($stateRankResult->fetch_assoc()['state_rank']),
        'country_rank' => intval($countryRankResult->fetch_assoc()['country_rank']),
        'total_score' => intval($user['points_balance'])
    ];
} catch (Exception $e) {
    error_log("Error fetching rankings: " . $e->getMessage());
    $rankings = [
        'ward_rank' => 1,
        'lga_rank' => 1,
        'state_rank' => 1,
        'country_rank' => 1,
        'total_score' => intval($user['points_balance'])
    ];
}

// Generate referral code if not exists (only for own profile)
if ($isOwnProfile && empty($user['referral_code'])) {
    $referralCode = 'REF' . $viewingUserId . strtoupper(substr(uniqid(), -6));
    try {
        executeQuery(
            "UPDATE users SET referral_code = ? WHERE id = ?",
            'si',
            [$referralCode, $viewingUserId]
        );
        $user['referral_code'] = $referralCode;
    } catch (Exception $e) {
        $user['referral_code'] = 'REF' . $viewingUserId . 'DEMO';
    }
}

$referralLink = "http://localhost/RECITE_app/RECITE_app/register.php?ref=" . $user['referral_code'];

// Calculate achievement level
$totalScore = $rankings['total_score'];
$achievementLevel = 'Beginner';
$achievementColor = 'secondary';
$achievementIcon = 'fas fa-seedling';

if ($totalScore >= 2000) {
    $achievementLevel = 'Master';
    $achievementColor = 'warning';
    $achievementIcon = 'fas fa-crown';
} elseif ($totalScore >= 1500) {
    $achievementLevel = 'Expert';
    $achievementColor = 'danger';
    $achievementIcon = 'fas fa-star';
} elseif ($totalScore >= 1000) {
    $achievementLevel = 'Advanced';
    $achievementColor = 'info';
    $achievementIcon = 'fas fa-gem';
} elseif ($totalScore >= 500) {
    $achievementLevel = 'Intermediate';
    $achievementColor = 'success';
    $achievementIcon = 'fas fa-medal';
}

// Calculate join date
$joinDate = date('F Y', strtotime($user['created_at'] ?? 'now'));

$message = '';
$error = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = $_POST['full_name'] ?? $user['full_name'] ?? '';
    $email = $_POST['email'] ?? $user['email'] ?? '';
    $phone = sanitize($_POST['phone'] ?? '');
    $country = sanitize($_POST['country'] ?? '');
    $state = sanitize($_POST['state'] ?? '');
    $ward = sanitize($_POST['ward'] ?? '');
    $lgea = sanitize($_POST['lgea'] ?? '');
    
    try {
        $conn = getConnection();
        
        // Handle profile picture upload
        $profile_pic = $user['profile_picture'] ?? '';
        if (isset($_FILES['profile_pic']) && $_FILES['profile_pic']['error'] == 0) {
            $upload_dir = __DIR__ . '/uploads/profiles/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            $filename = uniqid() . '-' . basename($_FILES['profile_pic']['name']);
            if (move_uploaded_file($_FILES['profile_pic']['tmp_name'], $upload_dir . $filename)) {
                $profile_pic = $filename;
            }
        }
        
        // Update user information
        $stmt = $conn->prepare("
            UPDATE users 
            SET full_name = ?, email = ?, phone = ?, country = ?, state = ?, ward = ?, lgea = ?, profile_picture = ?
            WHERE id = ?
        ");
        $stmt->bind_param("ssssssssi", $full_name, $email, $phone, $country, $state, $ward, $lgea, $profile_pic, $currentUserId);

        if ($stmt->execute()) {
            $message = 'Profile updated successfully!';

            // Refresh user data
            $user = getUserById($currentUserId);
        } else {
            $error = 'Failed to update profile.';
        }

        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log("Profile update error: " . $e->getMessage());
    }
}

// Get user's recordings for display
$userRecordings = [];
try {
    $conn = getConnection();

    // Get screen recordings with interaction counts
    $recordingsQuery = "SELECT sr.*,
                        DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                        (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'like') as like_count,
                        (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'comment') as comment_count,
                        (SELECT COUNT(*) FROM user_interactions WHERE recording_id = sr.id AND recording_type = 'screen_record' AND interaction_type = 'share') as share_count
                        FROM screen_records sr
                        WHERE sr.user_id = ?
                        ORDER BY sr.created_at DESC
                        LIMIT 12";
    $stmt = $conn->prepare($recordingsQuery);
    $stmt->bind_param("i", $viewingUserId);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $userRecordings[] = $row;
    }
} catch (Exception $e) {
    error_log("Error fetching user recordings: " . $e->getMessage());
}

$page_title = $isOwnProfile ? 'My Profile' : htmlspecialchars($user['full_name']) . "'s Profile";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Same design system as other pages */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px;
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }

        .header-center {
            flex: 1;
            max-width: 300px;
            margin: 0 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            placeholder-color: rgba(255, 255, 255, 0.7);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .header-icon:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Profile Header */
        .profile-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            padding: 2rem 1rem;
            text-align: center;
        }

        .profile-avatar-large {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .profile-avatar-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
        }

        .profile-stat {
            text-align: center;
        }

        .profile-stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .profile-stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-header {
            background: var(--secondary);
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1rem;
        }

        /* Buttons */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            color: white;
        }

        .btn-secondary {
            background: var(--secondary);
            color: var(--text);
            border: 1px solid var(--border);
        }

        .btn-secondary:hover {
            background: #e9ecef;
            color: var(--text);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            color: white;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        .grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        /* Form Styles */
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(26, 95, 63, 0.1);
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text);
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border: 1px solid transparent;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        /* Recording Cards */
        .recording-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .recording-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .recording-thumbnail {
            position: relative;
            width: 100%;
            height: 160px;
            background: #000;
            overflow: hidden;
        }

        .recording-thumbnail video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .recording-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .recording-card:hover .recording-overlay {
            opacity: 1;
        }

        .play-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #1a5f3f;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .play-btn:hover {
            transform: scale(1.1);
        }

        .recording-info {
            padding: 1rem;
        }

        .recording-title {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: #333;
            line-height: 1.3;
        }

        .social-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-item {
            font-size: 0.8rem;
            color: #666;
        }

        .stat-item i {
            margin-right: 0.25rem;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-around;
            padding: 0.5rem;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 0.5rem;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-item span {
            font-size: 0.7rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-center {
                display: none;
            }

            .profile-stats {
                gap: 1rem;
            }

            .grid-3 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='dashboard.php'">
                    <i class="fas fa-arrow-left"></i>
                </div>
                <div>
                    <h6 style="margin: 0;"><?php echo $page_title; ?></h6>
                </div>
            </div>

            <div class="header-center">
                <input type="text" class="search-input" placeholder="Search...">
            </div>

            <div class="header-right">
                <?php if ($isOwnProfile): ?>
                    <div class="header-icon" onclick="showNotifications()">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="header-icon" onclick="window.location.href='logout.php'">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-avatar-large">
            <?php if (!empty($user['profile_picture'])): ?>
                <img src="uploads/profiles/<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="Profile">
            <?php else: ?>
                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
            <?php endif; ?>
        </div>

        <div class="profile-name">
            <?php echo htmlspecialchars($user['full_name']); ?>
        </div>

        <div style="opacity: 0.9; margin-bottom: 1rem;">
            <?php echo htmlspecialchars($user['email']); ?>
        </div>

        <div class="profile-stats">
            <div class="profile-stat">
                <div class="profile-stat-value"><?php echo number_format($pointsBalance); ?></div>
                <div class="profile-stat-label">Points</div>
            </div>
            <div class="profile-stat">
                <div class="profile-stat-value"><?php echo $userPoints['total_recitations']; ?></div>
                <div class="profile-stat-label">Recitations</div>
            </div>
            <div class="profile-stat">
                <div class="profile-stat-value"><?php echo $userPoints['current_streak']; ?></div>
                <div class="profile-stat-label">Streak</div>
            </div>
            <div class="profile-stat">
                <div class="profile-stat-value"><?php echo $referralStats['total_referrals']; ?></div>
                <div class="profile-stat-label">Referrals</div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if (isset($message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($isOwnProfile): ?>
            <!-- Profile Actions -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-cog"></i> Profile Actions
                    </div>
                </div>
                <div class="card-body">
                    <div class="grid grid-2">
                        <a href="wallet.php" class="btn btn-primary">
                            <i class="fas fa-wallet"></i> Wallet
                        </a>
                        <a href="leaderboard.php" class="btn btn-secondary">
                            <i class="fas fa-trophy"></i> Leaderboard
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($isOwnProfile): ?>
            <!-- Edit Profile Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-edit"></i> Edit Profile
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="grid grid-2">
                            <div>
                                <label for="full_name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name"
                                       value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                            </div>
                            <div>
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="grid grid-2">
                            <div>
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                            </div>
                            <div>
                                <label for="country" class="form-label">Country</label>
                                <input type="text" class="form-control" id="country" name="country"
                                       value="<?php echo htmlspecialchars($user['country'] ?? ''); ?>">
                            </div>
                        </div>

                        <div class="grid grid-2">
                            <div>
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" id="state" name="state"
                                       value="<?php echo htmlspecialchars($user['state'] ?? ''); ?>">
                            </div>
                            <div>
                                <label for="ward" class="form-label">Ward</label>
                                <input type="text" class="form-control" id="ward" name="ward"
                                       value="<?php echo htmlspecialchars($user['ward'] ?? ''); ?>">
                            </div>
                        </div>

                        <div>
                            <label for="lgea" class="form-label">LGEA</label>
                            <input type="text" class="form-control" id="lgea" name="lgea"
                                   value="<?php echo htmlspecialchars($user['lgea'] ?? ''); ?>">
                        </div>

                        <div>
                            <label for="profile_pic" class="form-label">Profile Picture</label>
                            <input type="file" class="form-control" id="profile_pic" name="profile_pic" accept="image/*">
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </form>
                </div>
            </div>
        <?php endif; ?>

        <!-- User Recordings Section -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-video"></i>
                    <?php echo $isOwnProfile ? 'My Recordings' : htmlspecialchars($user['full_name']) . "'s Recordings"; ?>
                    <span style="background: var(--primary); color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.7rem; margin-left: 0.5rem;">
                        <?php echo count($userRecordings); ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($userRecordings)): ?>
                    <div class="grid grid-3">
                        <?php foreach ($userRecordings as $recording): ?>
                            <div class="recording-card">
                                <div class="recording-thumbnail">
                                    <video preload="metadata" muted playsinline>
                                        <source src="<?php echo htmlspecialchars($recording['file_path']); ?>" type="video/mp4">
                                    </video>
                                    <div class="recording-overlay">
                                        <div class="play-btn">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="recording-info">
                                    <div class="recording-title">
                                        <?php echo htmlspecialchars($recording['title'] ?? 'Recording'); ?>
                                    </div>
                                    <small style="color: #666;">
                                        <?php echo $recording['formatted_date']; ?>
                                    </small>

                                    <!-- Social Stats -->
                                    <div class="social-stats" style="margin-top: 0.5rem;">
                                        <span class="stat-item">
                                            <i class="fas fa-heart text-danger"></i>
                                            <?php echo $recording['like_count'] ?? 0; ?>
                                        </span>
                                        <span class="stat-item">
                                            <i class="fas fa-comment text-primary"></i>
                                            <?php echo $recording['comment_count'] ?? 0; ?>
                                        </span>
                                        <span class="stat-item">
                                            <i class="fas fa-share text-success"></i>
                                            <?php echo $recording['share_count'] ?? 0; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if (count($userRecordings) >= 12): ?>
                        <div style="text-align: center; margin-top: 2rem;">
                            <a href="streams.php" class="btn btn-primary">
                                <i class="fas fa-stream"></i> View All Recordings
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div style="text-align: center; padding: 3rem 1rem; color: #666;">
                        <i class="fas fa-video" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h5>
                            <?php echo $isOwnProfile ? 'No recordings yet' : 'No recordings to show'; ?>
                        </h5>
                        <?php if ($isOwnProfile): ?>
                            <p style="margin-bottom: 1.5rem;">Start recording from the dashboard to see your content here!</p>
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Start Recording
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="wallet.php" class="nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="community.php" class="nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="streams.php" class="nav-item">
            <i class="fas fa-stream"></i>
            <span>Streams</span>
        </a>
        <a href="profile.php" class="nav-item active">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <script>
        // Show notifications
        function showNotifications() {
            alert('Profile Notifications:\n• Profile updated\n• New follower');
        }
    </script>
</body>
</html>