<?php
$page_title = 'Chat';
require_once 'components/user_header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Sample chat data
$conversations = [
    [
        'id' => 1,
        'user_name' => '<PERSON>',
        'user_avatar' => 'assets/images/default-avatar.png',
        'last_message' => 'Assalamu alaikum, how is your recitation going?',
        'last_time' => '2 min ago',
        'unread_count' => 2,
        'is_online' => true
    ],
    [
        'id' => 2,
        'user_name' => 'Fatima Zahra',
        'user_avatar' => 'assets/images/default-avatar.png',
        'last_message' => 'JazakAllah<PERSON> khair for the advice!',
        'last_time' => '1 hour ago',
        'unread_count' => 0,
        'is_online' => false
    ],
    [
        'id' => 3,
        'user_name' => '<PERSON>',
        'user_avatar' => 'assets/images/default-avatar.png',
        'last_message' => 'Can you help me with Surah <PERSON>-Ka<PERSON>?',
        'last_time' => '3 hours ago',
        'unread_count' => 1,
        'is_online' => true
    ],
    [
        'id' => 4,
        'user_name' => 'Aisha Ibrahim',
        'user_avatar' => 'assets/images/default-avatar.png',
        'last_message' => 'Great recitation session today!',
        'last_time' => '1 day ago',
        'unread_count' => 0,
        'is_online' => false
    ],
];

// Sample messages for active conversation
$messages = [
    [
        'id' => 1,
        'sender_id' => 1,
        'sender_name' => 'Abdullah Muhammad',
        'message' => 'Assalamu alaikum brother!',
        'time' => '10:30 AM',
        'is_own' => false
    ],
    [
        'id' => 2,
        'sender_id' => $userId,
        'sender_name' => 'You',
        'message' => 'Wa alaikum salaam! How are you?',
        'time' => '10:32 AM',
        'is_own' => true
    ],
    [
        'id' => 3,
        'sender_id' => 1,
        'sender_name' => 'Abdullah Muhammad',
        'message' => 'Alhamdulillah, I\'m good. How is your recitation going?',
        'time' => '10:33 AM',
        'is_own' => false
    ],
    [
        'id' => 4,
        'sender_id' => $userId,
        'sender_name' => 'You',
        'message' => 'Making good progress! Working on Surah Al-Mulk now.',
        'time' => '10:35 AM',
        'is_own' => true
    ],
    [
        'id' => 5,
        'sender_id' => 1,
        'sender_name' => 'Abdullah Muhammad',
        'message' => 'MashaAllah! That\'s a beautiful Surah. Need any help with pronunciation?',
        'time' => '10:36 AM',
        'is_own' => false
    ],
];

$active_conversation = $conversations[0];
?>

<style>
:root {
    --primary-color: #B10020;
    --primary-light: #D32F2F;
    --background: #FFFFFF;
    --surface: #F8F9FA;
    --text: #1E1E1E;
    --text-secondary: #666666;
    --border: #E0E0E0;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
}

body {
    background-color: var(--background);
    color: var(--text);
}

.chat-container {
    height: calc(100vh - 200px);
    display: flex;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
}

.conversations-panel {
    width: 300px;
    border-right: 1px solid var(--border);
    background: var(--surface);
    display: flex;
    flex-direction: column;
}

.conversations-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.conversations-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
}

.conversation-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.conversation-item:hover {
    background: white;
}

.conversation-item.active {
    background: white;
    border-left: 4px solid var(--primary-color);
}

.conversation-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border);
    position: relative;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--success);
    border-radius: 50%;
    border: 2px solid white;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-name {
    font-weight: 600;
    color: var(--text);
    margin-bottom: 0.25rem;
}

.conversation-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.conversation-time {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.unread-badge {
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chat-header-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.chat-header-name {
    font-weight: 600;
    margin: 0;
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
}

.chat-action-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-action-btn:hover {
    background: rgba(255,255,255,0.3);
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--surface);
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.message.own {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message.own .message-content {
    background: var(--primary-color);
    color: white;
}

.message-text {
    margin-bottom: 0.25rem;
}

.message-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.message.own .message-time {
    color: rgba(255,255,255,0.8);
}

.message-input-container {
    padding: 1rem;
    background: white;
    border-top: 1px solid var(--border);
    display: flex;
    gap: 1rem;
    align-items: center;
}

.message-input {
    flex: 1;
    border: 2px solid var(--border);
    border-radius: 25px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background: var(--surface);
    transition: all 0.3s ease;
}

.message-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(177, 0, 32, 0.1);
}

.send-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-btn:hover {
    background: var(--primary-light);
}

@media (max-width: 768px) {
    .conversations-panel {
        width: 100%;
        position: absolute;
        height: 100%;
        z-index: 10;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .conversations-panel.active {
        transform: translateX(0);
    }
    
    .chat-panel {
        width: 100%;
    }
}
</style>

<div class="chat-container">
    <!-- Conversations Panel -->
    <div class="conversations-panel">
        <div class="conversations-header">
            <h3><i class="fas fa-comments me-2"></i>Messages</h3>
            <button class="chat-action-btn">
                <i class="fas fa-plus"></i>
            </button>
        </div>
        
        <div class="conversations-list">
            <?php foreach ($conversations as $conversation): ?>
                <div class="conversation-item <?php echo $conversation['id'] == 1 ? 'active' : ''; ?>">
                    <div class="conversation-avatar">
                        <img src="<?php echo $conversation['user_avatar']; ?>" alt="Avatar">
                        <?php if ($conversation['is_online']): ?>
                            <div class="online-indicator"></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="conversation-info">
                        <div class="conversation-name"><?php echo htmlspecialchars($conversation['user_name']); ?></div>
                        <div class="conversation-message"><?php echo htmlspecialchars($conversation['last_message']); ?></div>
                    </div>
                    
                    <div class="conversation-meta">
                        <div class="conversation-time"><?php echo $conversation['last_time']; ?></div>
                        <?php if ($conversation['unread_count'] > 0): ?>
                            <div class="unread-badge"><?php echo $conversation['unread_count']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <!-- Chat Panel -->
    <div class="chat-panel">
        <div class="chat-header">
            <div class="chat-header-info">
                <img src="<?php echo $active_conversation['user_avatar']; ?>" alt="Avatar" class="chat-header-avatar">
                <div>
                    <h4 class="chat-header-name"><?php echo htmlspecialchars($active_conversation['user_name']); ?></h4>
                    <small><?php echo $active_conversation['is_online'] ? 'Online' : 'Offline'; ?></small>
                </div>
            </div>
            
            <div class="chat-actions">
                <button class="chat-action-btn">
                    <i class="fas fa-video"></i>
                </button>
                <button class="chat-action-btn">
                    <i class="fas fa-phone"></i>
                </button>
                <button class="chat-action-btn">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        </div>
        
        <div class="messages-container">
            <?php foreach ($messages as $message): ?>
                <div class="message <?php echo $message['is_own'] ? 'own' : ''; ?>">
                    <div class="message-content">
                        <div class="message-text"><?php echo htmlspecialchars($message['message']); ?></div>
                        <div class="message-time"><?php echo $message['time']; ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="message-input-container">
            <input type="text" class="message-input" placeholder="Type your message..." />
            <button class="send-btn">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>
</div>

<?php require_once 'components/user_footer.php'; ?>

<script>
// Message input functionality
document.querySelector('.message-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

document.querySelector('.send-btn').addEventListener('click', sendMessage);

function sendMessage() {
    const input = document.querySelector('.message-input');
    const message = input.value.trim();
    
    if (message) {
        // Add message to UI (placeholder)
        const messagesContainer = document.querySelector('.messages-container');
        const messageHtml = `
            <div class="message own">
                <div class="message-content">
                    <div class="message-text">${message}</div>
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            </div>
        `;
        messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Clear input
        input.value = '';
        
        // In a real app, you'd send this to the server
        console.log('Sending message:', message);
    }
}

// Auto-scroll to bottom of messages
document.querySelector('.messages-container').scrollTop = document.querySelector('.messages-container').scrollHeight;
</script> 