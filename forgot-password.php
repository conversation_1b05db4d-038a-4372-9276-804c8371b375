<?php
/**
 * Forgot Password Page for Recite! App
 * Fresh design with password reset functionality
 */

require_once 'config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    
    if (empty($email)) {
        $error = 'Please enter your email address';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } else {
        try {
            // Check if user exists
            $user = executeQuery(
                "SELECT id, full_name FROM users WHERE email = ?",
                's',
                [$email]
            )->fetch_assoc();
            
            if ($user) {
                // Generate reset token
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                
                // Store reset token
                executeQuery(
                    "INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) 
                     ON DUPLICATE KEY UPDATE token = ?, expires_at = ?",
                    'sssss',
                    [$email, $token, $expires, $token, $expires]
                );
                
                // In a real app, send email here
                // For demo purposes, we'll show success message
                $success = 'Password reset instructions have been sent to your email address.';
            } else {
                // Don't reveal if email exists or not for security
                $success = 'If an account with that email exists, password reset instructions have been sent.';
            }
        } catch (Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            $error = 'An error occurred. Please try again later.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - <?php echo APP_NAME; ?></title>
    
    <!-- Fresh Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">
    
    <style>
        /* Auth Page Specific Styles */
        body {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-md);
            margin: 0;
        }
        
        .auth-container {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            padding: var(--space-2xl);
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }
        
        .auth-logo {
            font-size: var(--font-size-3xl);
            color: var(--primary);
            margin-bottom: var(--space-lg);
        }
        
        .auth-title {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            margin-bottom: var(--space-md);
            color: var(--gray-900);
        }
        
        .auth-subtitle {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            margin-bottom: var(--space-xl);
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            margin-bottom: var(--space-xl);
            transition: var(--transition);
        }
        
        .back-link:hover {
            color: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <a href="login.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Login
        </a>
        
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-key"></i>
            </div>
            <h1 class="auth-title">Forgot Password?</h1>
            <p class="auth-subtitle">
                Enter your email address and we'll send you instructions to reset your password.
            </p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo $success; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" id="forgotPasswordForm">
            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input type="email" class="form-input" id="email" name="email" 
                       placeholder="Enter your email address" required
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
            </div>
            
            <button type="submit" class="btn btn-primary btn-lg w-full">
                <i class="fas fa-paper-plane"></i>
                Send Reset Instructions
            </button>
        </form>
        
        <div class="text-center mt-8">
            <p class="text-gray-600">
                Remember your password? 
                <a href="login.php" class="text-primary font-medium">Sign in here</a>
            </p>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        // Form enhancement
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner loading"></i> Sending...';
            submitBtn.disabled = true;
            
            // Re-enable button if form submission fails
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 10000);
        });
        
        // Auto-focus email input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });
    </script>
</body>
</html>
