<?php
require_once '../config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

try {
    $conn = getConnection();
    
    // Get current user's ranking information
    $stmt = $conn->prepare("
        SELECT r.*, u.full_name, u.state, u.ward, u.lgea 
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE r.user_id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $currentUserRank = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    // Get Ward Rankings (top 20)
    $stmt = $conn->prepare("
        SELECT r.*, u.full_name, u.profile_picture, u.ward, u.state
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE u.ward = ? AND u.state = ? AND u.is_active = 1
        ORDER BY r.total_score DESC 
        LIMIT 20
    ");
    $stmt->bind_param("ss", $user['ward'], $user['state']);
    $stmt->execute();
    $wardRankings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    // Get State Rankings (top 50)
    $stmt = $conn->prepare("
        SELECT r.*, u.full_name, u.profile_picture, u.ward, u.state
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE u.state = ? AND u.is_active = 1
        ORDER BY r.total_score DESC 
        LIMIT 50
    ");
    $stmt->bind_param("s", $user['state']);
    $stmt->execute();
    $stateRankings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    // Get Country Rankings (top 100)
    $stmt = $conn->prepare("
        SELECT r.*, u.full_name, u.profile_picture, u.ward, u.state
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE u.country = ? AND u.is_active = 1
        ORDER BY r.total_score DESC 
        LIMIT 100
    ");
    $stmt->bind_param("s", $user['country']);
    $stmt->execute();
    $countryRankings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    // Get user statistics
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_recitations,
            AVG(accuracy_percentage) as avg_accuracy,
            MAX(accuracy_percentage) as best_accuracy,
            SUM(points_earned) as total_points
        FROM recitations 
        WHERE user_id = ? AND is_completed = 1
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $userStats = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    // Get recent top performers globally
    $stmt = $conn->prepare("
        SELECT r.*, u.full_name, u.profile_picture, u.state, u.country
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE u.is_active = 1
        ORDER BY r.total_score DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $globalTop = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    $conn->close();
    
} catch (Exception $e) {
    logError("Rankings page error: " . $e->getMessage());
    $currentUserRank = ['ward_rank' => 0, 'state_rank' => 0, 'country_rank' => 0, 'total_score' => 0];
    $wardRankings = $stateRankings = $countryRankings = $globalTop = [];
    $userStats = ['total_recitations' => 0, 'avg_accuracy' => 0, 'best_accuracy' => 0, 'total_points' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rankings - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/main.css" rel="stylesheet">
    <style>
        .ranking-card {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
        }
        .ranking-card h4 {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .ranking-list {
            max-height: 500px;
            overflow-y: auto;
        }
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }
        .ranking-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .ranking-item.current-user {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #f39c12;
            font-weight: bold;
        }
        .rank-number {
            width: 50px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            margin: 0 15px;
        }
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        .avatar-placeholder {
            width: 100%;
            height: 100%;
            background: #6c757d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .user-info {
            flex-grow: 1;
        }
        .user-name {
            font-weight: 600;
            color: #2c3e50;
        }
        .user-location {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .user-score {
            text-align: right;
            min-width: 80px;
        }
        .score {
            font-size: 1.1rem;
            font-weight: bold;
            color: #27ae60;
        }
        .ranking-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-book-quran me-2"></i>Qur'an Recite App
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="community.php">
                    <i class="fas fa-users me-1"></i>Community
                </a>
                <a class="nav-link active" href="rank.php">
                    <i class="fas fa-trophy me-1"></i>Rankings
                </a>
                <a class="nav-link" href="wallet.php">
                    <i class="fas fa-wallet me-1"></i>Wallet
                </a>
                <a class="nav-link" href="profile.php">
                    <i class="fas fa-user me-1"></i>Profile
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <h2 class="mb-0"><i class="fas fa-trophy me-2"></i>Rankings & Leaderboards</h2>
                        <p class="mb-0">Compete with reciters from your Ward, State, and across the Country</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- User's Current Rankings -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>My Current Rankings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="ranking-card text-center">
                                    <div class="ranking-icon">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                    </div>
                                    <h4 class="text-primary">#<?php echo $currentUserRank['ward_rank'] ?: 'Unranked'; ?></h4>
                                    <p class="mb-0">Ward Rank</p>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['ward']); ?></small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="ranking-card text-center">
                                    <div class="ranking-icon">
                                        <i class="fas fa-building text-success"></i>
                                    </div>
                                    <h4 class="text-success">#<?php echo $currentUserRank['lgea_rank'] ?: 'Unranked'; ?></h4>
                                    <p class="mb-0">LGEA Rank</p>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['lgea']); ?></small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="ranking-card text-center">
                                    <div class="ranking-icon">
                                        <i class="fas fa-flag text-warning"></i>
                                    </div>
                                    <h4 class="text-warning">#<?php echo $currentUserRank['state_rank'] ?: 'Unranked'; ?></h4>
                                    <p class="mb-0">State Rank</p>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['state']); ?></small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="ranking-card text-center">
                                    <div class="ranking-icon">
                                        <i class="fas fa-globe text-danger"></i>
                                    </div>
                                    <h4 class="text-danger">#<?php echo $currentUserRank['country_rank'] ?: 'Unranked'; ?></h4>
                                    <p class="mb-0">Country Rank</p>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['country']); ?></small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h5 class="text-primary"><?php echo number_format($currentUserRank['total_score'] ?? 0, 2); ?></h5>
                                <small>Total Score</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-success"><?php echo $userStats['total_recitations']; ?></h5>
                                <small>Recitations</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-warning"><?php echo round($userStats['avg_accuracy'] ?? 0, 1); ?>%</h5>
                                <small>Average Accuracy</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-info"><?php echo $userStats['total_points']; ?></h5>
                                <small>Points Earned</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rankings Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="rankingTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="global-tab" data-bs-toggle="tab" data-bs-target="#global" type="button" role="tab">
                                    <i class="fas fa-globe me-2"></i>Global Top
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="country-tab" data-bs-toggle="tab" data-bs-target="#country" type="button" role="tab">
                                    <i class="fas fa-flag me-2"></i>Country
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="state-tab" data-bs-toggle="tab" data-bs-target="#state" type="button" role="tab">
                                    <i class="fas fa-map me-2"></i>State
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="ward-tab" data-bs-toggle="tab" data-bs-target="#ward" type="button" role="tab">
                                    <i class="fas fa-map-marker-alt me-2"></i>Ward
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-body">
                        <div class="tab-content" id="rankingTabContent">
                            <!-- Global Rankings -->
                            <div class="tab-pane fade show active" id="global" role="tabpanel">
                                <h6 class="mb-3"><i class="fas fa-crown text-warning me-2"></i>Global Top Performers</h6>
                                <?php if (!empty($globalTop)): ?>
                                    <div class="ranking-list">
                                        <?php foreach ($globalTop as $index => $ranker): ?>
                                            <div class="ranking-item <?php echo $ranker['user_id'] == $userId ? 'current-user' : ''; ?>">
                                                <div class="rank-number">
                                                    <?php if ($index < 3): ?>
                                                        <i class="fas fa-medal <?php echo ['text-warning', 'text-secondary', 'text-warning'][$index]; ?>"></i>
                                                    <?php else: ?>
                                                        <span class="rank">#<?php echo $index + 1; ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="user-avatar">
                                                    <?php if ($ranker['profile_picture']): ?>
                                                        <img src="../<?php echo htmlspecialchars($ranker['profile_picture']); ?>" alt="Avatar">
                                                    <?php else: ?>
                                                        <div class="avatar-placeholder">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="user-info">
                                                    <div class="user-name"><?php echo htmlspecialchars($ranker['full_name']); ?></div>
                                                    <div class="user-location">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?php echo htmlspecialchars($ranker['state']); ?>, <?php echo htmlspecialchars($ranker['country']); ?>
                                                    </div>
                                                </div>
                                                <div class="user-score">
                                                    <span class="score"><?php echo number_format($ranker['total_score'], 2); ?></span>
                                                    <small>points</small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No rankings available yet. Be the first to recite!</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Country Rankings -->
                            <div class="tab-pane fade" id="country" role="tabpanel">
                                <h6 class="mb-3"><i class="fas fa-flag text-primary me-2"></i><?php echo htmlspecialchars($user['country']); ?> Rankings</h6>
                                <?php if (!empty($countryRankings)): ?>
                                    <div class="ranking-list">
                                        <?php foreach ($countryRankings as $index => $ranker): ?>
                                            <div class="ranking-item <?php echo $ranker['user_id'] == $userId ? 'current-user' : ''; ?>">
                                                <div class="rank-number">
                                                    <span class="rank">#<?php echo $index + 1; ?></span>
                                                </div>
                                                <div class="user-avatar">
                                                    <?php if ($ranker['profile_picture']): ?>
                                                        <img src="../<?php echo htmlspecialchars($ranker['profile_picture']); ?>" alt="Avatar">
                                                    <?php else: ?>
                                                        <div class="avatar-placeholder">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="user-info">
                                                    <div class="user-name"><?php echo htmlspecialchars($ranker['full_name']); ?></div>
                                                    <div class="user-location">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?php echo htmlspecialchars($ranker['ward']); ?>, <?php echo htmlspecialchars($ranker['state']); ?>
                                                    </div>
                                                </div>
                                                <div class="user-score">
                                                    <span class="score"><?php echo number_format($ranker['total_score'], 2); ?></span>
                                                    <small>points</small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-flag fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No country rankings available yet.</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- State Rankings -->
                            <div class="tab-pane fade" id="state" role="tabpanel">
                                <h6 class="mb-3"><i class="fas fa-map text-success me-2"></i><?php echo htmlspecialchars($user['state']); ?> State Rankings</h6>
                                <?php if (!empty($stateRankings)): ?>
                                    <div class="ranking-list">
                                        <?php foreach ($stateRankings as $index => $ranker): ?>
                                            <div class="ranking-item <?php echo $ranker['user_id'] == $userId ? 'current-user' : ''; ?>">
                                                <div class="rank-number">
                                                    <span class="rank">#<?php echo $index + 1; ?></span>
                                                </div>
                                                <div class="user-avatar">
                                                    <?php if ($ranker['profile_picture']): ?>
                                                        <img src="../<?php echo htmlspecialchars($ranker['profile_picture']); ?>" alt="Avatar">
                                                    <?php else: ?>
                                                        <div class="avatar-placeholder">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="user-info">
                                                    <div class="user-name"><?php echo htmlspecialchars($ranker['full_name']); ?></div>
                                                    <div class="user-location">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?php echo htmlspecialchars($ranker['ward']); ?>
                                                    </div>
                                                </div>
                                                <div class="user-score">
                                                    <span class="score"><?php echo number_format($ranker['total_score'], 2); ?></span>
                                                    <small>points</small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-map fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No state rankings available yet.</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Ward Rankings -->
                            <div class="tab-pane fade" id="ward" role="tabpanel">
                                <h6 class="mb-3"><i class="fas fa-map-marker-alt text-warning me-2"></i><?php echo htmlspecialchars($user['ward']); ?> Ward Rankings</h6>
                                <?php if (!empty($wardRankings)): ?>
                                    <div class="ranking-list">
                                        <?php foreach ($wardRankings as $index => $ranker): ?>
                                            <div class="ranking-item <?php echo $ranker['user_id'] == $userId ? 'current-user' : ''; ?>">
                                                <div class="rank-number">
                                                    <span class="rank">#<?php echo $index + 1; ?></span>
                                                </div>
                                                <div class="user-avatar">
                                                    <?php if ($ranker['profile_picture']): ?>
                                                        <img src="../<?php echo htmlspecialchars($ranker['profile_picture']); ?>" alt="Avatar">
                                                    <?php else: ?>
                                                        <div class="avatar-placeholder">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="user-info">
                                                    <div class="user-name"><?php echo htmlspecialchars($ranker['full_name']); ?></div>
                                                    <div class="user-location">
                                                        <i class="fas fa-home me-1"></i>
                                                        Local Competitor
                                                    </div>
                                                </div>
                                                <div class="user-score">
                                                    <span class="score"><?php echo number_format($ranker['total_score'], 2); ?></span>
                                                    <small>points</small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No ward rankings available yet. Start reciting to appear here!</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5><i class="fas fa-rocket me-2"></i>Improve Your Ranking</h5>
                        <p class="text-muted">Practice more recitations to climb the leaderboards and earn points!</p>
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fas fa-microphone me-2"></i>Start Reciting
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh rankings every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
        
        // Highlight current user in rankings
        document.addEventListener('DOMContentLoaded', function() {
            const currentUserItems = document.querySelectorAll('.current-user');
            currentUserItems.forEach(item => {
                item.scrollIntoView({ behavior: 'smooth', block: 'center' });
            });
        });
    </script>
</body>
</html> 