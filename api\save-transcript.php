<?php
require_once '../config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'User not authenticated']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
    exit;
}

// Required fields
$requiredFields = ['transcript', 'expected_text', 'accuracy_percentage', 'correct_words', 'total_words', 'attempts_used', 'points_earned'];
foreach ($requiredFields as $field) {
    if (!isset($input[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => "Missing required field: $field"]);
        exit;
    }
}

// Sanitize input
$transcript = sanitize($input['transcript']);
$expectedText = sanitize($input['expected_text']);
$accuracyPercentage = floatval($input['accuracy_percentage']);
$correctWords = intval($input['correct_words']);
$totalWords = intval($input['total_words']);
$attemptsUsed = intval($input['attempts_used']);
$pointsEarned = intval($input['points_earned']);
$videoId = intval($input['video_id'] ?? 1);
$userId = $_SESSION['user_id'];

// Validate ranges
if ($accuracyPercentage < 0 || $accuracyPercentage > 100) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid accuracy percentage']);
    exit;
}

if ($attemptsUsed < 0 || $attemptsUsed > 3) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid attempts count']);
    exit;
}

try {
    $conn = getConnection();
    $conn->autocommit(false); // Start transaction
    
    // Calculate marks deducted
    $marksDeducted = 0;
    if ($attemptsUsed > 0) {
        $deductionRates = [1, 2, 3];
        $marksDeducted = $deductionRates[min($attemptsUsed - 1, 2)];
    }
    
    // Insert recitation record
    $stmt = $conn->prepare("
        INSERT INTO recitations (
            user_id, video_id, transcript, correct_words, total_words, 
            accuracy_percentage, marks_deducted, attempts_used, points_earned, is_completed
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
    ");
    
    $stmt->bind_param("iisiiidii", 
        $userId, $videoId, $transcript, $correctWords, $totalWords,
        $accuracyPercentage, $marksDeducted, $attemptsUsed, $pointsEarned
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to save recitation: ' . $stmt->error);
    }
    
    $recitationId = $conn->insert_id;
    $stmt->close();
    
    // Update user points and recitation count
    $stmt = $conn->prepare("
        UPDATE users 
        SET points_balance = points_balance + ?, 
            total_recitations = total_recitations + 1,
            last_recitation_date = NOW()
        WHERE id = ?
    ");
    $stmt->bind_param("ii", $pointsEarned, $userId);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to update user points: ' . $stmt->error);
    }
    $stmt->close();
    
    // Record points transaction
    if ($pointsEarned > 0) {
        $stmt = $conn->prepare("
            INSERT INTO transactions (user_id, transaction_type, points, description, status) 
            VALUES (?, 'earning', ?, 'Recitation points earned', 'completed')
        ");
        $stmt->bind_param("ii", $userId, $pointsEarned);
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to record transaction: ' . $stmt->error);
        }
        $stmt->close();
    }
    
    // Update user ranking
    updateUserRanking($userId);
    
    // Check for streak bonus
    checkAndAwardStreakBonus($userId);
    
    // Check if user qualifies for perfect recitation bonus
    if ($accuracyPercentage == 100 && $attemptsUsed == 0) {
        $bonusPoints = 5; // Extra bonus for perfect first-try recitation
        
        $stmt = $conn->prepare("UPDATE users SET points_balance = points_balance + ? WHERE id = ?");
        $stmt->bind_param("ii", $bonusPoints, $userId);
        $stmt->execute();
        
        $stmt = $conn->prepare("
            INSERT INTO transactions (user_id, transaction_type, points, description, status) 
            VALUES (?, 'earning', ?, 'Perfect recitation bonus', 'completed')
        ");
        $stmt->bind_param("ii", $userId, $bonusPoints);
        $stmt->execute();
        $stmt->close();
        
        $pointsEarned += $bonusPoints;
    }
    
    // Get updated user stats
    $stmt = $conn->prepare("
        SELECT points_balance, total_recitations, wallet_balance 
        FROM users 
        WHERE id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $userStats = $result->fetch_assoc();
    $stmt->close();
    
    // Get user's new ranking
    $stmt = $conn->prepare("SELECT * FROM rankings WHERE user_id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $ranking = $result->fetch_assoc();
    $stmt->close();
    
    $conn->commit(); // Commit transaction
    $conn->close();
    
    // Success response
    echo json_encode([
        'success' => true,
        'recitation_id' => $recitationId,
        'points_earned' => $pointsEarned,
        'marks_deducted' => $marksDeducted,
        'user_stats' => $userStats,
        'ranking' => $ranking,
        'message' => 'Recitation saved successfully!'
    ]);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollback();
        $conn->close();
    }
    
    logError("Save transcript error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to save recitation: ' . $e->getMessage()
    ]);
}
?> 