<?php
/**
 * Mirror Camera Component
 * Provides live webcam mirror view for users during recitation
 * No recording - just a mirror for posture monitoring
 */
?>

<div class="mirror-camera-container">
    <div class="card h-100">
        <div class="card-header bg-success text-white">
            <h6 class="mb-0">
                <i class="fas fa-video me-2"></i>
                Mirror View
            </h6>
            <small class="text-light">Watch your posture while reciting</small>
        </div>
        
        <div class="card-body p-0">
            <div class="camera-wrapper">
                <video id="mirrorVideo" class="mirror-video" autoplay muted playsinline></video>
                <div id="cameraOverlay" class="camera-overlay">
                    <div class="camera-status">
                        <i class="fas fa-video-slash fa-3x text-muted"></i>
                        <p class="mt-2 text-muted">Click to enable camera</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="camera-controls">
                    <button id="toggleCameraBtn" class="btn btn-success btn-sm">
                        <i class="fas fa-video"></i> Enable Camera
                    </button>
                </div>
                <div class="camera-info">
                    <span id="cameraStatus" class="badge bg-secondary">Disabled</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.mirror-camera-container {
    height: 100%;
}

.camera-wrapper {
    position: relative;
    width: 100%;
    height: 300px;
    background: #000;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mirror-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scaleX(-1); /* Mirror effect */
    display: none;
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.camera-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.camera-status {
    text-align: center;
    color: white;
}

.camera-status i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .camera-wrapper {
        height: 250px;
    }
}

/* Camera permission styles */
.camera-permission-denied {
    background: #dc3545;
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 5px;
}

.camera-loading {
    background: #007bff;
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 5px;
}
</style>

<script>
class MirrorCamera {
    constructor() {
        this.video = document.getElementById('mirrorVideo');
        this.overlay = document.getElementById('cameraOverlay');
        this.toggleBtn = document.getElementById('toggleCameraBtn');
        this.statusBadge = document.getElementById('cameraStatus');
        this.stream = null;
        this.isActive = false;
        
        this.init();
    }
    
    init() {
        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showError('Camera not supported in this browser');
            return;
        }
        
        // Bind events
        this.toggleBtn.addEventListener('click', () => this.toggleCamera());
        this.overlay.addEventListener('click', () => this.enableCamera());
        
        // Check for camera permission on load
        this.checkCameraPermission();
    }
    
    async checkCameraPermission() {
        try {
            const permissions = await navigator.permissions.query({ name: 'camera' });
            
            switch (permissions.state) {
                case 'granted':
                    this.updateStatus('Camera permission granted', 'success');
                    break;
                case 'prompt':
                    this.updateStatus('Click to allow camera access', 'info');
                    break;
                case 'denied':
                    this.updateStatus('Camera access denied', 'danger');
                    this.showPermissionDenied();
                    break;
            }
            
            permissions.addEventListener('change', () => {
                this.checkCameraPermission();
            });
            
        } catch (error) {
            console.log('Permission API not supported');
            this.updateStatus('Click to enable camera', 'info');
        }
    }
    
    async enableCamera() {
        if (this.isActive) return;
        
        this.updateStatus('Requesting camera access...', 'warning');
        this.showLoading();
        
        try {
            const constraints = {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user' // Front camera
                },
                audio: false // No audio needed for mirror
            };
            
            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // Set video source
            this.video.srcObject = this.stream;
            
            // Wait for video to load
            await new Promise((resolve) => {
                this.video.addEventListener('loadedmetadata', resolve, { once: true });
            });
            
            // Show video and hide overlay
            this.video.style.display = 'block';
            this.overlay.classList.add('hidden');
            
            // Update UI
            this.isActive = true;
            this.updateStatus('Camera active', 'success');
            this.toggleBtn.innerHTML = '<i class="fas fa-video-slash"></i> Disable Camera';
            this.toggleBtn.classList.remove('btn-success');
            this.toggleBtn.classList.add('btn-danger');
            
            console.log('Mirror camera enabled successfully');
            
        } catch (error) {
            console.error('Camera access error:', error);
            this.handleCameraError(error);
        }
    }
    
    disableCamera() {
        if (!this.isActive) return;
        
        // Stop all tracks
        if (this.stream) {
            this.stream.getTracks().forEach(track => {
                track.stop();
            });
            this.stream = null;
        }
        
        // Reset video
        this.video.srcObject = null;
        this.video.style.display = 'none';
        
        // Show overlay
        this.overlay.classList.remove('hidden');
        this.overlay.innerHTML = `
            <div class="camera-status">
                <i class="fas fa-video-slash fa-3x text-muted"></i>
                <p class="mt-2 text-muted">Click to enable camera</p>
            </div>
        `;
        
        // Update UI
        this.isActive = false;
        this.updateStatus('Camera disabled', 'secondary');
        this.toggleBtn.innerHTML = '<i class="fas fa-video"></i> Enable Camera';
        this.toggleBtn.classList.remove('btn-danger');
        this.toggleBtn.classList.add('btn-success');
        
        console.log('Mirror camera disabled');
    }
    
    toggleCamera() {
        if (this.isActive) {
            this.disableCamera();
        } else {
            this.enableCamera();
        }
    }
    
    handleCameraError(error) {
        let errorMessage = 'Failed to access camera';
        
        switch (error.name) {
            case 'NotAllowedError':
                errorMessage = 'Camera permission denied';
                this.showPermissionDenied();
                break;
            case 'NotFoundError':
                errorMessage = 'No camera found';
                break;
            case 'NotReadableError':
                errorMessage = 'Camera is being used by another application';
                break;
            case 'OverconstrainedError':
                errorMessage = 'Camera constraints cannot be satisfied';
                break;
            case 'SecurityError':
                errorMessage = 'Camera access blocked for security reasons';
                break;
            default:
                errorMessage = `Camera error: ${error.message}`;
        }
        
        this.updateStatus(errorMessage, 'danger');
        this.showError(errorMessage);
    }
    
    showLoading() {
        this.overlay.classList.remove('hidden');
        this.overlay.innerHTML = `
            <div class="camera-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Accessing camera...</p>
            </div>
        `;
    }
    
    showPermissionDenied() {
        this.overlay.classList.remove('hidden');
        this.overlay.innerHTML = `
            <div class="camera-permission-denied">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
                <p class="mt-2">Camera access denied</p>
                <small>Please allow camera access in your browser settings</small>
            </div>
        `;
    }
    
    showError(message) {
        this.overlay.classList.remove('hidden');
        this.overlay.innerHTML = `
            <div class="camera-status">
                <i class="fas fa-exclamation-circle fa-3x text-danger"></i>
                <p class="mt-2 text-danger">${message}</p>
                <button class="btn btn-outline-light btn-sm" onclick="mirrorCamera.enableCamera()">
                    Try Again
                </button>
            </div>
        `;
    }
    
    updateStatus(message, type) {
        if (this.statusBadge) {
            this.statusBadge.textContent = message;
            this.statusBadge.className = `badge bg-${type}`;
        }
    }
    
    // Public methods
    isEnabled() {
        return this.isActive;
    }
    
    destroy() {
        this.disableCamera();
        
        // Remove event listeners
        if (this.toggleBtn) {
            this.toggleBtn.removeEventListener('click', this.toggleCamera);
        }
        if (this.overlay) {
            this.overlay.removeEventListener('click', this.enableCamera);
        }
        
        console.log('Mirror camera destroyed');
    }
}

// Initialize mirror camera when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.mirrorCamera = new MirrorCamera();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.mirrorCamera) {
        window.mirrorCamera.destroy();
    }
});
</script> 