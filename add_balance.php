<?php
require_once 'config/database.php';

echo "<h2>Adding Balance to Test User</h2>";

try {
    $conn = getConnection();
    
    // Add balance to user ID 1 (test user)
    $userId = 1;
    $amount = 1000.00;
    
    $stmt = $conn->prepare("UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?");
    $stmt->bind_param("di", $amount, $userId);
    
    if ($stmt->execute()) {
        echo "<p>✅ Added ₦" . number_format($amount, 2) . " to user ID $userId</p>";
        
        // Check new balance
        $stmt = $conn->prepare("SELECT wallet_balance, full_name FROM users WHERE id = ?");
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        if ($user) {
            echo "<p><strong>User:</strong> " . htmlspecialchars($user['full_name']) . "</p>";
            echo "<p><strong>New Balance:</strong> ₦" . number_format($user['wallet_balance'], 2) . "</p>";
        }
    } else {
        echo "<p>❌ Failed to add balance</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";
echo "<p><a href='test_unlock_api.php'>Test Unlock API</a></p>";
?>
