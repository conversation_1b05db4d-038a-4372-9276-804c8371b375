<?php
$page_title = 'Admin Dashboard';
require_once __DIR__ . '/../components/admin_header.php';

$conn = getConnection();

// Fetch stats for dashboard cards
$total_users_result = $conn->query("SELECT COUNT(*) as count FROM users");
$total_users = $total_users_result->fetch_assoc()['count'];

// Use try-catch for tables that may not exist yet
try {
    $total_recitations_result = $conn->query("SELECT COUNT(*) as count FROM recitations");
    $total_recitations = $total_recitations_result->fetch_assoc()['count'];
} catch (Exception $e) {
    $total_recitations = 0;
}

try {
    $total_earnings_result = $conn->query("SELECT SUM(amount) as sum FROM transactions WHERE transaction_type = 'deposit'");
    $total_earnings = $total_earnings_result->fetch_assoc()['sum'];
} catch (Exception $e) {
    $total_earnings = 0;
}

try {
    $pending_withdrawals_result = $conn->query("SELECT COUNT(*) as count FROM withdrawal_requests WHERE status = 'pending'");
    $pending_withdrawals = $pending_withdrawals_result->fetch_assoc()['count'];
} catch (Exception $e) {
    $pending_withdrawals = 0;
}


// Fetch recent users
$recent_users_result = $conn->query("SELECT id, full_name, email, created_at FROM users ORDER BY created_at DESC LIMIT 5");
$recent_users = $recent_users_result->fetch_all(MYSQLI_ASSOC);

// Fetch recent recitations - using mock data since recitations table may not exist yet
$recent_recitations = [
    [
        'full_name' => 'Demo User 1',
        'title' => 'Al-Fatiha Recitation',
        'accuracy_score' => 95,
        'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
    ],
    [
        'full_name' => 'Demo User 2', 
        'title' => 'Al-Baqarah Verse 1-5',
        'accuracy_score' => 88,
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
    ],
    [
        'full_name' => 'Demo User 3',
        'title' => 'Al-Ikhlas',
        'accuracy_score' => 92,
        'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
    ]
];

$conn->close();
?>

<!-- Stats Grid -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon primary"><i class="fas fa-users"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $total_users; ?></h3>
            <p class="stat-label">Total Users</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon success"><i class="fas fa-microphone-alt"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $total_recitations; ?></h3>
            <p class="stat-label">Total Recitations</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon warning"><i class="fas fa-dollar-sign"></i></div>
        <div class="stat-info">
            <h3 class="stat-value">₦<?php echo number_format($total_earnings ?? 0, 2); ?></h3>
            <p class="stat-label">Total Earnings</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon danger"><i class="fas fa-hourglass-half"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $pending_withdrawals; ?></h3>
            <p class="stat-label">Pending Withdrawals</p>
        </div>
    </div>
</div>

<!-- Data Tables -->
<div class="row">
    <div class="col-lg-6">
        <div class="data-table-container">
            <div class="table-header">
                <h4 class="table-title">Recent Users</h4>
                <a href="users.php" class="btn btn-sm btn-outline">View All</a>
            </div>
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Joined</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_users as $user): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                        <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="data-table-container">
            <div class="table-header">
                <h4 class="table-title">Recent Recitations</h4>
                <a href="videos.php" class="btn btn-sm btn-outline">View All</a>
            </div>
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Video</th>
                        <th>Score</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                     <?php foreach ($recent_recitations as $recitation): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($recitation['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($recitation['title']); ?></td>
                        <td><span class="badge badge-success"><?php echo $recitation['accuracy_score']; ?>%</span></td>
                        <td><?php echo date('M d, Y', strtotime($recitation['created_at'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>


<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>