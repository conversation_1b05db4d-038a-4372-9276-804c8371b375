<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

// Check if user is already logged in
if (isLoggedIn()) {
    header('Location: user/dashboard.php');
    exit;
}

// Get featured videos and top users for display
$conn = getConnection();

// Get latest videos (with error handling)
$featuredVideos = [];
try {
    $stmt = $conn->prepare("SELECT * FROM videos WHERE is_active = 1 ORDER BY created_at DESC LIMIT 6");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $featuredVideos[] = $row;
    }
} catch (Exception $e) {
    // If videos table doesn't exist, create sample data
    error_log("Videos table error: " . $e->getMessage());
    $featuredVideos = [
        [
            'id' => 1,
            'title' => '<PERSON>ah Al-<PERSON>iha - Beautiful Recitation',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'category' => 'Short Surahs',
            'reciter' => 'Sheikh Abdul Rahman',
            'description' => 'Beautiful recitation of the opening chapter of the Quran'
        ],
        [
            'id' => 2,
            'title' => 'Surah Al-Ikhlas - Perfect Pronunciation',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'category' => 'Short Surahs',
            'reciter' => 'Sheikh Muhammad',
            'description' => 'Learn perfect pronunciation of Surah Al-Ikhlas'
        ],
        [
            'id' => 3,
            'title' => 'Surah Al-Falaq - Protection Prayer',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'category' => 'Short Surahs',
            'reciter' => 'Sheikh Ahmad',
            'description' => 'Seeking protection through Quranic recitation'
        ]
    ];
}

// Get top performers for leaderboard preview (with error handling)
$topUsers = [];
try {
    $stmt = $conn->prepare("
        SELECT u.full_name, u.profile_picture, u.state, u.ward,
               COALESCE(SUM(r.final_score), 0) as total_score
        FROM users u
        LEFT JOIN recitations r ON u.id = r.user_id AND r.is_completed = 1
        WHERE u.is_active = 1
        GROUP BY u.id
        ORDER BY total_score DESC
        LIMIT 10
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $topUsers[] = $row;
    }
} catch (Exception $e) {
    // If rankings table doesn't exist, create sample data
    error_log("Rankings query error: " . $e->getMessage());
    $topUsers = [
        [
            'full_name' => 'Abdullah Muhammad',
            'profile_picture' => 'assets/images/default-avatar.png',
            'state' => 'Kano',
            'ward' => 'Fagge',
            'total_score' => 2450
        ],
        [
            'full_name' => 'Fatima Zahra',
            'profile_picture' => 'assets/images/default-avatar.png',
            'state' => 'Lagos',
            'ward' => 'Victoria Island',
            'total_score' => 2380
        ],
        [
            'full_name' => 'Muhammad Yusuf',
            'profile_picture' => 'assets/images/default-avatar.png',
            'state' => 'Abuja',
            'ward' => 'Garki',
            'total_score' => 2320
        ]
    ];
}

// Get recent stream recordings (with error handling)
$recentStreams = [];
try {
    $stmt = $conn->prepare("
        SELECT sr.*, u.full_name, u.profile_picture
        FROM screen_records sr
        JOIN users u ON sr.user_id = u.id
        WHERE sr.is_public = 1
        ORDER BY sr.created_at DESC
        LIMIT 8
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $recentStreams[] = $row;
    }
} catch (Exception $e) {
    // If screen_records table doesn't exist, create sample data
    error_log("Screen records query error: " . $e->getMessage());
    $recentStreams = [
        [
            'id' => 1,
            'title' => 'My Surah Al-Fatiha Practice',
            'full_name' => 'Abdullah Muhammad',
            'profile_picture' => 'assets/images/default-avatar.png',
            'views_count' => 45,
            'likes_count' => 12,
            'duration' => 180,
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'id' => 2,
            'title' => 'Perfect Tajweed Practice',
            'full_name' => 'Fatima Zahra',
            'profile_picture' => 'assets/images/default-avatar.png',
            'views_count' => 38,
            'likes_count' => 15,
            'duration' => 240,
            'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
        ]
    ];
}

$stmt->close();
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Learn, Recite, Rank & Earn</title>

    <!-- Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">

    <style>
        /* Home Page Specific Styles */
        body {
            background: var(--white);
            margin: 0;
            padding: 0;
        }

        /* Header Navigation */
        .home-header {
            background: var(--white);
            box-shadow: var(--shadow-sm);
            padding: var(--space-lg) 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .home-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-lg);
        }

        .home-logo {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            text-decoration: none;
            color: var(--primary);
        }

        .home-logo i {
            font-size: var(--font-size-2xl);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .home-logo-text {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--gray-900);
        }

        .home-nav-buttons {
            display: flex;
            gap: var(--space-sm);
        }

        .home-nav-buttons .btn {
            padding: var(--space-sm) var(--space-md);
            font-size: var(--font-size-sm);
            font-weight: 500;
            border-radius: var(--radius-md);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-xs);
            transition: var(--transition);
            white-space: nowrap;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: var(--space-4xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-lg);
        }

        .hero-icon {
            font-size: 5rem;
            margin-bottom: var(--space-2xl);
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .hero-title {
            font-size: var(--font-size-4xl);
            font-weight: 800;
            margin-bottom: var(--space-xl);
            line-height: 1.2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .hero-subtitle {
            font-size: var(--font-size-xl);
            margin-bottom: var(--space-3xl);
            opacity: 0.95;
            line-height: 1.6;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            gap: var(--space-md);
            justify-content: center;
            flex-wrap: wrap;
            max-width: 100%;
            margin: 0 auto;
        }

        .hero-btn {
            padding: var(--space-md) var(--space-lg);
            border-radius: var(--radius-lg);
            text-decoration: none;
            font-weight: 600;
            font-size: var(--font-size-base);
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            min-width: 140px;
            justify-content: center;
            white-space: nowrap;
        }

        .hero-btn-primary {
            background: var(--white);
            color: var(--primary);
            box-shadow: var(--shadow-lg);
        }

        .hero-btn-primary:hover {
            background: var(--gray-50);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: var(--primary);
        }

        .hero-btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .hero-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: var(--white);
        }

        /* Container */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-lg);
            width: 100%;
            box-sizing: border-box;
        }

        /* Prevent horizontal overflow */
        body {
            overflow-x: hidden;
        }

        * {
            box-sizing: border-box;
        }

        /* Features Section */
        .features-section {
            padding: var(--space-4xl) 0;
            background: var(--gray-50);
        }

        .section-header {
            text-align: center;
            margin-bottom: var(--space-3xl);
        }

        .section-title {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: var(--space-lg);
            line-height: 1.2;
        }

        .section-subtitle {
            font-size: var(--font-size-lg);
            color: var(--gray-600);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-lg);
            width: 100%;
            max-width: 100%;
        }

        .feature-card {
            background: var(--white);
            padding: var(--space-2xl);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            text-align: center;
            transition: var(--transition);
            border: 1px solid var(--gray-200);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-light);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-lg);
            color: var(--white);
            font-size: var(--font-size-2xl);
        }

        .feature-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--space-md);
        }

        .feature-description {
            color: var(--gray-600);
            line-height: 1.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                padding: 0 var(--space-md);
            }

            .hero-content {
                padding: 0 var(--space-sm);
            }

            .hero-icon {
                font-size: 3.5rem;
                margin-bottom: var(--space-lg);
            }

            .hero-title {
                font-size: var(--font-size-2xl);
                margin-bottom: var(--space-md);
            }

            .hero-subtitle {
                font-size: var(--font-size-base);
                margin-bottom: var(--space-xl);
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
                gap: var(--space-sm);
            }

            .hero-btn {
                width: auto;
                min-width: 160px;
                padding: var(--space-sm) var(--space-md);
                font-size: var(--font-size-sm);
            }

            .home-nav {
                padding: 0 var(--space-md);
            }

            .home-nav-buttons {
                gap: var(--space-xs);
            }

            .home-nav-buttons .btn {
                padding: var(--space-xs) var(--space-sm);
                font-size: var(--font-size-xs);
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: var(--space-lg);
            }

            .section-title {
                font-size: var(--font-size-2xl);
            }

            .section-subtitle {
                font-size: var(--font-size-base);
            }
        }

        @media (max-width: 480px) {
            .hero-section {
                padding: var(--space-2xl) 0;
            }

            .hero-btn {
                min-width: 140px;
                font-size: var(--font-size-xs);
            }

            .home-logo-text {
                font-size: var(--font-size-lg);
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="home-header">
        <nav class="home-nav">
            <a href="index.php" class="home-logo">
                <i class="fas fa-mosque"></i>
                <span class="home-logo-text"><?php echo APP_NAME; ?></span>
            </a>
            <div class="home-nav-buttons">
                <a href="login.php" class="btn btn-outline-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Login</span>
                </a>
                <a href="register.php" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    <span>Register</span>
                </a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <div class="hero-icon">
                <i class="fas fa-mosque"></i>
            </div>
            <h1 class="hero-title"><?php echo APP_NAME; ?></h1>
            <p class="hero-subtitle">Learn, Recite, Rank & Earn with AI-powered Qur'anic education platform</p>
            <!-- <div class="hero-buttons">
                <a href="register.php" class="hero-btn hero-btn-primary">
                    <i class="fas fa-user-plus"></i>
                    <span>Register (₦1,000)</span>
                </a>
                <a href="login.php" class="hero-btn hero-btn-secondary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Login</span>
                </a>
            </div> -->
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="main-container">
            <div class="section-header">
                <h2 class="section-title">Why Choose <?php echo APP_NAME; ?>?</h2>
                <p class="section-subtitle">Experience the future of Qur'anic learning with our innovative AI-powered platform</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <h3 class="feature-title">AI Speech Recognition</h3>
                    <p class="feature-description">Advanced AI technology provides real-time feedback on your recitation accuracy with precise word-by-word analysis and pronunciation guidance.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="feature-title">Ranking System</h3>
                    <p class="feature-description">Compete with users from your Ward, LGEA, State, and Country. Climb the leaderboards through consistent practice and dedication.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <h3 class="feature-title">Earn Rewards</h3>
                    <p class="feature-description">Convert your learning achievements into real rewards. Earn points and cash through accurate recitations and consistent practice.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3 class="feature-title">Video Streaming</h3>
                    <p class="feature-description">Share your recitation journey with the community. Watch, like, and learn from fellow reciters worldwide.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title">Community</h3>
                    <p class="feature-description">Connect with other learners through live chat, video calls, and group recitation sessions in our vibrant community.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="feature-title">Multilingual Support</h3>
                    <p class="feature-description">Available in English, Hausa, Arabic, Hindi, and Chinese to serve our diverse global Muslim community.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <!-- <section class="py-5" style="background: var(--background-light);">
        <div class="main-container">
            <div class="row text-center">
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">1,000+</h3>
                            <p class="text-secondary mb-0">Active Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">50,000+</h3>
                            <p class="text-secondary mb-0">Recitations</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">₦500K+</h3>
                            <p class="text-secondary mb-0">Rewards Paid</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">95%</h3>
                            <p class="text-secondary mb-0">Accuracy Rate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!-- CTA Section -->
    <!-- <section class="py-5">
        <div class="main-container text-center">
            <div class="card" style="background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); color: white;">
                <div class="card-body p-5">
                    <h2 class="mb-4">Ready to Start Your Qur'anic Journey?</h2>
                    <p class="text-large mb-4 opacity-75">Join thousands of Muslims worldwide who are improving their recitation skills and earning rewards.</p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="register.php" class="btn btn-lg" style="background: white; color: var(--primary-color);">
                            <i class="fas fa-rocket me-2"></i>Get Started Now
                        </a>
                        <a href="about.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle me-2"></i>Learn More
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!-- Featured Videos Section -->
    <section style="padding: var(--space-4xl) 0; background: var(--white);">
        <div class="main-container">
            <div class="section-header">
                <h2 class="section-title">Featured Recitations</h2>
                <p class="section-subtitle">Learn from beautiful Qur'anic recitations by renowned scholars</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-xl);">
                <?php foreach (array_slice($featuredVideos, 0, 6) as $video): ?>
                <div class="feature-card" style="text-align: left;">
                    <div style="aspect-ratio: 16/9; background: var(--gray-100); border-radius: var(--radius-lg); margin-bottom: var(--space-lg); display: flex; align-items: center; justify-content: center; color: var(--gray-500);">
                        <i class="fab fa-youtube" style="font-size: var(--font-size-3xl);"></i>
                    </div>
                    <h4 style="font-size: var(--font-size-lg); font-weight: 600; color: var(--gray-900); margin-bottom: var(--space-sm);">
                        <?php echo htmlspecialchars($video['title']); ?>
                    </h4>
                    <p style="color: var(--gray-600); margin-bottom: var(--space-md); font-size: var(--font-size-sm);">
                        By <?php echo htmlspecialchars($video['reciter']); ?>
                    </p>
                    <p style="color: var(--gray-500); font-size: var(--font-size-sm); line-height: 1.5;">
                        <?php echo htmlspecialchars(substr($video['description'], 0, 100)) . '...'; ?>
                    </p>
                </div>
                <?php endforeach; ?>
            </div>

            <div style="text-align: center; margin-top: var(--space-2xl);">
                <a href="register.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-play-circle"></i>
                    <span>Start Learning Today</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Community Stats Section -->
    <section style="padding: var(--space-4xl) 0; background: var(--gray-50);">
        <div class="main-container">
            <div class="section-header">
                <h2 class="section-title">Join Our Growing Community</h2>
                <p class="section-subtitle">Thousands of Muslims worldwide are improving their Qur'anic recitation</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-xl); text-align: center;">
                <div class="feature-card">
                    <div style="font-size: var(--font-size-3xl); font-weight: 800; color: var(--primary); margin-bottom: var(--space-sm);">
                        <?php echo count($topUsers) > 0 ? '1000+' : '500+'; ?>
                    </div>
                    <h4 style="font-size: var(--font-size-lg); font-weight: 600; color: var(--gray-900); margin-bottom: var(--space-sm);">
                        Active Learners
                    </h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">
                        Students practicing daily
                    </p>
                </div>

                <div class="feature-card">
                    <div style="font-size: var(--font-size-3xl); font-weight: 800; color: var(--primary); margin-bottom: var(--space-sm);">
                        50+
                    </div>
                    <h4 style="font-size: var(--font-size-lg); font-weight: 600; color: var(--gray-900); margin-bottom: var(--space-sm);">
                        Surahs Available
                    </h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">
                        Complete Qur'anic chapters
                    </p>
                </div>

                <div class="feature-card">
                    <div style="font-size: var(--font-size-3xl); font-weight: 800; color: var(--primary); margin-bottom: var(--space-sm);">
                        95%
                    </div>
                    <h4 style="font-size: var(--font-size-lg); font-weight: 600; color: var(--gray-900); margin-bottom: var(--space-sm);">
                        Accuracy Rate
                    </h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">
                        AI-powered precision
                    </p>
                </div>

                <div class="feature-card">
                    <div style="font-size: var(--font-size-3xl); font-weight: 800; color: var(--primary); margin-bottom: var(--space-sm);">
                        5
                    </div>
                    <h4 style="font-size: var(--font-size-lg); font-weight: 600; color: var(--gray-900); margin-bottom: var(--space-sm);">
                        Languages
                    </h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">
                        Global accessibility
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--gray-900); color: var(--white); padding: var(--space-3xl) 0 var(--space-xl);">
        <div class="main-container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-2xl); margin-bottom: var(--space-2xl);">
                <div>
                    <div style="display: flex; align-items: center; gap: var(--space-md); margin-bottom: var(--space-lg);">
                        <i class="fas fa-mosque" style="font-size: var(--font-size-xl); color: var(--primary-light);"></i>
                        <h5 style="color: var(--white); font-weight: 700; margin: 0;"><?php echo APP_NAME; ?></h5>
                    </div>
                    <p style="color: var(--gray-300); line-height: 1.6; margin-bottom: var(--space-lg);">Empowering the global Muslim community through innovative Qur'anic education and AI-powered learning experiences.</p>
                    <div style="display: flex; gap: var(--space-md);">
                        <a href="#" style="color: var(--gray-400); font-size: var(--font-size-lg); transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-400)'">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" style="color: var(--gray-400); font-size: var(--font-size-lg); transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-400)'">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" style="color: var(--gray-400); font-size: var(--font-size-lg); transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-400)'">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" style="color: var(--gray-400); font-size: var(--font-size-lg); transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-400)'">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h6 style="color: var(--white); font-weight: 600; margin-bottom: var(--space-lg);">Platform</h6>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: var(--space-sm);"><a href="login.php" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Login</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="register.php" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Register</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="#features" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Features</a></li>
                    </ul>
                </div>

                <div>
                    <h6 style="color: var(--white); font-weight: 600; margin-bottom: var(--space-lg);">Community</h6>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: var(--space-sm);"><a href="#" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Rankings</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="#" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Streaming</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="#" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Support</a></li>
                    </ul>
                </div>

                <div>
                    <h6 style="color: var(--white); font-weight: 600; margin-bottom: var(--space-lg);">Languages</h6>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇺🇸 English</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇳🇬 Hausa</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇸🇦 العربية</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇮🇳 हिन्दी</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇨🇳 中文</li>
                    </ul>
                </div>
            </div>

            <div style="border-top: 1px solid var(--gray-700); padding-top: var(--space-xl); display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--space-md);">
                <p style="color: var(--gray-400); margin: 0;">&copy; 2024 Universal Reciters Foundation. All rights reserved.</p>
                <p style="color: var(--gray-400); margin: 0;">Founded by Mallam AlFijir Usman Zaki</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html> 