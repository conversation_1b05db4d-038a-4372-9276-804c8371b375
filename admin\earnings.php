<?php
$page_title = 'Earnings Report';
require_once __DIR__ . '/../components/admin_header.php';

if (!isset($_SESSION['user_id']) || !isAdmin($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Get earnings statistics
$total_registrations = $conn->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'")->fetch_assoc()['count'];
$registration_revenue = $total_registrations * 1000; // ₦1000 per registration

$total_points_sold = $conn->query("SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE type = 'point_purchase'")->fetch_assoc()['total'];

$engagement_revenue = $conn->query("SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE type = 'engagement_fee'")->fetch_assoc()['total'];

$post_revenue = $conn->query("SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE type = 'post_fee'")->fetch_assoc()['total'];

$total_revenue = $registration_revenue + $total_points_sold + $engagement_revenue + $post_revenue;

// Recent transactions
$recent_transactions = $conn->query("
    SELECT t.*, u.name as user_name
    FROM transactions t
    LEFT JOIN users u ON t.user_id = u.id
    ORDER BY t.created_at DESC
    LIMIT 20
")->fetch_all(MYSQLI_ASSOC);

// Fetch earnings data (example, you'll need to adjust)
$stmt = $conn->query("
    SELECT DATE(created_at) as date, SUM(amount) as total_earnings
    FROM earnings
    GROUP BY DATE(created_at)
    ORDER BY date DESC
    LIMIT 30
");
$daily_earnings = $stmt->fetchAll();

$total_earnings = $conn->query("SELECT SUM(amount) FROM earnings")->fetchColumn();
?>

<div class="row">
    <div class="col-12">
        <div class="stat-card mb-4">
            <div class="stat-info">
                <p class="stat-label">Total Platform Earnings</p>
                <h3 class="stat-value">$<?php echo number_format($total_earnings ?? 0, 2); ?></h3>
            </div>
            <div class="stat-icon warning"><i class="fas fa-dollar-sign"></i></div>
        </div>
    </div>
</div>

<div class="data-table-container">
    <div class="table-header">
        <h4 class="table-title">Daily Earnings</h4>
    </div>
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th class="text-end">Total Earnings</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($daily_earnings)): ?>
                    <tr><td colspan="2" class="text-center py-5">No earnings data available.</td></tr>
                <?php else: ?>
                    <?php foreach ($daily_earnings as $earning): ?>
                    <tr>
                        <td><?php echo date('M d, Y', strtotime($earning['date'])); ?></td>
                        <td class="text-end fw-semibold">$<?php echo number_format($earning['total_earnings'], 2); ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<div class="container mt-4">
    <h1><i class="fas fa-chart-line me-2"></i>Earnings Dashboard</h1>

    <!-- Revenue Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h4>₦<?php echo number_format($total_revenue); ?></h4>
                    <p class="mb-0">Total Revenue</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h4>₦<?php echo number_format($registration_revenue); ?></h4>
                    <p class="mb-0">Registration Fees</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h4>₦<?php echo number_format($total_points_sold); ?></h4>
                    <p class="mb-0">Point Sales</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h4>₦<?php echo number_format($engagement_revenue); ?></h4>
                    <p class="mb-0">Engagement Fees</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Transactions</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_transactions as $transaction): ?>
                            <tr>
                                <td><?php echo $transaction['id']; ?></td>
                                <td><?php echo htmlspecialchars($transaction['user_name'] ?? 'System'); ?></td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?php echo ucfirst(str_replace('_', ' ', $transaction['type'])); ?>
                                    </span>
                                </td>
                                <td>₦<?php echo number_format($transaction['amount'], 2); ?></td>
                                <td><?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?> 