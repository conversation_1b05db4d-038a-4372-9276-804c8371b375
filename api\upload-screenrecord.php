<?php
require_once '../config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'User not authenticated']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['recording']) || $_FILES['recording']['error'] !== UPLOAD_ERR_OK) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'No file uploaded or upload error']);
    exit;
}

// Validate input
$title = sanitize($_POST['title'] ?? 'Recitation Recording');
$duration = intval($_POST['duration'] ?? 0);
$recitationId = intval($_POST['recitation_id'] ?? 0);
$userId = $_SESSION['user_id'];

// Validate file type and size
$file = $_FILES['recording'];
$allowedTypes = ['video/webm', 'video/mp4', 'video/ogg'];
$maxSize = 100 * 1024 * 1024; // 100MB

if (!in_array($file['type'], $allowedTypes)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid file type. Only WebM, MP4, and OGG videos are allowed']);
    exit;
}

if ($file['size'] > $maxSize) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'File too large. Maximum size is 100MB']);
    exit;
}

try {
    $conn = getConnection();
    $conn->autocommit(false); // Start transaction
    
    // Check if user has enough wallet balance for screen recording session (₦30)
    $sessionFee = 30;
    $user = getUserById($userId);
    
    if ($user['wallet_balance'] < $sessionFee) {
        throw new Exception('Insufficient wallet balance. Please add funds to record and share videos.');
    }
    
    // Create upload directory if it doesn't exist
    $uploadDir = '../uploads/screen-recordings/';
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0777, true)) {
            throw new Exception('Failed to create upload directory');
        }
    }
    
    // Generate unique filename
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    if (empty($fileExtension)) {
        // Determine extension from MIME type
        $mimeToExt = [
            'video/webm' => 'webm',
            'video/mp4' => 'mp4',
            'video/ogg' => 'ogg'
        ];
        $fileExtension = $mimeToExt[$file['type']] ?? 'webm';
    }
    
    $fileName = 'recording_' . $userId . '_' . time() . '_' . uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    $relativePath = 'uploads/screen-recordings/' . $fileName;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception('Failed to save uploaded file');
    }
    
    // Insert screen record into database
    $stmt = $conn->prepare("
        INSERT INTO screen_records (user_id, recitation_id, title, file_path, file_size, duration) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->bind_param("iissii", $userId, $recitationId, $title, $relativePath, $file['size'], $duration);
    
    if (!$stmt->execute()) {
        // Remove uploaded file if database insert fails
        unlink($filePath);
        throw new Exception('Failed to save recording to database: ' . $stmt->error);
    }
    
    $recordId = $conn->insert_id;
    $stmt->close();
    
    // Deduct session fee from user's wallet
    updateUserWallet($userId, -$sessionFee, 'Screen recording session fee', 'deduction');
    
    // Update user's total screen recordings count (add this field if needed)
    $stmt = $conn->prepare("UPDATE users SET updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $stmt->close();
    
    // Get updated user stats
    $stmt = $conn->prepare("SELECT wallet_balance, points_balance FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $userStats = $result->fetch_assoc();
    $stmt->close();
    
    $conn->commit(); // Commit transaction
    $conn->close();
    
    // Generate video thumbnail (optional - would require FFmpeg)
    // generateVideoThumbnail($filePath);
    
    echo json_encode([
        'success' => true,
        'record_id' => $recordId,
        'file_path' => $relativePath,
        'file_size' => $file['size'],
        'duration' => $duration,
        'session_fee_deducted' => $sessionFee,
        'user_stats' => $userStats,
        'message' => 'Recording uploaded successfully!'
    ]);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollback();
        $conn->close();
    }
    
    // Remove uploaded file if it exists and there was an error
    if (isset($filePath) && file_exists($filePath)) {
        unlink($filePath);
    }
    
    logError("Upload screen record error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Function to generate video thumbnail (optional)
function generateVideoThumbnail($videoPath) {
    // This would require FFmpeg to be installed on the server
    // $thumbnailPath = str_replace('.webm', '_thumb.jpg', $videoPath);
    // $command = "ffmpeg -i \"$videoPath\" -ss 00:00:01.000 -vframes 1 \"$thumbnailPath\" 2>&1";
    // exec($command, $output, $returnCode);
    // return $returnCode === 0 ? $thumbnailPath : false;
}
?> 