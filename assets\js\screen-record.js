// Screen Recording for Qur'an Recitation
class ScreenRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.isRecording = false;
        this.recordingStartTime = null;
        this.recordingStream = null;
        this.canSave = false;
        
        this.initializeRecorder();
    }
    
    initializeRecorder() {
        // Check for MediaRecorder support
        if (!MediaRecorder.isTypeSupported('video/webm;codecs=vp9')) {
            if (!MediaRecorder.isTypeSupported('video/webm;codecs=vp8')) {
                console.warn('VP9 and VP8 not supported, falling back to default codec');
            }
        }
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        const recordButton = document.getElementById('startScreenRecord');
        const statusElement = document.getElementById('recordingStatus');
        
        if (recordButton) {
            recordButton.addEventListener('click', () => {
                this.toggleRecording();
            });
        }
        
        // Auto-start recording when recitation begins
        document.addEventListener('recitationStarted', () => {
            this.startRecording();
        });
        
        // Auto-stop recording when recitation completes
        document.addEventListener('recitationCompleted', () => {
            this.stopRecording();
        });
    }
    
    async toggleRecording() {
        if (this.isRecording) {
            await this.stopRecording();
        } else {
            await this.startRecording();
        }
    }
    
    async startRecording() {
        try {
            // Request screen capture
            this.recordingStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    mediaSource: 'screen',
                    width: { ideal: 1920, max: 1920 },
                    height: { ideal: 1080, max: 1080 },
                    frameRate: { ideal: 30, max: 60 }
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            });
            
            // Add microphone audio if available
            try {
                const audioStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                
                // Combine streams
                const combinedStream = new MediaStream();
                
                // Add video tracks from screen capture
                this.recordingStream.getVideoTracks().forEach(track => {
                    combinedStream.addTrack(track);
                });
                
                // Add audio tracks from microphone
                audioStream.getAudioTracks().forEach(track => {
                    combinedStream.addTrack(track);
                });
                
                this.recordingStream = combinedStream;
            } catch (audioError) {
                console.warn('Could not access microphone:', audioError);
                // Continue with screen audio only
            }
            
            // Set up MediaRecorder
            const options = {
                mimeType: this.getSupportedMimeType(),
                videoBitsPerSecond: 2500000, // 2.5 Mbps
                audioBitsPerSecond: 128000   // 128 kbps
            };
            
            this.mediaRecorder = new MediaRecorder(this.recordingStream, options);
            this.recordedChunks = [];
            
            // Event handlers
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.onstop = () => {
                this.handleRecordingStop();
            };
            
            this.mediaRecorder.onerror = (event) => {
                console.error('MediaRecorder error:', event.error);
                this.showError('Recording error: ' + event.error.message);
            };
            
            // Start recording
            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;
            this.recordingStartTime = Date.now();
            
            this.updateUI();
            this.showSuccess('Screen recording started');
            
            // Handle stream end (user stopped sharing)
            this.recordingStream.getVideoTracks()[0].addEventListener('ended', () => {
                if (this.isRecording) {
                    this.stopRecording();
                }
            });
            
        } catch (error) {
            console.error('Error starting screen recording:', error);
            this.showError('Failed to start screen recording: ' + error.message);
        }
    }
    
    async stopRecording() {
        if (!this.isRecording || !this.mediaRecorder) {
            return;
        }
        
        try {
            this.mediaRecorder.stop();
            
            // Stop all tracks
            if (this.recordingStream) {
                this.recordingStream.getTracks().forEach(track => track.stop());
            }
            
            this.isRecording = false;
            this.updateUI();
            
        } catch (error) {
            console.error('Error stopping recording:', error);
            this.showError('Error stopping recording: ' + error.message);
        }
    }
    
    handleRecordingStop() {
        if (this.recordedChunks.length === 0) {
            this.showError('No recording data available');
            return;
        }
        
        // Create blob from recorded chunks
        const blob = new Blob(this.recordedChunks, {
            type: this.getSupportedMimeType()
        });
        
        const duration = Math.round((Date.now() - this.recordingStartTime) / 1000);
        
        // Store the recording data
        this.recordingBlob = blob;
        this.recordingDuration = duration;
        
        // Show preview
        this.showRecordingPreview(blob, duration);
        
        // Enable save if recitation was completed
        if (this.canSave) {
            this.enableSaveButton();
        }
        
        this.showSuccess(`Recording completed (${duration}s). ${this.canSave ? 'You can now save it.' : 'Complete recitation to save.'}`);
    }
    
    showRecordingPreview(blob, duration) {
        // Create video element for preview
        const previewContainer = document.createElement('div');
        previewContainer.className = 'recording-preview mt-3 p-3 border rounded';
        previewContainer.innerHTML = `
            <h6><i class="fas fa-video me-2"></i>Recording Preview</h6>
            <video controls style="width: 100%; max-height: 200px;" class="rounded">
                <source src="${URL.createObjectURL(blob)}" type="${blob.type}">
                Your browser does not support video playback.
            </video>
            <div class="mt-2 d-flex justify-content-between align-items-center">
                <small class="text-muted">Duration: ${duration}s | Size: ${this.formatFileSize(blob.size)}</small>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="screenRecorder.downloadRecording()">
                        <i class="fas fa-download me-1"></i>Download
                    </button>
                    <button class="btn btn-success" onclick="screenRecorder.saveToServer()" ${!this.canSave ? 'disabled' : ''}>
                        <i class="fas fa-cloud-upload-alt me-1"></i>Save to Profile
                    </button>
                </div>
            </div>
        `;
        
        // Add to speech panel
        const speechPanel = document.querySelector('.speech-panel');
        if (speechPanel) {
            // Remove existing preview
            const existingPreview = speechPanel.querySelector('.recording-preview');
            if (existingPreview) {
                existingPreview.remove();
            }
            speechPanel.appendChild(previewContainer);
        }
    }
    
    downloadRecording() {
        if (!this.recordingBlob) {
            this.showError('No recording available to download');
            return;
        }
        
        const url = URL.createObjectURL(this.recordingBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `quran-recitation-${Date.now()}.webm`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('Recording downloaded');
    }
    
    async saveToServer() {
        if (!this.recordingBlob || !this.canSave) {
            this.showError('Cannot save recording at this time');
            return;
        }
        
        try {
            this.showLoading('Uploading recording...');
            
            // Create form data
            const formData = new FormData();
            formData.append('recording', this.recordingBlob, `recitation-${Date.now()}.webm`);
            formData.append('duration', this.recordingDuration);
            formData.append('title', this.generateTitle());
            formData.append('recitation_id', window.speechAnalyzer?.currentRecitationId || '');
            
            // Upload to server
            const response = await fetch('../api/upload-screenrecord.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('Recording saved successfully!');
                this.clearRecording();
                
                // Redirect to streaming page to view
                if (confirm('Recording saved! Would you like to view it on the streaming page?')) {
                    window.location.href = 'streaming.php';
                }
            } else {
                throw new Error(result.error || 'Upload failed');
            }
            
        } catch (error) {
            console.error('Error saving recording:', error);
            this.showError('Failed to save recording: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    generateTitle() {
        const now = new Date();
        const date = now.toLocaleDateString();
        const time = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        return `Recitation - ${date} ${time}`;
    }
    
    clearRecording() {
        this.recordingBlob = null;
        this.recordingDuration = 0;
        this.canSave = false;
        
        // Remove preview
        const preview = document.querySelector('.recording-preview');
        if (preview) {
            preview.remove();
        }
        
        this.updateUI();
    }
    
    enableSave() {
        this.canSave = true;
        this.enableSaveButton();
    }
    
    enableSaveButton() {
        const saveButton = document.querySelector('button[onclick="screenRecorder.saveToServer()"]');
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.classList.remove('btn-outline-success');
            saveButton.classList.add('btn-success');
        }
    }
    
    updateUI() {
        const recordButton = document.getElementById('startScreenRecord');
        const statusElement = document.getElementById('recordingStatus');
        
        if (recordButton) {
            if (this.isRecording) {
                recordButton.innerHTML = '<i class="fas fa-stop me-1"></i>Stop Recording';
                recordButton.classList.remove('btn-outline-danger');
                recordButton.classList.add('btn-danger');
            } else {
                recordButton.innerHTML = '<i class="fas fa-record-vinyl me-1"></i>Record Screen';
                recordButton.classList.remove('btn-danger');
                recordButton.classList.add('btn-outline-danger');
            }
        }
        
        if (statusElement) {
            if (this.isRecording) {
                statusElement.innerHTML = '<i class="fas fa-circle text-danger"></i> Recording...';
                statusElement.className = 'text-danger';
            } else if (this.recordingBlob) {
                statusElement.innerHTML = `<i class="fas fa-check text-success"></i> Recorded (${this.recordingDuration}s)`;
                statusElement.className = 'text-success';
            } else {
                statusElement.innerHTML = 'Ready to record';
                statusElement.className = 'text-muted';
            }
        }
    }
    
    getSupportedMimeType() {
        const types = [
            'video/webm;codecs=vp9,opus',
            'video/webm;codecs=vp8,opus',
            'video/webm;codecs=h264,opus',
            'video/webm',
            'video/mp4'
        ];
        
        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        
        return 'video/webm'; // Fallback
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    showLoading(message) {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loadingOverlay';
        loadingDiv.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        loadingDiv.style.cssText = 'background: rgba(0,0,0,0.7); z-index: 2000;';
        loadingDiv.innerHTML = `
            <div class="card text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <h5>${message}</h5>
            </div>
        `;
        document.body.appendChild(loadingDiv);
    }
    
    hideLoading() {
        const loadingDiv = document.getElementById('loadingOverlay');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }
    
    showSuccess(message) {
        this.showAlert(message, 'success');
    }
    
    showError(message) {
        this.showAlert(message, 'danger');
    }
    
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1060; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Global screen recorder instance
let screenRecorder;

// Initialize when DOM is loaded
function initializeScreenRecording() {
    screenRecorder = new ScreenRecorder();
    console.log('Screen recorder initialized');
}

// Export for use in other scripts
window.ScreenRecorder = ScreenRecorder; 