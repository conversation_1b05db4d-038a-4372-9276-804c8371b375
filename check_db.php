<?php
require_once 'config/database.php';

$conn = getConnection();

echo "<h2>Database Tables Check</h2>";

// Check if screen_records table exists
$result = $conn->query("SHOW TABLES LIKE 'screen_records'");
if ($result->num_rows > 0) {
    echo "<h3>screen_records table exists</h3>";
    $desc = $conn->query("DESCRIBE screen_records");
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $desc->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<h3>screen_records table does NOT exist</h3>";
    echo "<p>Creating table...</p>";
    
    $createTable = "CREATE TABLE screen_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        file_path VARCHAR(500) NOT NULL,
        file_size INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )";
    
    if ($conn->query($createTable)) {
        echo "<p>Table created successfully!</p>";
    } else {
        echo "<p>Error creating table: " . $conn->error . "</p>";
    }
}

// Check if mirror_recordings table exists
$result = $conn->query("SHOW TABLES LIKE 'mirror_recordings'");
if ($result->num_rows > 0) {
    echo "<h3>mirror_recordings table exists</h3>";
    $desc = $conn->query("DESCRIBE mirror_recordings");
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $desc->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<h3>mirror_recordings table does NOT exist</h3>";
    echo "<p>Creating table...</p>";
    
    $createTable = "CREATE TABLE mirror_recordings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        file_path VARCHAR(500) NOT NULL,
        file_size INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )";
    
    if ($conn->query($createTable)) {
        echo "<p>Table created successfully!</p>";
    } else {
        echo "<p>Error creating table: " . $conn->error . "</p>";
    }
}

echo "<p><a href='dashboard.php'>Back to Dashboard</a></p>";
?>
